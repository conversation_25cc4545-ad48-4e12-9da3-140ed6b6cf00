﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Configuration;
using System.Windows.Forms;
using Datos.Errores;
using System.IO;

namespace Datos.Generales
{
    public class GeneralesDAL
    {
        public string consultarValorLlave(string sKey)
        {
            string sValorLlave = string.Empty;
            string sRutaConfiguracion = Application.ExecutablePath;

            try
            {
                Configuration config = ConfigurationManager.OpenExeConfiguration(sRutaConfiguracion);
                sValorLlave = config.AppSettings.Settings[sKey].Value.ToString();
                //sValorLlave = ConfigurationManager.AppSettings[sKey].ToString();
            }
            catch(Exception ex)
            {
                GrabarErrorDAL Error = new GrabarErrorDAL();
                Error.grabarError("Datos.Generales", "GeneralesDAL.cs", "consultarValorLlave", "Error ConfigurationManager", 14, ex.Message.ToString());
                throw new Exception(ex.Message.ToString());
            }

            return sValorLlave;
        }

        public void grabarCredenciales(string sKey, string sValue)
        {
            string sRutaConfiguracion = Application.ExecutablePath;
            try
            {
                Configuration config = ConfigurationManager.OpenExeConfiguration(sRutaConfiguracion);
                config.AppSettings.Settings[sKey].Value = sValue;
                config.Save(ConfigurationSaveMode.Modified);
            }
            catch (Exception ex)
            {
                GrabarErrorDAL Error = new GrabarErrorDAL();
                Error.grabarError("Datos.Generales", "GeneralesDAL.cs", "grabarCredenciales", "Error ConfigurationManager", 15, ex.Message.ToString());
                throw new Exception(ex.Message.ToString());
            }
        }

        /// <summary>
        /// Función para sanitizar la ruta del archivo 
        /// </summary>
        /// <param name="rutaArchivo"></param>
        /// <returns></returns>
        public string SanitizarRutaArchivo(string rutaArchivo)
        {
            return Path.GetFileName(rutaArchivo);
        }
    }
}
