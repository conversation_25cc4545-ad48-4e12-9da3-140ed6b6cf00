﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Npgsql;
using NpgsqlTypes;

namespace Datos.Control
{
    public class ControlHilos: Conexion.ConexionDAL   
    {
        NpgsqlCommand cmd = new NpgsqlCommand();
        NpgsqlDataReader lector;

        public int ObtenerNumHilos(int iMovimiento)
        {
            int NumHilos = 1;
            
            try
            {
                conectarBaseDatos(3); //Administracion
                cmd.CommandText = "SELECT iNumhilos FROM fun_obtenerhilostalon(@imovimiento)";
                cmd.Parameters.AddWithValue("@imovimiento", NpgsqlDbType.Integer, iMovimiento);
                cmd.Connection = nConexion;

                lector = cmd.ExecuteReader();

                if (lector.Read())
                {
                    NumHilos = int.Parse(lector["iNumhilos"].ToString());
                }
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message.ToString());
            }
            finally
            {
                cerrarConexion();
            }

            return NumHilos;
        }
    }
}
