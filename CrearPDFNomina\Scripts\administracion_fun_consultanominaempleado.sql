-- Function: fun_consultanominaempleado(integer, date, integer)

-- DROP FUNCTION fun_consultanominaempleado(integer, date, integer);

CREATE OR REPLACE FUNCTION fun_consultanominaempleado(integer, date, integer)
  RETURNS SETOF type_consultanominaempleadointranet AS
$BODY$
DECLARE
	/*
		NOMBRE: <PERSON><PERSON> Atondo 96294418
		BD: Administracion PostgreSQL
		FECHA: 17/04/2017
		SERVIDOR PRUEBAS: ************
		SERVIDOR PRODUCCION: **********
		DESCRIPCION: FUNCION EN LA CUAL SE OBTIENEN LOS REGISTROS DEL FONDO DE LOS EMPLEADOS EN BASE A LA TABLA DE MOVIMIENTOS DE NOMINA DE LA QUINCENA
		MODULO: NO0069, COMPROBANTE.PHP
		RUTA: svn://************/sysx/administracion/contabilidad/nominacoppel/NO0069/CrearPDFNomina/Scripts
	*/
   
	iNumemp			ALIAS FOR $1;
	dFechaNomina		ALIAS FOR $2;
	iOpcion			ALIAS FOR $3; --1 Intranet, 2 Envio Correo
	sSql 			VARCHAR;
	rEmpleado 		type_consultanominaempleadointranet;
BEGIN 
	--Tabla principal
	CREATE TEMPORARY TABLE tmpNominaEmpleados
	(
	    iempleado INTEGER NOT NULL DEFAULT 0,
	    capellidopaterno CHARACTER VARYING(50) NOT NULL DEFAULT '',
	    capellidomaterno CHARACTER VARYING(50) NOT NULL DEFAULT '',
	    cnombre CHARACTER VARYING(50) NOT NULL DEFAULT '',
	    icentro INTEGER NOT NULL DEFAULT 0,
	    cdesccentro CHARACTER VARYING(50) NOT NULL DEFAULT '',
	    inumciudad INTEGER NOT NULL DEFAULT 0,
	    cdescciudad CHARACTER VARYING(50) NOT NULL DEFAULT '',
	    cnumafiliacion CHARACTER VARYING(11) NOT NULL DEFAULT '',
	    ccurp CHARACTER VARYING(20) NOT NULL DEFAULT '',
	    crfc CHARACTER VARYING(13) NOT NULL DEFAULT '',
	    dfechafinal DATE NOT NULL DEFAULT '19000101',
	    dfechainicio DATE NOT NULL DEFAULT '19000101',
	    ctarjetabanco CHARACTER VARYING(20) NOT NULL DEFAULT '',
	    cdescrutapago CHARACTER VARYING(25) NOT NULL DEFAULT '',
	    fechaalta DATE NOT NULL DEFAULT '19000101',
	    numeroseguros INTEGER NOT NULL DEFAULT 0,
	    importeseguros INTEGER NOT NULL DEFAULT 0,
	    ctarjetadespensa CHARACTER VARYING(16) NOT NULL DEFAULT '',
	    mtotaldespensa TEXT NOT NULL DEFAULT '',
	    msueldomensual TEXT NOT NULL DEFAULT '',
	    mtotalapagar TEXT NOT NULL DEFAULT '',
	    mtotalingresos TEXT NOT NULL DEFAULT '',
	    mtotalegresos TEXT NOT NULL DEFAULT '',
	    inumeroempresa INTEGER NOT NULL DEFAULT 0,
	    cnombreempresa CHARACTER VARYING(50) NOT NULL DEFAULT '',
	    ccorreoempleado CHARACTER VARYING(100) NOT NULL DEFAULT ''
	)ON COMMIT DROP;


	CREATE UNIQUE INDEX indx_tmpNominaEmpleados
	ON tmpNominaEmpleados
	USING btree
	(iempleado,inumeroempresa);
	
	--Tabla para los centros
	CREATE TEMPORARY TABLE tmpCentrosEmpleados
	(
	    icentro INTEGER NOT NULL DEFAULT 0,
	    iciudad INTEGER NOT NULL DEFAULT 0,
	    iregion INTEGER NOT NULL DEFAULT 0
	)ON COMMIT DROP;

	CREATE UNIQUE INDEX indx_tmpCentrosEmpleados
	ON tmpCentrosEmpleados
	USING btree
	(icentro);

	INSERT INTO tmpCentrosEmpleados 
	SELECT a.idu_centro, a.idu_ciudad, b.idu_region
	FROM cat_Centros_gx a JOIN cat_ciudades_gx b 
	ON a.idu_ciudad = b.idu_ciudad
	WHERE b.idu_region = 1;

	sSql := 'INSERT INTO tmpNominaEmpleados SELECT a.numemp,rtrim(a.apellidopaterno) AS apellidopaterno, RTRIM(a.apellidomaterno) AS apellidomaterno, RTRIM(a.nombre) AS nombre, a.centro, 
			  RTRIM(a.descripcioncentro) AS descripcioncentro, a.numerociudad, RTRIM(a.descripcionciudad) AS descripcionciudad, 
			  a.numeroafiliacion, a.curp, a.rfc,a.fechafinal, a.fechainicio,a.tarjetabanco, a.descripcionrutapago, a.fechaalta, a.numeroseguros, a.importeseguro,  a.tarjetadespensa, 
			  TO_CHAR(a.importedespensa/100.00,''LFM9,999,999.00'') AS mTotalDespensa,
			  TO_CHAR(a.sueldomensual/100.00,''LFM9,999,999.00'') AS mSueldoMensual,
			  TO_CHAR(a.totalapagar/100.00,''LFM9,999,999.00'') AS mTotalAPagar, 
			  TO_CHAR(a.totalingresos/100.00,''LFM9,999,999.00'')  AS mTotalIngresos,
			  TO_CHAR(a.totalegresos/100.00,''LFM9,999,999.00'') AS mTotalEgresos,
			  a.empresa, RTRIM(a.descripcionempresa) AS descripcionempresa, '' '' AS des_correo
			  FROM saprecibosnominasgrupo a ';			 

	IF iOpcion = 2 THEN
		sSql := sSql||'JOIN cat_emailempleados b ON a.numemp = b.num_empleado ';
	END IF;

	--PARA OBTENER UNA REGION EN ESPECIFICO. EJEMPLO 1 - CULIACAN
	/*IF iOpcion = 2 THEN
		sSql := sSql||'JOIN tmpCentrosEmpleados c ON a.centro = c.icentro ';
	END IF;*/

	sSql := sSql||'WHERE a.fechanomina = DATE('''||dFechaNomina||''') AND a.totalapagar > 0 ';

	IF iNumEmp != 0 THEN
		sSql := sSql||'AND a.numemp = '||iNumEmp||' ';
	END IF;

	sSql := sSql||'ORDER BY a.numemp ASC, a.empresa';
	
	EXECUTE sSql;

	
	IF iOpcion = 2 THEN
		UPDATE tmpNominaEmpleados 
		SET ccorreoempleado = b.des_correo
		FROM cat_emailempleados b
		WHERE b.num_empleado = tmpNominaEmpleados.iempleado;
	END IF;

	FOR rEmpleado IN SELECT iempleado, capellidopaterno, capellidomaterno, cnombre, icentro, cdesccentro, inumciudad, cdescciudad, 
			cnumafiliacion, ccurp, crfc, dfechafinal, dfechainicio, ctarjetabanco, cdescrutapago, fechaalta, numeroseguros, 
			importeseguros, ctarjetadespensa, mtotaldespensa, msueldomensual, mtotalapagar, mtotalingresos, mtotalegresos, 
			inumeroempresa, cnombreempresa, ccorreoempleado 
			FROM tmpNominaEmpleados
		LOOP RETURN NEXT rEmpleado;
	END LOOP;

END;
 $BODY$
  LANGUAGE plpgsql VOLATILE SECURITY DEFINER;
GRANT EXECUTE ON FUNCTION fun_consultanominaempleado(integer, date, integer) TO sysgenexus;