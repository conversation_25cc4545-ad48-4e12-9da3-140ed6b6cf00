﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Datos.Conexion;

namespace Negocios.Conexion
{
    public class ConfigurarBO
    {
        public bool configurarConexion()
        {
            bool bContinuar = false; 

            ConfigurarDAL Configurar = new ConfigurarDAL(); 
            try
            {
                bContinuar = Configurar.grabarConfiguracion();

                return bContinuar;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message.ToString());
            }
        }
    }
}
