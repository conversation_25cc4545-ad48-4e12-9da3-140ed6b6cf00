## YAML Template.
---
stages:
  - checkmarxSAST
  - checkmarxSCA
workflow:
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_SOURCE_BRANCH_NAME != "master"
    - if: $CI_COMMIT_BRANCH && $CI_O<PERSON>EN_MERGE_REQUESTS
      when: never
    - if: $CI_COMMIT_TAG
    - if: $CI_COMMIT_BRANCH == "master"
    - if: $CI_COMMIT_BRANCH =~ /\d{1,6}_\d{6}_(\w*Desarrollo\w*)/
    - if: $CI_COMMIT_BRANCH =~ /\d{1,6}_(\w*GAMANSOLUTIONS\w*)/
    - if: $CI_COMMIT_BRANCH =~ /(\w*Desarrollo\w*)/
    - if: $CI_COMMIT_BRANCH =~ /\d{1,6}.\d{1}_\d{6}(\w*Desarrollo\w*)/

    
##sonar:
##  image: harbor.coppel.io/library/sonar-scanner:4.6
##  stage: sonarQube
##  tags:
##    - docker
##  script:
##    - export NODE_PATH=$NODE_PATH:`npm root -g`
##    - sonar-scanner -Dsonar.host.url=$CPL_SONARQUBE_AFORE
##  only:
##    refs:
##      - merge_requests
##    variables:
##      - $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "master"
##      - $CI_MERGE_REQUEST_TARGET_BRANCH_NAME =~ /\d{1,6}_\d{6}_(\w*Desarrollo\w*)/
##      - $CI_MERGE_REQUEST_TARGET_BRANCH_NAME =~ /\d{1,6}.\d{1}_\d{6}(\w*Desarrollo\w*)/
##      - $CI_MERGE_REQUEST_TARGET_BRANCH_NAME =~ /\d{1,6}._(\w*GAMANSOLUTIONS\w*)/
##      - $CI_MERGE_REQUEST_TARGET_BRANCH_NAME =~ /d-[a-zA-Z0-9.]{1,8}[-_]{1}[a-zA-Z0-9-_]{1,10}/
##  except:
##    - branches

include:
  - 'checkmarx.gitlab-ci.yml'
  - 'sca.gitlab-ci.yml'
