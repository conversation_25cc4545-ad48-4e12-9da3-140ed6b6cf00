-- Function: fun_obtiene_correos_empleados(integer)

-- DROP FUNCTION fun_obtiene_correos_empleados(integer);

CREATE OR REPLACE FUNCTION fun_obtiene_correos_empleados(integer)
  RETURNS SETOF type_obtiene_correos_empleados AS
$BODY$
DECLARE
	/*
		NOMBRE: <PERSON><PERSON> Atondo 96294418
		BD: Administracion PostgreSQL
		FECHA: 17/04/2017
		SERVIDOR PRUEBAS: ************
		SERVIDOR PRODUCCION: **********
		DESCRIPCION: FUNCION EN LA CUAL SE OBTIENEN LOS REGISTROS DE GASOLINA DE LOS DIRECTIVOS EN BASE A LA TABLA DE GASOLINA HISTORICA, LA CUAL CONTIENE LOS MOVIMIENTOS GENERADOS EN LA QUINCENA
		MODULO: NO0069, COMPROBANTE.PHP
		RUTA: svn://************/sysx/administracion/contabilidad/nominacoppel/NO0069/CrearPDFNomina/Scripts
	*/
	iOpcion ALIAS FOR $1; --1 Intranet, 2 Envio Correo
	dFecha DATE;
	sSql VARCHAR;
	rRegistros type_obtiene_correos_empleados;
BEGIN
	--Tabla principal
	CREATE TEMPORARY TABLE tmpgasolina
	(
	    iempleado INTEGER NOT NULL DEFAULT 0,
	    ccorreoempleado CHARACTER VARYING(100) NOT NULL DEFAULT ''
	)ON COMMIT DROP;


	CREATE UNIQUE INDEX indx_tmpgasolina
	ON tmpgasolina
	USING btree
	(iempleado);

	--Tabla para los centros
	CREATE TEMPORARY TABLE tmpCentrosEmpleados
	(
	    icentro INTEGER NOT NULL DEFAULT 0,
	    iciudad INTEGER NOT NULL DEFAULT 0,
	    iregion INTEGER NOT NULL DEFAULT 0
	)ON COMMIT DROP;

	CREATE UNIQUE INDEX indx_tmpCentrosEmpleados
	ON tmpCentrosEmpleados
	USING btree
	(icentro);

	dFecha := (SELECT MAX(fechacorte) FROM gasdirectivosgasolinahistorico);

	INSERT INTO tmpCentrosEmpleados 
	SELECT a.idu_centro, a.idu_ciudad, b.idu_region
	FROM cat_Centros_gx a JOIN cat_ciudades_gx b 
	ON a.idu_ciudad = b.idu_ciudad
	WHERE b.idu_region = 1;

	INSERT INTO tmpgasolina SELECT a.num_empleado, CASE WHEN COALESCE(b.empleado,0) = 0 THEN '' ELSE RTRIM(a.des_correo) END AS des_correo
	FROM cat_emailempleados a
	LEFT JOIN gasdirectivosgasolinahistorico b ON a.num_empleado = b.empleado AND b.fechacorte = DATE(dFecha)
	WHERE COALESCE(b.empleado,0) != 0 GROUP BY a.num_empleado, b.empleado, a.des_correo ORDER BY a.num_empleado;
		
	/*IF iOpcion = 2 THEN 
		DELETE FROM tmpgasolina 
		USING tmpgasolina AS a
		LEFT JOIN tmpCentrosEmpleados AS b ON a.centro = b.icentro 
		WHERE tmpgasolina.centro = a.centro AND COALESCE(b.icentro,0) = 0;
	END IF;*/

	FOR rRegistros IN SELECT iempleado, ccorreoempleado
			  FROM tmpgasolina 
	LOOP
		RETURN NEXT rRegistros;
	END LOOP;
END;
$BODY$
  LANGUAGE plpgsql VOLATILE SECURITY DEFINER;