﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Npgsql;
using NpgsqlTypes;
using System.Configuration;
using System.Data.SqlClient;
using Datos.Generales;
using System.IO;

namespace Datos.Conexion
{
    public class ConexionDAL
    {
        public NpgsqlConnection nConexion = new NpgsqlConnection();
        private string sDB = string.Empty;
        private string sUser = string.Empty;
        private string sclave = string.Empty;
        public string sServer = string.Empty;
        private string sPuerto = string.Empty;

        public int conectarBaseDatos(int iBaseDeDatos)
        {
            sPuerto = "5432";

            switch (iBaseDeDatos)
            {
                case 1: //BD nominacontabilidad
                    sServer = Entidades.Conexion.Conexion.sServidorContabilidad;
                    sDB = Entidades.Conexion.Conexion.sBDContabilidad;
                    sUser = Entidades.Conexion.Conexion.sUsuarioContabilidad;
                    sclave = Entidades.Conexion.Conexion.sClaveContabilidad;
                    break;
                case 2: //BD personal 
                    sServer = Entidades.Conexion.Conexion.sServidorPersonal;
                    sDB = Entidades.Conexion.Conexion.sBDPersonal;
                    sUser = Entidades.Conexion.Conexion.sUsuarioPersonal;
                    sclave = Entidades.Conexion.Conexion.sClavePersonal;
                    break;
                case 3: //BD Administracion
                    sServer = Entidades.Conexion.Conexion.sServidorAdministracion;
                    sDB = Entidades.Conexion.Conexion.sBDAdministracion;
                    sUser = Entidades.Conexion.Conexion.sUsuarioAdministracion;
                    sclave = Entidades.Conexion.Conexion.sClavedministracion;
                    break;
                default:
                    //Conexion default a la BD nominacontabilidad
                    sServer = Entidades.Conexion.Conexion.sServidorContabilidad;
                    sDB = Entidades.Conexion.Conexion.sBDContabilidad;
                    sUser = Entidades.Conexion.Conexion.sUsuarioContabilidad;
                    sclave = Entidades.Conexion.Conexion.sClaveContabilidad;
                    break;
            }

            NpgsqlConnectionStringBuilder builder = new NpgsqlConnectionStringBuilder();
            builder.Host = sServer;
            builder.Port = int.Parse(sPuerto);
            builder.Database = sDB;
            builder.UserName = sUser;
            builder.Password = sclave;
            builder.Pooling = false;

            int iStatusConexion = 0;
            try
            {
                nConexion.ConnectionString = builder.ToString(); 
                nConexion.Open();

                if (nConexion.State == System.Data.ConnectionState.Open)
                {
                    iStatusConexion = 1;
                }
                else
                {
                    iStatusConexion = 0;
                }
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message.ToString());
            }

            return iStatusConexion;
        }

        public void cerrarConexion()
        {
            try
            {
                if (nConexion.State == System.Data.ConnectionState.Open)
                {
                    nConexion.Close();
                }
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message.ToString());
            }
        }

        public string RutaWS()
        {
            string iIp = string.Empty;
            string sRuta = string.Empty;

            iIp = Entidades.Conexion.Conexion.sServidorWebService;
            sRuta = Entidades.Conexion.Conexion.sDescripcionWebService;

            return sRuta;
        }
    }
}
