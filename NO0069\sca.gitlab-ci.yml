#
# Include this file in your .gitlab-ci.yml file to automate & integrate Checkmarx security scans.

variables:
    CX_FLOW_EXE_SCA: "java -Dhttp.proxyHost=************ -Dhttp.proxyPort=8080 -jar /app/cx-flow.jar --trust-cert"
    SCA_PROJECT_NAME: "$CI_PROJECT_NAME-$CI_COMMIT_REF_NAME"


checkmarx-scan-mr-sca:
  stage: checkmarxSC<PERSON>
  needs: []
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event" && 
            ($CI_MERGE_REQUEST_TARGET_BRANCH_NAME =~ /\d{1,6}_\d{6}_(\w*Desarrollo\w*)/ || 
             $CI_MERGE_REQUEST_TARGET_BRANCH_NAME =~ /\d{1,6}_(\w*GAMANSOLUTIONS\w*)/ || 
             $CI_MERGE_REQUEST_TARGET_BRANCH_NAME =~ /(\w*develop\w*)/ ||
             $CI_MERGE_REQUEST_TARGET_BRANCH_NAME =~ /(\w*master\w*)/) &&
             $CI_MERGE_REQUEST_SOURCE_BRANCH_NAME !~ /(\w*master\w*)/'
  image:   
    name: ${CHECKMARX_DOCKER_IMAGE}
    entrypoint: ['']
  variables:
    CHECKMARX_INCREMENTAL: "true"
  script:
    - cat ${CX_FLOW_CONFIG_SCA} > application.yml
    - ${CX_FLOW_EXE_SCA}
          --scan 
          --app="${CI_PROJECT_NAME}" 
          --namespace="${CI_PROJECT_NAMESPACE}" 
          --repo-name="${CI_PROJECT_NAME}" 
          --repo-url="${CI_REPOSITORY_URL}" 
          --cx-team="${SCA_TEAM}" 
          --cx-project="${SCA_PROJECT_NAME}" 
          --branch="${CI_MERGE_REQUEST_SOURCE_BRANCH_NAME}"
          --spring.profiles.active="${CX_FLOW_ENABLED_VULNERABILITY_SCANNERS}" 
          --f=.
          ${PARAMS}
  tags:
    - omnicanal


checkmarx-scan-master-sca:
  stage: checkmarxSCA
  needs: []
  rules:
    - if: '$CI_PIPELINE_SOURCE == "push" && $CI_COMMIT_BRANCH == "master"'
  image:   
    name: ${CHECKMARX_DOCKER_IMAGE}
    entrypoint: ['']
  variables:
    CHECKMARX_INCREMENTAL: "true"
  script:
    - cat ${CX_FLOW_CONFIG_SCA} > application.yml
    - ${CX_FLOW_EXE_SCA}
          --scan 
          --app="${CI_PROJECT_NAME}" 
          --namespace="${CI_PROJECT_NAMESPACE}" 
          --repo-name="${CI_PROJECT_NAME}" 
          --repo-url="${CI_REPOSITORY_URL}" 
          --cx-team="${SCA_TEAM}" 
          --cx-project="${CI_PROJECT_NAME}" 
          --branch="${CI_COMMIT_BRANCH}"
          --spring.profiles.active="${CX_FLOW_ENABLED_VULNERABILITY_SCANNERS}" 
          --f=.
          ${PARAMS}
  tags:
    - omnicanal


checkmarx-scan-commit-sca:
  stage: checkmarxSCA
  needs: []
  rules:
    - if: '$CI_PIPELINE_SOURCE == "push" && $CI_COMMIT_BRANCH != "master" && $CX_COMMIT_SCAN =~ /(?i)(\w*true\w*)/'
  image:   
    name: ${CHECKMARX_DOCKER_IMAGE}
    entrypoint: ['']
  variables:
    CHECKMARX_INCREMENTAL: "true"
  script:
    - cat ${CX_FLOW_CONFIG_SCA} > application.yml
    - ${CX_FLOW_EXE_SCA}
          --scan 
          --app="${CI_PROJECT_NAME}" 
          --namespace="${CI_PROJECT_NAMESPACE}" 
          --repo-name="${CI_PROJECT_NAME}" 
          --repo-url="${CI_REPOSITORY_URL}" 
          --cx-team="${SCA_TEAM}" 
          --cx-project="${SCA_PROJECT_NAME}" 
          --branch="${CI_COMMIT_BRANCH}"
          --spring.profiles.active="${CX_FLOW_ENABLED_VULNERABILITY_SCANNERS}" 
          --f=.
          ${PARAMS}
  tags:
    - omnicanal
