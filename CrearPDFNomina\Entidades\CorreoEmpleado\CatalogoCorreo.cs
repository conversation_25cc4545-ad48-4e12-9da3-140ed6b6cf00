﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Entidades.CorreoEmpleado
{
    public class CatalogoCorreo
    {
        private int _iNumEmpleado;
        private string _sCorreoEmpleado;
        private DateTime _dFechaCaptura;

        public CatalogoCorreo()
        {
            _iNumEmpleado = 0;
            _sCorreoEmpleado = string.Empty;
            _dFechaCaptura = Convert.ToDateTime("1900-01-01");
        }

        public int iNumEmpleado
        {
            get
            {
                return _iNumEmpleado;
            }
            set
            {
                _iNumEmpleado = value;
            }
        }

        public string sCorreoEmpleado
        {
            get
            {
                return _sCorreoEmpleado;
            }
            set
            {
                _sCorreoEmpleado = value;
            }
        }

        public DateTime dFechaCaptura
        {
            get
            {
                return _dFechaCaptura;
            }
            set
            {
                _dFechaCaptura = value;
            }
        }
    }
}
