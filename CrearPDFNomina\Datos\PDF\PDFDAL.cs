﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.IO;
using Npgsql;
using NpgsqlTypes;
using Datos.Errores;
using Entidades.PDFNomina;
using Datos.Generales;

namespace Datos.PDF
{
    public class PDFDAL : Conexion.ConexionDAL
    {
        public void DecodificarPDF(int iNumeroEmpleado, string sNombrePDF) //Metodo extra para obtener el PDF de la BD ya decodificado.
        {
            NpgsqlCommand cmd = new NpgsqlCommand();
            NpgsqlDataReader lector;
            cmd.Connection = nConexion;
            byte[] bPDF = null;
            DateTime dFechaNomina = Convert.ToDateTime("1900-01-01");
            string sImagenTemporal = "C:\\" + sNombrePDF + ".pdf";  //Nombre del archivo y su extencion
            
            try
            {
                cmd.CommandText = "SELECT fec_nomina,des_archivopdf FROM mov_talonesempleadosmail where num_empleado = @iNumEmp;";
                cmd.Parameters.AddWithValue("@iNumEmp", NpgsqlDbType.Integer, iNumeroEmpleado);

                conectarBaseDatos(3);
                lector = cmd.ExecuteReader();

                if (lector.Read())
                {
                    dFechaNomina = Convert.ToDateTime(lector["fec_nomina"]);
                    bPDF = (byte[])lector["des_archivopdf"];
                }

                File.WriteAllBytes(sImagenTemporal, bPDF); //Crear archivo PDF en el disco local
            }
            catch (Exception ex)
            {
                GrabarErrorDAL Error = new GrabarErrorDAL();
                Error.grabarError("Datos.PDF", "PDFDAL.cs", "DecodificarPDF", "Error en la tabla mov_talonesempleadosmail al DecodificarPDF", 15, ex.Message.ToString());
                throw new Exception(ex.Message.ToString());
            }
            finally
            {
                cerrarConexion();               
                bPDF = null;               
            }
        }

        public PDFNominaConfiguracion ObtenerPDFNominaConfiguracion(int iNumEmpresa)
        {
            PDFNominaConfiguracion pdfNominaConfiguracion = null;
            NpgsqlCommand cmd = new NpgsqlCommand();
            GeneralesDAL generalesDAL = new GeneralesDAL();
            NpgsqlDataReader lector;
            cmd.Connection = nConexion;

            try
            {
                cmd.CommandText = "SELECT num_empresa, des_nombre_archivo, num_ancho, num_alto, des_autor FROM fun_obtener_pdfnomina_configuracion(@iNumEmpresa);";
                cmd.Parameters.AddWithValue("@iNumEmpresa", NpgsqlDbType.Integer, iNumEmpresa);

                conectarBaseDatos(3);
                lector = cmd.ExecuteReader();

                if (lector.Read())
                {
                    pdfNominaConfiguracion = new PDFNominaConfiguracion();
                    pdfNominaConfiguracion.iNumEmpresa = Convert.ToInt32(lector["num_empresa"]);
                    pdfNominaConfiguracion.sNombreArchivo = generalesDAL.SanitizarRutaArchivo(Convert.ToString(lector["des_nombre_archivo"]));
                    pdfNominaConfiguracion.iAncho = Convert.ToInt32(lector["num_ancho"]);
                    pdfNominaConfiguracion.iAlto = Convert.ToInt32(lector["num_alto"]);
                    pdfNominaConfiguracion.sAutor = Convert.ToString(lector["des_autor"]);
                }
            }
            catch (Exception)
            {
                throw;
            }
            finally
            {
                cerrarConexion();
            }

            return pdfNominaConfiguracion;
        }
    }
      
}
