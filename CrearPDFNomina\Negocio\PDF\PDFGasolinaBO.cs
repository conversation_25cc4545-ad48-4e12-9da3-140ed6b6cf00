﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Configuration;
using System.IO;
using iTextSharp.text;
using iTextSharp.text.pdf;
using Entidades.Gasolina;
using System.Windows.Forms;
using System.Globalization;

namespace Negocios.PDF
{
    public class PDFGasolinaBO
    {
        public void construirPDFGasolina(DateTime dFechaCorte, int iNumEmp, List<EstadoDetalleGasolina> listaDetallexEmpleado)
        {
            Document document = new Document(PageSize.LETTER); //Tamaño de Hoja LETTER = CARTA
            string sRutaCarpeta = @"C:\PDF\"; //Se obtiene la ruta donde se generara el PDF
            bool bExisteCarpeta = Directory.Exists(sRutaCarpeta);
            string sRutaCompleta = "" + sRutaCarpeta + "" + iNumEmp.ToString() + "_" + dFechaCorte.ToString("yyyy-MM-dd") + ".pdf";
            string sAutor = string.Empty;
            int iUnidad = 0;
            float[] anchocolumnatitulo = new float[3] { 10f, 30f, 30f }; //Se genera variable para ancho de las columnas
            float[] anchoDeColumnas = new float[6] { 15f, 30f, 10f, 10f, 10f, 10f };

            iTextSharp.text.Font fontTitle = FontFactory.GetFont("Arial", 8);
            iTextSharp.text.Font fontText = FontFactory.GetFont("Arial", 7);

            if (bExisteCarpeta == false)
            {
                Directory.CreateDirectory(sRutaCarpeta);
            }

            PdfWriter.GetInstance(document, new FileStream(sRutaCompleta, FileMode.Create));

            //Configuracion del PDF 
            document.AddTitle("Vales de Gasolina");
            document.AddAuthor(sAutor);
            document.Open();

            Paragraph paragraph = new Paragraph();
            paragraph.Clear();
            paragraph.Alignment = Element.ALIGN_LEFT;
            paragraph.SetLeading(0.0f, 0.2f);
            paragraph.Font = fontText;
            paragraph.Add("\n");
            document.Add(paragraph);

            //Generacion del Detalle Titulos
            PdfPTable TablaTitulos = new PdfPTable(3);
            TablaTitulos.SetWidths(anchocolumnatitulo);
            PdfPTable tablaDetalle = new PdfPTable(6);
            tablaDetalle.SetWidths(anchoDeColumnas);

            //CONFIRGURACION DE CELDAS
            ConfigurarCeldasPDF ConfCelda = new ConfigurarCeldasPDF();

            PdfPCell cellEspacio = new PdfPCell(new Phrase("\n", fontText));
            cellEspacio.Colspan = 3;
            ConfCelda.PropiedadesCeldadas(cellEspacio, 1, 1, 1, 0, 10, 10, 1);
            TablaTitulos.AddCell(cellEspacio);
                        
            //Datos del colaborador
            PdfPCell CeldaNumEmp = new PdfPCell(new Phrase(String.Format("{0:G}", iNumEmp), fontTitle));
            ConfCelda.PropiedadesCeldadas(CeldaNumEmp, 0, 1, 0, 0, 10, 0, 1);
            TablaTitulos.AddCell(CeldaNumEmp);

            PdfPCell CeldaNombre = new PdfPCell(new Phrase(String.Format("{0:G}", listaDetallexEmpleado[0].sApellidoPaterno.Trim() + " " + listaDetallexEmpleado[0].sApellidoMaterno.Trim() + " " + listaDetallexEmpleado[0].sNombre.Trim()), fontTitle));
            ConfCelda.PropiedadesCeldadas(CeldaNombre, 0, 0, 0, 0, 0, 0, 1);
            TablaTitulos.AddCell(CeldaNombre);

            PdfPCell CeldaCentro = new PdfPCell(new Phrase(String.Format("{0:G}", listaDetallexEmpleado[0].sDescripcionCentro), fontTitle));
            ConfCelda.PropiedadesCeldadas(CeldaCentro, 0, 0, 1, 0, 0, 10, 1);
            TablaTitulos.AddCell(CeldaCentro);


            //Next
            PdfPCell CeldaDetalle = new PdfPCell(new Phrase("Consumo de Gasolina ", fontTitle));
            CeldaDetalle.Colspan = 2;
            ConfCelda.PropiedadesCeldadas(CeldaDetalle, 0, 1, 0, 0, 10, 0, 1);
            TablaTitulos.AddCell(CeldaDetalle);

            PdfPCell CeldaCorte = new PdfPCell(new Phrase(String.Format("{0:N}", "Correspondiente a: " + dFechaCorte.ToString("MM/yyyy")), fontTitle));
            ConfCelda.PropiedadesCeldadas(CeldaCorte, 0, 0, 1, 0, 0, 10, 1);
            TablaTitulos.AddCell(CeldaCorte);

            document.Add(TablaTitulos); //Finaliza el detalle de la tabla de titulos
            
            //Titulos de Celdas
            PdfPCell CeldaVale = new PdfPCell(new Phrase("VALE ", fontTitle));
            ConfCelda.PropiedadesCeldadas(CeldaVale, 0, 1, 0, 0, 10, 0, 1);
            tablaDetalle.AddCell(CeldaVale);

            PdfPCell CeldaProveedor = new PdfPCell(new Phrase("PROVEEDOR ", fontTitle));
            ConfCelda.PropiedadesCeldadas(CeldaProveedor, 0, 0, 0, 0, 0, 0, 1);
            tablaDetalle.AddCell(CeldaProveedor);

            PdfPCell CeldaFactura = new PdfPCell(new Phrase("FACTURA ", fontTitle));
            ConfCelda.PropiedadesCeldadas(CeldaFactura, 0, 0, 0, 0, 0, 0, 1);
            tablaDetalle.AddCell(CeldaFactura);

            PdfPCell CeldaCD = new PdfPCell(new Phrase("CD ", fontTitle));
            ConfCelda.PropiedadesCeldadas(CeldaCD, 0, 0, 0, 0, 0, 0, 1);
            tablaDetalle.AddCell(CeldaCD);

            PdfPCell CeldaFecha = new PdfPCell(new Phrase("FECHA ", fontTitle));
            ConfCelda.PropiedadesCeldadas(CeldaFecha, 0, 0, 0, 0, 0, 0, 1);
            tablaDetalle.AddCell(CeldaFecha);

            PdfPCell CeldaImporte = new PdfPCell(new Phrase("IMPORTE ", fontTitle));
            ConfCelda.PropiedadesCeldadas(CeldaImporte, 0, 0, 1, 0, 0, 10, 1);
            tablaDetalle.AddCell(CeldaImporte);

            document.Add(tablaDetalle);

            tablaDetalle.DeleteBodyRows(); //Eliminar informacion

            for (int i = 0; i < listaDetallexEmpleado.LongCount(); i++)
            {
                if(iUnidad != listaDetallexEmpleado[i].iUnidad)
                {
                    PdfPCell celda = new PdfPCell(new Phrase(String.Format("{0:N}", "Unidad: " + listaDetallexEmpleado[i].iUnidad), fontText));
                    celda.Colspan = 6;
                    ConfCelda.PropiedadesCeldadas(celda, 0, 1, 1, 0, 10, 0, 1);
                    tablaDetalle.AddCell(celda);
                }

                //Se agrega movimiento
                PdfPCell CeldaVale1 = new PdfPCell(new Phrase(String.Format("{0:G}", listaDetallexEmpleado[i].iVale), fontText));
                ConfCelda.PropiedadesCeldadas(CeldaVale1, 0, 1, 0, 0, 10, 0, 1);
                tablaDetalle.AddCell(CeldaVale1);

                PdfPCell CeldaProveedor1 = new PdfPCell(new Phrase(String.Format("{0:G}", listaDetallexEmpleado[i].iProveedor + " " + listaDetallexEmpleado[i].sNombreProveedor), fontText));
                ConfCelda.PropiedadesCeldadas(CeldaProveedor1, 0, 0, 0, 0, 0, 0, 1);
                tablaDetalle.AddCell(CeldaProveedor1);

                PdfPCell CeldaFactura1 = new PdfPCell(new Phrase(String.Format("{0:G}", listaDetallexEmpleado[i].sFactura), fontText));
                ConfCelda.PropiedadesCeldadas(CeldaFactura1, 0, 0, 0, 0, 0, 0, 1);
                tablaDetalle.AddCell(CeldaFactura1);

                PdfPCell CeldaCD1 = new PdfPCell(new Phrase(String.Format("{0:G}", listaDetallexEmpleado[i].sCiudad), fontText));
                ConfCelda.PropiedadesCeldadas(CeldaCD1, 0, 0, 0, 0, 0, 0, 1);
                tablaDetalle.AddCell(CeldaCD1);

                PdfPCell CeldaFecha1 = new PdfPCell(new Phrase(String.Format("{0:G}", listaDetallexEmpleado[i].dFechaVale.ToString("yyyy-MM-dd")), fontText));
                ConfCelda.PropiedadesCeldadas(CeldaFecha1, 0, 0, 0, 0, 0, 0, 1);
                tablaDetalle.AddCell(CeldaFecha1);

                PdfPCell CeldaImporte1 = new PdfPCell(new Phrase(String.Format("{0:C}", listaDetallexEmpleado[i].iImporte), fontText));
                ConfCelda.PropiedadesCeldadas(CeldaImporte1, 0, 0, 1, 0, 0, 10, 2);
                tablaDetalle.AddCell(CeldaImporte1);

                iUnidad = listaDetallexEmpleado[i].iUnidad;
            }
            //Total
            PdfPCell cell1 = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
            ConfCelda.PropiedadesCeldadas(cell1, 0, 1, 0, 0, 10, 0, 1);
            tablaDetalle.AddCell(cell1);

            PdfPCell cell2 = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
            ConfCelda.PropiedadesCeldadas(cell2, 0, 0, 0, 0, 0, 0, 1);
            tablaDetalle.AddCell(cell2);

            PdfPCell cell3 = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
            ConfCelda.PropiedadesCeldadas(cell3, 0, 0, 0, 0, 0, 0, 1);
            tablaDetalle.AddCell(cell3);

            PdfPCell cell4 = new PdfPCell(new Phrase(" ", fontText));
            ConfCelda.PropiedadesCeldadas(cell4, 0, 0, 0, 0, 0, 0, 1);
            tablaDetalle.AddCell(cell4);

            PdfPCell cell5 = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
            ConfCelda.PropiedadesCeldadas(cell5, 0, 0, 0, 0, 0, 0, 1);
            tablaDetalle.AddCell(cell5);

            PdfPCell cellTotal = new PdfPCell(new Phrase(String.Format("{0:C}", listaDetallexEmpleado[0].iTotal), fontText));
            ConfCelda.PropiedadesCeldadas(cellTotal, 0, 0, 1, 0, 0, 10, 2);
            tablaDetalle.AddCell(cellTotal);


            //Factor
            PdfPCell cell12 = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
            ConfCelda.PropiedadesCeldadas(cell12, 0, 1, 0, 0, 10, 0, 1);
            tablaDetalle.AddCell(cell12);

            PdfPCell cell22 = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
            ConfCelda.PropiedadesCeldadas(cell22, 0, 0, 0, 0, 0, 0, 1);
            tablaDetalle.AddCell(cell22);

            PdfPCell cell32 = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
            ConfCelda.PropiedadesCeldadas(cell32, 0, 0, 0, 0, 0, 0, 1);
            tablaDetalle.AddCell(cell32);

            PdfPCell cell42 = new PdfPCell(new Phrase("(-) Factor ", fontText));
            ConfCelda.PropiedadesCeldadas(cell42, 0, 0, 0, 0, 0, 0, 1);
            tablaDetalle.AddCell(cell42);

            PdfPCell cell52 = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
            ConfCelda.PropiedadesCeldadas(cell52, 0, 0, 0, 0, 0, 0, 1);
            tablaDetalle.AddCell(cell52);

            PdfPCell celliFactor = new PdfPCell(new Phrase(String.Format("{0:C}", listaDetallexEmpleado[0].iFactor), fontText));
            ConfCelda.PropiedadesCeldadas(celliFactor, 0, 0, 1, 0, 0, 10, 2);
            tablaDetalle.AddCell(celliFactor);


            //Retencion A. Adicional
            PdfPCell cell13 = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
            ConfCelda.PropiedadesCeldadas(cell13, 0, 1, 0, 0, 10, 0, 1);
            tablaDetalle.AddCell(cell13);

            PdfPCell cell23 = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
            ConfCelda.PropiedadesCeldadas(cell23, 0, 0, 0, 0, 0, 0, 1);
            tablaDetalle.AddCell(cell23);

            PdfPCell cell33 = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
            ConfCelda.PropiedadesCeldadas(cell33, 0, 0, 0, 0, 0, 0, 1);
            tablaDetalle.AddCell(cell33);

            PdfPCell cell43 = new PdfPCell(new Phrase("(=) A. Adicional ", fontText));
            ConfCelda.PropiedadesCeldadas(cell43, 0, 0, 0, 0, 0, 0, 1);
            tablaDetalle.AddCell(cell43);

            PdfPCell cell53 = new PdfPCell(new Phrase(String.Format("{0:N}", " "), fontText));
            ConfCelda.PropiedadesCeldadas(cell53, 0, 0, 0, 0, 0, 0, 1);
            tablaDetalle.AddCell(cell53);

            PdfPCell cellRetencion = new PdfPCell(new Phrase(String.Format("{0:C}", listaDetallexEmpleado[0].iRetencion), fontText));
            ConfCelda.PropiedadesCeldadas(cellRetencion, 0, 0, 1, 0, 0, 10, 2);
            tablaDetalle.AddCell(cellRetencion);

            document.Add(tablaDetalle); //Finaliza el detalle de la tabla


            TablaTitulos.DeleteBodyRows(); //Eliminar informacion anterior

            PdfPCell CeldaEspacio = new PdfPCell(new Phrase("\n", fontText));
            CeldaEspacio.Colspan = 3;
            ConfCelda.PropiedadesCeldadas(CeldaEspacio, 0, 1, 1, 0, 10, 10, 1);
            TablaTitulos.AddCell(CeldaEspacio);

            PdfPCell CeldaNota = new PdfPCell(new Phrase("Te recuerdo que esta importe último se te descontará por nomina del día 15 del mes por tus consumos del combustible. ", fontText));
            CeldaNota.Colspan = 3;
            ConfCelda.PropiedadesCeldadas(CeldaNota, 0, 1, 1, 0, 10, 10, 1);
            TablaTitulos.AddCell(CeldaNota);

            PdfPCell CeldaEspacio2 = new PdfPCell(new Phrase("\n", fontText));
            CeldaEspacio2.Colspan = 3;
            ConfCelda.PropiedadesCeldadas(CeldaEspacio2, 0, 1, 1, 1, 10, 10, 1);
            TablaTitulos.AddCell(CeldaEspacio2);

            document.Add(TablaTitulos); 

            document.Close();
        }
    }
}
