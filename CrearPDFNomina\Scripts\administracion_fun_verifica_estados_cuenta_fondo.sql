CREATE OR REPLACE FUNCTION fun_verifica_estados_cuenta_fondo(INTEGER)
   RETURNS DATE AS
$BODY$
DECLARE
	iClaveMovimiento ALIAS FOR $1;
	dFechaEnviados DATE;
	dFechaEdoCta DATE;
BEGIN
	/*
		NOMBRE: <PERSON><PERSON><PERSON>#eda 93114338
		MODIFICO: <PERSON><PERSON>ondo 96294418
		BD: Administracion PostgreSQL
		FECHA: 29/07/2016
		SERVIDOR PRUEBAS: ************
		SERVIDOR PRODUCCION: **********
		DESCRIPCION: Se encargara de verificar si ya se genero el envio de estados de cuenta por correo.
		MODULO: NO0069
		RUTA: svn://************/sysx/administracion/contabilidad/nominacoppel/NO0069/CrearPDFNomina/Scripts
	*/
	
	dFechaEnviados := COALESCE((SELECT fec_estado_cuenta FROM mov_estados_cuenta_fondo WHERE clv_movimiento = iClaveMovimiento ORDER BY fec_estado_cuenta DESC LIMIT 1),'19000101');
	
	IF (iClaveMovimiento = 2) THEN --Edo. Cta. Fondo
		dFechaEdoCta := DATE('19000101');
		--dFechaEdoCta := COALESCE((SELECT fecha FROM elpestadoscuenta ORDER BY fecha DESC LIMIT 1),'19000101');
	ELSIF (iClaveMovimiento = 3) THEN --Vales Gasolina
		dFechaEdoCta := COALESCE((SELECT fechacorte from gasdirectivosgasolinahistorico ORDER BY fechacorte DESC LIMIT 1),'19000101');
	END IF;
	
	IF (dFechaEnviados = dFechaEdoCta) THEN
		dFechaEdoCta := DATE('19000101');
	END IF;

	RETURN dFechaEdoCta;
END;
$BODY$
   LANGUAGE plpgsql VOLATILE SECURITY DEFINER;