﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Entidades.Gasolina
{
    public class EstadoDetalleGasolina
    {
        private string _snombre;
        private string _sapellidopaterno;
        private string _sapellidomaterno;
        private string _sdescripcioncentro;
        private DateTime _dfechacorte;
        private int _iunidad;
        private Int64 _ivale;
        private int _iproveedor;
        private string _snombreProveedor;
        private string _sfactura;
        private string _sciudad;
        private DateTime _dfechavale;
        private decimal _iimporte;
        private decimal _ifactor;
        private decimal _iretencion;
        private decimal _itotal;

        public EstadoDetalleGasolina()
        {
            _snombre = string.Empty;
            _sapellidopaterno = string.Empty;
            _sapellidomaterno = string.Empty;
            _sdescripcioncentro = string.Empty;
            _dfechacorte = Convert.ToDateTime("1900-01-01");
            _iunidad = 0;
            _ivale = 0;
            _iproveedor = 0;
            _snombreProveedor = string.Empty;
            _sfactura = string.Empty;
            _sciudad = string.Empty;
            _dfechavale = Convert.ToDateTime("1900-01-01");
            _iimporte = 0;
            _ifactor = 0;
            _iretencion = 0;
            _itotal = 0;
        }
        public string sNombre
        {
            get
            {
                return _snombre;
            }
            set
            {
                _snombre = value;
            }
        }
        public string sApellidoPaterno
        {
            get
            {
                return _sapellidopaterno;
            }
            set
            {
                _sapellidopaterno = value;
            }
        }
        public string sApellidoMaterno
        {
            get
            {
                return _sapellidomaterno;
            }
            set
            {
                _sapellidomaterno = value;
            }
        }
        public string sDescripcionCentro
        {
            get
            {
                return _sdescripcioncentro;
            }
            set
            {
                _sdescripcioncentro = value;
            }
        }
        public DateTime dFechaCorte
        {
            get
            {
                return _dfechacorte;
            }
            set
            {
                _dfechacorte = value;
            }
        }
        public int iUnidad
        {
            get
            {
                return _iunidad;
            }
            set
            {
                _iunidad = value;
            }
        }
        public Int64 iVale
        {
            get
            {
                return _ivale;
            }
            set
            {
                _ivale = value;
            }
        }
        public int iProveedor
        {
            get
            {
                return _iproveedor;
            }
            set
            {
                _iproveedor = value;
            }
        }
        public string sNombreProveedor
        {
            get
            {
                return _snombreProveedor;
            }
            set
            {
                _snombreProveedor = value;
            }
        }
        public string sFactura
        {
            get
            {
                return _sfactura;
            }
            set
            {
                _sfactura = value;
            }
        }
        public string sCiudad
        {
            get
            {
                return _sciudad;
            }
            set
            {
                _sciudad = value;
            }
        }
        public DateTime dFechaVale
        {
            get
            {
                return _dfechavale;
            }
            set
            {
                _dfechavale = value;
            }
        }
        public decimal iImporte
        {
            get
            {
                return _iimporte;
            }
            set
            {
                _iimporte = value;
            }
        }
        public decimal iFactor
        {
            get
            {
                return _ifactor;
            }
            set
            {
                _ifactor = value;
            }
        }
        public decimal iRetencion
        {
            get
            {
                return _iretencion;
            }
            set
            {
                _iretencion = value;
            }
        }
        public decimal iTotal
        {
            get
            {
                return _itotal;
            }
            set
            {
                _itotal = value;
            }
        }
    }
}
