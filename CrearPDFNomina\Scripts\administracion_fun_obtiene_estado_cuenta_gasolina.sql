-- Function: fun_obtiene_estado_cuenta_gasolina(integer, date)

DROP FUNCTION fun_obtiene_estado_cuenta_gasolina(integer, date);

CREATE OR REPLACE FUNCTION fun_obtiene_estado_cuenta_gasolina(INTEGER,DATE)
   RETURNS SETOF type_obtiene_estado_cuenta_gasolina AS
$BODY$
DECLARE
	/*
		NOMBRE: <PERSON><PERSON><PERSON>#eda 93114338
		BD: Administracion PostgreSQL
		FECHA: 02/08/2016
		SERVIDOR PRUEBAS: ************
		SERVIDOR PRODUCCION: **********
		DESCRIPCION: Se encarga de obtener los movimientos de estados de cuenta de gasolina por directivo
		MODULO:
		RUTA:
	*/

	iEmpleado ALIAS FOR $1;
	dFecha ALIAS FOR $2;
	rRegistros type_obtiene_estado_cuenta_gasolina;
BEGIN
	FOR rRegistros IN SELECT a.proveedor,a.nombre<PERSON><PERSON>edor,a.factura,a.fecha_vale,a.importe,a.empleado,a.fecha_captura,a.unidad,a.iniciales,b.nom_empleado,
			  b.nom_empapellidopaterno,b.nom_empapellidomaterno,a.centro,a.nombrecentro,a.chofer,a.nomchofer,a.total,a.factor,a.retencion,a.numerociudad,
			  a.valen,a.fechacorte,a.adicional
			  FROM gasdirectivosgasolinahistorico a
			  JOIN cat_empleadoslinea_gx b ON a.empleado = b.idu_empleado
			  WHERE a.fechacorte = dFecha
			  AND a.empleado = iEmpleado
			  ORDER BY a.unidad ASC,a.fecha_vale ASC
	LOOP
		RETURN NEXT rRegistros;
	END LOOP;
END;
$BODY$
   LANGUAGE plpgsql VOLATILE SECURITY DEFINER;

GRANT EXECUTE ON FUNCTION fun_obtiene_estado_cuenta_gasolina(INTEGER,DATE) TO sysgenexus;