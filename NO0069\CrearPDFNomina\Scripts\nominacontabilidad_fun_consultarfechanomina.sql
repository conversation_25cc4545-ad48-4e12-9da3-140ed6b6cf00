CREATE OR REPLACE FUNCTION fun_consultarfechanomina(OUT dfechanomina date, OUT dfechareparto date, OUT dfechaaguinaldo date, OUT igenerautilidades smallint, OUT igeneraaguinaldo smallint)
  RETURNS record AS
$BODY$
DECLARE  
	--Autor: 96862629 - Cabanillas Urias Jaime <PERSON>
	--Base de Datos: nominacontabilidad
	--<PERSON>cha creacion: 06/05/2016
	--Servidor de desarrollo: ************
	--Servidor de produccion: **********
	--Descripcion: Obtiene las fechas de generacion de nomina, utilidades y aguinaldo, y determina si se debe tomar en cuenta el proceso
	--de generacion de Utilidades y Aguinaldo
	dMesActual SMALLINT DEFAULT 0; 
	dMesUtilidades SMALLINT DEFAULT 0; 
	dMesAguinaldo SMALLINT DEFAULT 0;
BEGIN
	iGeneraUtilidades = 0;
	iGeneraAguinaldo = 0;

	IF EXISTS(SELECT fechanomina FROM nomcontrol LIMIT 1) THEN 
		SELECT fechanomina, fechareparto, fechaaguinaldo INTO dFechaNomina, dFechaReparto, dFechaAguinaldo FROM nomcontrol LIMIT 1;
		SELECT EXTRACT(MONTH FROM dFechaReparto), EXTRACT(MONTH FROM dFechaAguinaldo), EXTRACT(MONTH FROM dFechaNomina) INTO dMesUtilidades,dMesAguinaldo,dMesActual; 

		IF dMesActual = dMesUtilidades AND EXTRACT(DAY FROM dFechaNomina) = 15 THEN
			iGeneraUtilidades = 1;
		ELSIF dMesActual = dMesAguinaldo AND EXTRACT(DAY FROM dFechaNomina) = 30 THEN 
			iGeneraAguinaldo = 1;
		END IF; 
	ELSE 
		dFechaNomina = '1900-01-01'::DATE;
		dFechaReparto = '1900-01-01'::DATE;
		dFechaAguinaldo = '1900-01-01'::DATE;
	END IF;

	RETURN;
END; 
$BODY$
  LANGUAGE plpgsql VOLATILE SECURITY DEFINER;

GRANT EXECUTE ON FUNCTION fun_consultarfechanomina(OUT dfechanomina date, OUT dfechareparto date, OUT dfechaaguinaldo date, OUT igenerautilidades smallint, OUT igeneraaguinaldo smallint) TO sysingresos;