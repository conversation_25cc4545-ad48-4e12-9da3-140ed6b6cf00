﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Datos.Nomina;
using Datos.Control;
using Datos.Errores;
using Entidades.Nomina;
using Negocios.PDF;
using Negocios.Generales;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Threading.Tasks;

namespace Negocios.Nomina
{
    public class NominaBO
    {
        private static object threadLock = new object();
        public bool construirPDF(DateTime dFechaNomina, int iTipo)
        {
            NominaDetalleBO NominaDetalle = new NominaDetalleBO();
            PDFNominaBO PDF = new PDFNominaBO();
            GeneralesBO Generales = new GeneralesBO();


            bool bGenero = true;
            byte[] bPDF64 = null;
            int iContador = 0;

            try
            {

                List<NominaEmpleado> listaNomina = obtenerNominaEmpleado(dFechaNomina);

                foreach (var item in listaNomina)
                {
                    List<NominaDetalleEmpleado> listaNominaDetalle = NominaDetalle.obtenerNominaDetalleEmpleado(item.iNumEmp, dFechaNomina, item.iNumeroEmpresa);

                    if (listaNominaDetalle.LongCount() >= 1)
                    {
                        iContador++;
                        PDF.construirPDFNomina(dFechaNomina, item.iCentro, item.sDescCentro, item.dTotalAPagar, item.dTotalIngresos, item.dTotalEgresos, item.dFechaInicio, item.dFechaFinal, item.sTarjetaBanco, item.sDescRutaPago, item.sDescCiudad, item.iNumEmp, item.sApellidoPaterno, item.sApellidoMaterno, item.sNombre, item.sNumAfiliacion, item.sRFC, item.sCURP, listaNominaDetalle, item.iNumeroEmpresa, item.sNombreEmpresa, item.sTarjetaDespensa, item.dTotalDespensa,item.cNombreEmpleadoCorregido,item.cRfcEmpleadoCorregido);
                        bPDF64 = Generales.convertirPDFA64(item.iNumEmp, dFechaNomina); //Se codifica PDF a 64 byte para insertarlo en BD nominacontabilidad
                        guardarTalonEmpleado(iTipo, item.iNumEmp, item.sApellidoPaterno, item.sApellidoMaterno, item.sNombre, bPDF64, dFechaNomina, item.sCorreoEmpleado, item.iNumeroEmpresa); //sCemail
                        Generales.depurarArchivo(item.iNumEmp, dFechaNomina);
                    }
                }

                if (iContador == 0)
                {
                    //MessageBox.Show("No existen registros para generar los PDF's", "PDF Nomina", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    bGenero = false;
                }
                else
                {
                    //Elimina Carpeta de PDF's del disco local
                    Generales.depurarCarpeta();
                }
            }
            catch (Exception ex)
            {
                GrabarErrorDAL Error = new GrabarErrorDAL();
                Error.grabarError("Negocios.Nomina", "NominaBO.cs", "construirPDF", "Error al Generar PDF", 5, ex.Message.ToString());
                throw new Exception(ex.Message.ToString());
            }

            return bGenero;
        }

        public List<NominaEmpleado> obtenerNominaEmpleado(DateTime dFechaNomina)
        {
            NominaDAL Nomina = new NominaDAL();
            try
            {
                return Nomina.consultarNomina(dFechaNomina);
            }
            catch (Exception ex)
            {
               GrabarErrorDAL Error = new GrabarErrorDAL();
               Error.grabarError("Negocios.Nomina", "NominaBO.cs", "obtenerNominaEmpleado", "Error al obtener la lista de empleados", 6, ex.Message.ToString());
               throw new Exception(ex.Message.ToString());
            }
        }

        public Int64 guardarTalonEmpleado(int iTipo, int iNumeroEmpleado, string sApellidoPaterno, string ApellidoMaterno, string sNombre,
            byte[] sPDF64, DateTime dFechaNomina, string sCorreoEmpleado, int iEmpresa)
        {
            NominaDAL Nomina = new NominaDAL();

            try
            {
                return Nomina.guardarTalonNomina(iTipo, iNumeroEmpleado, sApellidoPaterno, ApellidoMaterno, sNombre, sPDF64, dFechaNomina, sCorreoEmpleado, iEmpresa);
            }
            catch (Exception ex)
            {
                GrabarErrorDAL Error = new GrabarErrorDAL();
                Error.grabarError("Negocios.Nomina", "NominaBO.cs", "guardarTalonEmpleado", "Error al Guardar talon del empleado", 7, ex.Message.ToString());
                throw new Exception(ex.Message.ToString());
            }
        }

        public bool depurarTalonNomina(int iTipo)
        {
            bool bContinuar = false;
            NominaDAL Nomina = new NominaDAL();

            try
            {
                bContinuar = Nomina.depurarTalonNomina(iTipo);
            }
            catch (Exception ex)
            {
                GrabarErrorDAL Error = new GrabarErrorDAL();
                Error.grabarError("Negocios.Nomina", "NominaBO.cs", "depurarTalonNomina", "Error al depurar talon nomina", 8, ex.Message.ToString());
                throw new Exception(ex.Message.ToString());
            }

            return bContinuar;
        }
    }
}
