-- Function: fun_obtiene_estado_fondo_empleados(integer, date, integer)

-- DROP FUNCTION fun_obtiene_estado_fondo_empleados(integer, date, integer);

CREATE OR REPLACE FUNCTION fun_obtiene_estado_fondo_empleados(integer, date, integer)
  RETURNS SETOF type_obtiene_estado_fondo_empleados AS
$BODY$
DECLARE
	iNumemp		ALIAS FOR $1;
	dFecha		ALIAS FOR $2;
	iOpcion		ALIAS FOR $3; --1 Intranet, 2 Envio Correo
	sSql 		VARCHAR;
	rRegistros 	type_obtiene_estado_fondo_empleados;
BEGIN
	/*
		NOMBRE: <PERSON><PERSON> At<PERSON>o 96294418
		BD: Administracion PostgreSQL
		FECHA: 17/04/2017
		SERVIDOR PRUEBAS: ************
		SERVIDOR PRODUCCION: **********
		DESCRIPCION: FUNCION EN LA CUAL SE OBTIENEN LOS REGISTROS DEL FONDO DE LOS EMPLEADOS EN BASE A LA TABLA DE MOVIMIENTOS DE NOMINA DE LA QUINCENA
		MODULO: NO0069, COMPROBANTE.PHP
		RUTA: svn://************/sysx/administracion/contabilidad/nominacoppel/NO0069/CrearPDFNomina/Scripts
	*/
	
	CREATE TEMPORARY TABLE tmptalones
	(
		num_empleado INT NOT NULL DEFAULT 0,
		des_apellidopaterno VARCHAR(50) NOT NULL DEFAULT '',
		des_apellidomaterno VARCHAR(50) NOT NULL DEFAULT '',
		des_nombre VARCHAR(50) NOT NULL DEFAULT '',
		clv_rfc VARCHAR(13) NOT NULL DEFAULT '',
		clv_curp VARCHAR(18) NOT NULL DEFAULT '',
		fec_alta DATE NOT NULL DEFAULT '19000101',
		imp_sueldomensual INT NOT NULL DEFAULT 0,
		num_centro INT NOT NULL DEFAULT 0,
		des_centro VARCHAR(50) NOT NULL DEFAULT '',
		num_seguros INT NOT NULL DEFAULT 0,
		imp_seguros INT NOT NULL DEFAULT 0,
		des_correo VARCHAR(100) NOT NULL DEFAULT '',
		fec_nomina DATE NOT NULL DEFAULT '19000101'
	)ON COMMIT DROP;

	CREATE UNIQUE INDEX indx_tmptalones
	ON tmptalones
	USING btree
	(num_empleado);

	--Tabla para los centros
	CREATE TEMPORARY TABLE tmpCentrosEmpleados
	(
	    icentro INTEGER NOT NULL DEFAULT 0,
	    iciudad INTEGER NOT NULL DEFAULT 0,
	    iregion INTEGER NOT NULL DEFAULT 0
	)ON COMMIT DROP;

	CREATE UNIQUE INDEX indx_tmpCentrosEmpleados
	ON tmpCentrosEmpleados
	USING btree
	(icentro);

	INSERT INTO tmpCentrosEmpleados 
	SELECT a.idu_centro, a.idu_ciudad, b.idu_region
	FROM cat_Centros_gx a JOIN cat_ciudades_gx b 
	ON a.idu_ciudad = b.idu_ciudad
	WHERE b.idu_region = 1;

	sSql := 'INSERT INTO tmptalones(num_empleado,fec_nomina,des_correo)
	SELECT a.numemp, MAX(a.fechanomina), RTRIM(b.des_correo) AS des_correo
	FROM saprecibosnominasgrupo a
	JOIN cat_emailempleados b ON a.numemp = b.num_empleado 
	WHERE TO_CHAR(a.fechanomina,''yyyyMM'') = TO_CHAR(DATE('''||dFecha||'''),''yyyyMM'') ';
	--JOIN cat_empleados_culiacan b ON a.numemp = b.num_empleado 

	IF iNumEmp != 0 THEN
		sSql := sSql||'AND a.numemp = '||iNumEmp||' ';
	END IF;

	sSql := sSql||'GROUP BY a.numemp, b.des_correo ORDER BY a.numemp; ';

	EXECUTE sSql;

	UPDATE tmptalones SET des_apellidopaterno = a.apellidopaterno, des_apellidomaterno = a.apellidomaterno, des_nombre = a.nombre, clv_rfc = a.rfc, clv_curp = a.curp,
	fec_alta = a.fechaalta, imp_sueldomensual = a.sueldomensual, num_centro = a.centro, des_centro = a.descripcioncentro, num_seguros = a.numeroseguros, 
	imp_seguros = a.importeseguro
	FROM saprecibosnominasgrupo a
	WHERE tmptalones.num_empleado = a.numemp
	AND tmptalones.fec_nomina = a.fechanomina;

	/*IF iOpcion = 2 THEN 
		DELETE FROM tmptalones 
		USING tmptalones AS a
		LEFT JOIN tmpCentrosEmpleados AS b ON a.num_centro = b.icentro 
		WHERE tmptalones.num_centro = a.num_centro AND COALESCE(b.icentro,0) = 0;
	END IF;*/
	
	FOR rRegistros IN SELECT a.num_empleado, a.des_apellidopaterno, a.des_apellidomaterno, a.des_nombre, a.clv_rfc, a.clv_curp, a.fec_alta, a.imp_sueldomensual, 
			  a.num_centro, a.des_centro, a.num_seguros, a.imp_seguros, a.des_correo
			  FROM tmptalones a
			  ORDER BY a.num_empleado
	LOOP
		RETURN NEXT rRegistros;
	END LOOP;
END;
$BODY$
  LANGUAGE plpgsql VOLATILE SECURITY DEFINER;
GRANT EXECUTE ON FUNCTION fun_obtiene_estado_fondo_empleados(integer, date, integer) TO sysgenexus;