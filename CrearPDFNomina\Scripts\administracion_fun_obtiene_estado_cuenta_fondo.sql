CREATE TYPE type_obtiene_estado_cuenta_fondo AS
(
	num_empleado INT,
	clv_movimiento INT,
	fec_movimiento DATE,
	imp_movimiento BIGINT,
	des_movimiento VARCHAR(50)
);

CREATE OR REPLACE FUNCTION fun_obtiene_estado_cuenta_fondo(INTEGER,DATE)
   RETURNS SETOF type_obtiene_estado_cuenta_fondo AS
$BODY$
DECLARE
	iNumEmpleado ALIAS FOR $1;
	dFecFondo ALIAS FOR $2;
	rRegistros type_obtiene_estado_cuenta_fondo;
BEGIN
	/*
		NOMBRE: <PERSON><PERSON><PERSON>#eda 93114338
		BD: Administracion PostgreSQL
		FECHA: 29/07/2016
		SERVIDOR PRUEBAS: ************
		SERVIDOR PRODUCCION: **********
		DESCRIPCION: Se encargará de obtener los movimientos para generacion de estado de cuenta del fondo.
		MODULO: 
		RUTA: 
	*/
	
	CREATE TEMPORARY TABLE tmpmovimientos
	(
		num_empleado INT NOT NULL DEFAULT 0,
		clv_movimiento INT NOT NULL DEFAULT 0,
		fec_movimiento DATE NOT NULL DEFAULT '19000101',
		imp_movimiento BIGINT NOT NULL DEFAULT 0,
		des_movimiento VARCHAR(50) NOT NULL DEFAULT ''
	)ON COMMIT DROP;

	INSERT INTO tmpmovimientos(clv_movimiento,des_movimiento,num_empleado,fec_movimiento)
	SELECT clv_movimiento,des_movimiento,iNumEmpleado,dFecFondo
	FROM cat_movimientos_estados_fondo
	ORDER BY clv_movimiento ASC;

	UPDATE tmpmovimientos SET imp_movimiento = a.importe
	FROM elpestadoscuenta a
	WHERE tmpmovimientos.num_empleado = a.numemp
	AND tmpmovimientos.clv_movimiento = a.clave
	AND tmpmovimientos.fec_movimiento = a.fecha;

	FOR rRegistros IN SELECT num_empleado, clv_movimiento, fec_movimiento, imp_movimiento, des_movimiento
			  FROM tmpmovimientos
			  ORDER BY clv_movimiento
	LOOP
		RETURN NEXT rRegistros;
	END LOOP;
END;
$BODY$
   LANGUAGE plpgsql VOLATILE SECURITY DEFINER;

GRANT EXECUTE ON FUNCTION fun_obtiene_estado_cuenta_fondo(INTEGER,DATE) TO sysgenexus;