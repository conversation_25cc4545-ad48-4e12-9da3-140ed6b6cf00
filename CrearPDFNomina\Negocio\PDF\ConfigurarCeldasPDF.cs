﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Configuration;
using System.IO;
using iTextSharp.text;
using iTextSharp.text.pdf;
using Entidades.Fondo;
using System.Windows.Forms;

namespace Negocios.PDF
{
    public class ConfigurarCeldasPDF
    {
        public void PropiedadesCeldadas(PdfPCell Celda, int BorderTop, int BorderLeft, int BoderRight, int BorderBottom, int PaddingLeft, int PaddingRight, int alineacion)
        {
            Celda.BorderWidthTop = BorderTop;
            Celda.BorderWidthLeft = BorderLeft;
            Celda.BorderWidthRight = BoderRight;
            Celda.BorderWidthBottom = BorderBottom;
            Celda.PaddingRight = PaddingRight;
            Celda.PaddingLeft = PaddingLeft;
            if (alineacion == 1) //LEFT
            {
                Celda.HorizontalAlignment = Element.ALIGN_LEFT;
            }
            else if (alineacion == 2) //RIGHT
            {
                Celda.HorizontalAlignment = Element.ALIGN_RIGHT;
            }
            else if (alineacion == 3) //CENTER
            {
                Celda.HorizontalAlignment = Element.ALIGN_CENTER;
            }
            else
            {
                //null
            }
        }
    }
}
