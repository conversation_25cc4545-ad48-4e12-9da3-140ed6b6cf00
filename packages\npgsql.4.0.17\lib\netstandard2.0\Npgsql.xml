<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Npgsql</name>
    </assembly>
    <members>
        <member name="T:Npgsql.BackendMessages.CopyDataMessage">
            <summary>
            Note that this message doesn't actually contain the data, but only the length. Data is processed
            directly from the connector's buffer.
            </summary>
        </member>
        <member name="T:Npgsql.BackendMessages.CopyDoneMessage">
            <remarks>
            Note: This message is both a frontend and a backend message
            </remarks>
        </member>
        <member name="T:Npgsql.BackendMessages.DataRowMessage">
            <summary>
            DataRow is special in that it does not parse the actual contents of the backend message,
            because in sequential mode the message will be traversed and processed sequentially by
            <see cref="T:Npgsql.NpgsqlSequentialDataReader"/>.
            </summary>
        </member>
        <member name="T:Npgsql.BackendMessages.ErrorOrNoticeMessage.ErrorFieldTypeCode">
            <summary>
            Error and notice message field codes
            </summary>
        </member>
        <member name="T:Npgsql.BackendMessages.RowDescriptionMessage">
            <summary>
            A RowDescription message sent from the backend.
            </summary>
            <remarks>
            See http://www.postgresql.org/docs/current/static/protocol-message-formats.html
            </remarks>
        </member>
        <member name="M:Npgsql.BackendMessages.RowDescriptionMessage.GetFieldIndex(System.String)">
            <summary>
            Given a string name, returns the field's ordinal index in the row.
            </summary>
        </member>
        <member name="M:Npgsql.BackendMessages.RowDescriptionMessage.TryGetFieldIndex(System.String,System.Int32@)">
            <summary>
            Given a string name, returns the field's ordinal index in the row.
            </summary>
        </member>
        <member name="T:Npgsql.BackendMessages.RowDescriptionMessage.InsensitiveComparer">
            <summary>
            Comparer that's case-insensitive and Kana width-insensitive
            </summary>
        </member>
        <member name="T:Npgsql.BackendMessages.FieldDescription">
            <summary>
            A descriptive record on a single field received from PostgreSQL.
            See RowDescription in http://www.postgresql.org/docs/current/static/protocol-message-formats.html
            </summary>
        </member>
        <member name="P:Npgsql.BackendMessages.FieldDescription.Name">
            <summary>
            The field name.
            </summary>
        </member>
        <member name="P:Npgsql.BackendMessages.FieldDescription.TypeOID">
            <summary>
            The object ID of the field's data type.
            </summary>
        </member>
        <member name="P:Npgsql.BackendMessages.FieldDescription.TypeSize">
            <summary>
            The data type size (see pg_type.typlen). Note that negative values denote variable-width types.
            </summary>
        </member>
        <member name="P:Npgsql.BackendMessages.FieldDescription.TypeModifier">
            <summary>
            The type modifier (see pg_attribute.atttypmod). The meaning of the modifier is type-specific.
            </summary>
        </member>
        <member name="P:Npgsql.BackendMessages.FieldDescription.TableOID">
            <summary>
            If the field can be identified as a column of a specific table, the object ID of the table; otherwise zero.
            </summary>
        </member>
        <member name="P:Npgsql.BackendMessages.FieldDescription.ColumnAttributeNumber">
            <summary>
            If the field can be identified as a column of a specific table, the attribute number of the column; otherwise zero.
            </summary>
        </member>
        <member name="P:Npgsql.BackendMessages.FieldDescription.FormatCode">
            <summary>
            The format code being used for the field.
            Currently will be zero (text) or one (binary).
            In a RowDescription returned from the statement variant of Describe, the format code is not yet known and will always be zero.
            </summary>
        </member>
        <member name="P:Npgsql.BackendMessages.FieldDescription.Handler">
            <summary>
            The Npgsql type handler assigned to handle this field.
            Returns <see cref="T:Npgsql.TypeHandlers.UnknownTypeHandler"/> for fields with format text.
            </summary>
        </member>
        <member name="P:Npgsql.BackendMessages.FieldDescription.RealHandler">
            <summary>
            The type handler resolved for this field, regardless of whether it's binary or text.
            </summary>
        </member>
        <member name="M:Npgsql.BackendMessages.FieldDescription.ToString">
            <summary>
            Returns a string that represents the current object.
            </summary>
        </member>
        <member name="T:Npgsql.IBackendMessage">
            <summary>
            Base class for all classes which represent a message sent by the PostgreSQL backend.
            </summary>
        </member>
        <member name="T:Npgsql.FrontendMessage">
            <summary>
            Base class for all classes which represent a message sent to the PostgreSQL backend.
            Concrete classes which directly inherit this represent arbitrary-length messages which can chunked.
            </summary>
        </member>
        <member name="M:Npgsql.FrontendMessage.Write(Npgsql.NpgsqlWriteBuffer,System.Boolean)">
            <param name="buf">the buffer into which to write the message.</param>
            <param name="async"></param>
            <returns>
            Whether there was enough space in the buffer to contain the entire message.
            If false, the buffer should be flushed and write should be called again.
            </returns>
        </member>
        <member name="P:Npgsql.FrontendMessage.ResponseMessageCount">
            <summary>
            Returns how many messages PostgreSQL is expected to send in response to this message.
            Used for message prepending.
            </summary>
        </member>
        <member name="T:Npgsql.SimpleFrontendMessage">
            <summary>
            Represents a simple frontend message which is typically small and fits well within
            the write buffer. The message is first queries for the number of bytes it requires,
            and then writes itself out.
            </summary>
        </member>
        <member name="P:Npgsql.SimpleFrontendMessage.Length">
            <summary>
            Returns the number of bytes needed to write this message.
            </summary>
        </member>
        <member name="M:Npgsql.SimpleFrontendMessage.WriteFully(Npgsql.NpgsqlWriteBuffer)">
            <summary>
            Writes the message contents into the buffer.
            </summary>
        </member>
        <member name="T:Npgsql.StatementType">
            <summary>
            Specifies the type of SQL statement, e.g. SELECT
            </summary>
        </member>
        <member name="T:Npgsql.ConnectorPool">
            <summary>
            Connection pool for PostgreSQL physical connections. Attempts to allocate connections over MaxPoolSize will
            block until someone releases. Implementation is completely lock-free to avoid contention, and ensure FIFO
            for open attempts waiting (because the pool is at capacity).
            </summary>
        </member>
        <member name="P:Npgsql.ConnectorPool.UserFacingConnectionString">
            <summary>
            Contains the connection string returned to the user from <see cref="P:Npgsql.NpgsqlConnection.ConnectionString"/>
            after the connection has been opened. Does not contain the password unless Persist Security Info=true.
            </summary>
        </member>
        <member name="F:Npgsql.ConnectorPool._clearCounter">
            <summary>
            Incremented every time this pool is cleared via <see cref="M:Npgsql.NpgsqlConnection.ClearPool(Npgsql.NpgsqlConnection)"/> or
            <see cref="M:Npgsql.NpgsqlConnection.ClearAllPools"/>. Allows us to identify connections which were
            created before the clear.
            </summary>
        </member>
        <member name="F:Npgsql.ConnectorPool.PoolSizeLimit">
            <summary>
            Maximum number of possible connections in any pool.
            </summary>
        </member>
        <member name="F:Npgsql.Counters.HardConnectsPerSecond">
            <summary>
            The number of connections per second that are being made to a database server.
            </summary>
        </member>
        <member name="F:Npgsql.Counters.HardDisconnectsPerSecond">
            <summary>
            The number of disconnects per second that are being made to a database server.
            </summary>
        </member>
        <member name="F:Npgsql.Counters.NumberOfActiveConnectionPools">
            <summary>
            The total number of connection pools.
            </summary>
        </member>
        <member name="F:Npgsql.Counters.NumberOfActiveConnections">
            <summary>
            The number of (pooled) active connections that are currently in use.
            </summary>
        </member>
        <member name="F:Npgsql.Counters.NumberOfFreeConnections">
            <summary>
            The number of connections available for use in the connection pools.
            </summary>
        </member>
        <member name="F:Npgsql.Counters.NumberOfNonPooledConnections">
            <summary>
            The number of active connections that are not pooled.
            </summary>
        </member>
        <member name="F:Npgsql.Counters.NumberOfPooledConnections">
            <summary>
            The number of active connections that are being managed by the connection pooling infrastructure.
            </summary>
        </member>
        <member name="F:Npgsql.Counters.SoftConnectsPerSecond">
            <summary>
            The number of active connections being pulled from the connection pool.
            </summary>
        </member>
        <member name="F:Npgsql.Counters.SoftDisconnectsPerSecond">
            <summary>
            The number of active connections that are being returned to the connection pool.
            </summary>
        </member>
        <member name="T:Npgsql.Counter">
            <summary>
            This class is currently a simple wrapper around System.Diagnostics.PerformanceCounter.
            Since these aren't supported in .NET Standard, all the ifdef'ing happens here.
            When an alternative performance counter API emerges for netstandard, it can be added here.
            </summary>
        </member>
        <member name="P:Npgsql.FrontendMessages.BindMessage.Portal">
            <summary>
            The name of the destination portal (an empty string selects the unnamed portal).
            </summary>
        </member>
        <member name="P:Npgsql.FrontendMessages.BindMessage.Statement">
            <summary>
            The name of the source prepared statement (an empty string selects the unnamed prepared statement).
            </summary>
        </member>
        <member name="P:Npgsql.FrontendMessages.CloseMessage.Name">
            <summary>
            The name of the prepared statement or portal to close (an empty string selects the unnamed prepared statement or portal).
            </summary>
        </member>
        <member name="P:Npgsql.FrontendMessages.CloseMessage.StatementOrPortal">
            <summary>
            Whether to close a statement or a portal
            </summary>
        </member>
        <member name="P:Npgsql.FrontendMessages.DescribeMessage.Name">
            <summary>
            The name of the prepared statement or portal to describe (an empty string selects the unnamed prepared statement or portal).
            </summary>
        </member>
        <member name="P:Npgsql.FrontendMessages.DescribeMessage.StatementOrPortal">
            <summary>
            Whether to describe a statement or a portal
            </summary>
        </member>
        <member name="P:Npgsql.FrontendMessages.ParseMessage.Query">
            <summary>
            The query string to be parsed.
            </summary>
        </member>
        <member name="P:Npgsql.FrontendMessages.ParseMessage.Statement">
            <summary>
            The name of the destination prepared statement (an empty string selects the unnamed prepared statement).
            </summary>
        </member>
        <member name="M:Npgsql.FrontendMessages.PasswordMessage.CreateMD5(System.String,System.String,System.Byte[])">
            <summary>
            Creates an MD5 password message.
            This is the password, hashed with the username as salt, and hashed again with the backend-provided
            salt.
            </summary>
        </member>
        <member name="T:Npgsql.FrontendMessages.PregeneratedMessage">
            <summary>
            A frontend message of an arbitrary type that has been pregenerated for efficiency - it is kept
            in byte[] form and doesn't have to be serialized for each send.
            </summary>
        </member>
        <member name="M:Npgsql.FrontendMessages.PregeneratedMessage.#ctor(System.Byte[],System.String,System.Int32)">
            <summary>
            Constructs a new pregenerated message.
            </summary>
            <param name="data">The data to be sent for this message, not including the 4-byte length.</param>
            <param name="description">Optional string form/description for debugging</param>
            <param name="responseMessageCount">Returns how many messages PostgreSQL is expected to send in response to this message.</param>
        </member>
        <member name="T:Npgsql.FrontendMessages.QueryMessage">
            <summary>
            A simple query message.
            </summary>
        </member>
        <member name="T:Npgsql.INpgsqlDatabaseInfoFactory">
            <summary>
            A factory which get generate instances of <see cref="T:Npgsql.NpgsqlDatabaseInfo"/>, which describe a database
            and the types it contains. When first connecting to a database, Npgsql will attempt to load information
            about it via this factory.
            </summary>
        </member>
        <member name="M:Npgsql.INpgsqlDatabaseInfoFactory.Load(Npgsql.NpgsqlConnection,Npgsql.NpgsqlTimeout,System.Boolean)">
            <summary>
            Given a connection, loads all necessary information about the connected database, e.g. its types.
            A factory should only handle the exact database type it was meant for, and return null otherwise.
            </summary>
            <returns>
            An object describing the database to which <paramref name="conn"/> is connected, or null if the
            database isn't of the correct type and isn't handled by this factory.
            </returns>
        </member>
        <member name="T:Npgsql.KerberosUsernameProvider">
            <summary>
            Launches MIT Kerberos klist and parses out the default principal from it.
            Caches the result.
            </summary>
        </member>
        <member name="T:Npgsql.Logging.ConsoleLoggingProvider">
            <summary>
            An logging provider that outputs Npgsql logging messages to standard error.
            </summary>
        </member>
        <member name="M:Npgsql.Logging.ConsoleLoggingProvider.#ctor(Npgsql.Logging.NpgsqlLogLevel,System.Boolean,System.Boolean)">
            <summary>
            Constructs a new <see cref="T:Npgsql.Logging.ConsoleLoggingProvider"/>
            </summary>
            <param name="minLevel">Only messages of this level of higher will be logged</param>
            <param name="printLevel">If true, will output the log level (e.g. WARN). Defaults to false.</param>
            <param name="printConnectorId">If true, will output the connector ID. Defaults to false.</param>
        </member>
        <member name="M:Npgsql.Logging.ConsoleLoggingProvider.CreateLogger(System.String)">
            <summary>
            Creates a new <see cref="T:Npgsql.Logging.ConsoleLogger"/> instance of the given name.
            </summary>
        </member>
        <member name="T:Npgsql.Logging.INpgsqlLoggingProvider">
            Used to create logger instances of the given name.
        </member>
        <member name="M:Npgsql.Logging.INpgsqlLoggingProvider.CreateLogger(System.String)">
            <summary>
            Creates a new INpgsqlLogger instance of the given name.
            </summary>
        </member>
        <member name="T:Npgsql.Logging.NpgsqlLogger">
            <summary>
            A generic interface for logging.
            </summary>
        </member>
        <member name="T:Npgsql.Logging.NpgsqlLogManager">
            <summary>
            Manages logging for Npgsql, used to set the logging provider.
            </summary>
        </member>
        <member name="P:Npgsql.Logging.NpgsqlLogManager.Provider">
            <summary>
            The logging provider used for logging in Npgsql.
            </summary>
        </member>
        <member name="P:Npgsql.Logging.NpgsqlLogManager.IsParameterLoggingEnabled">
            <summary>
            Determines whether parameter contents will be logged alongside SQL statements - this may reveal sensitive information.
            Defaults to false.
            </summary>
        </member>
        <member name="T:Npgsql.INpgsqlNameTranslator">
            <summary>
            A component which translates a CLR name (e.g. SomeClass) into a database name (e.g. some_class)
            according to some scheme.
            Used for mapping enum and composite types.
            </summary>
        </member>
        <member name="M:Npgsql.INpgsqlNameTranslator.TranslateTypeName(System.String)">
            <summary>
            Given a CLR type name (e.g class, struct, enum), translates its name to a database type name.
            </summary>
        </member>
        <member name="M:Npgsql.INpgsqlNameTranslator.TranslateMemberName(System.String)">
            <summary>
            Given a CLR member name (property or field), translates its name to a database type name.
            </summary>
        </member>
        <member name="T:Npgsql.NameTranslation.NpgsqlNullNameTranslator">
            <summary>
            A name translator which preserves CLR names (e.g. SomeClass) when mapping names to the database.
            </summary>
        </member>
        <member name="M:Npgsql.NameTranslation.NpgsqlNullNameTranslator.TranslateTypeName(System.String)">
            <summary>
            Given a CLR type name (e.g class, struct, enum), translates its name to a database type name.
            </summary>
        </member>
        <member name="M:Npgsql.NameTranslation.NpgsqlNullNameTranslator.TranslateMemberName(System.String)">
            <summary>
            Given a CLR member name (property or field), translates its name to a database type name.
            </summary>
        </member>
        <member name="T:Npgsql.NameTranslation.NpgsqlSnakeCaseNameTranslator">
            <summary>
            A name translator which converts standard CLR names (e.g. SomeClass) to snake-case database
            names (some_class)
            </summary>
        </member>
        <member name="M:Npgsql.NameTranslation.NpgsqlSnakeCaseNameTranslator.#ctor">
            <summary>
            Creates a new <see cref="T:Npgsql.NameTranslation.NpgsqlSnakeCaseNameTranslator"/>.
            </summary>
        </member>
        <member name="M:Npgsql.NameTranslation.NpgsqlSnakeCaseNameTranslator.#ctor(System.Boolean)">
            <summary>
            Creates a new <see cref="T:Npgsql.NameTranslation.NpgsqlSnakeCaseNameTranslator"/>.
            </summary>
            <param name="legacyMode">Uses the legacy naming convention if <c>true</c>, otherwise it uses the new naming convention.</param>
        </member>
        <member name="M:Npgsql.NameTranslation.NpgsqlSnakeCaseNameTranslator.TranslateTypeName(System.String)">
            <summary>
            Given a CLR type name (e.g class, struct, enum), translates its name to a database type name.
            </summary>
        </member>
        <member name="M:Npgsql.NameTranslation.NpgsqlSnakeCaseNameTranslator.TranslateMemberName(System.String)">
            <summary>
            Given a CLR member name (property or field), translates its name to a database type name.
            </summary>
        </member>
        <member name="M:Npgsql.NameTranslation.NpgsqlSnakeCaseNameTranslator.ConvertToSnakeCase(System.String)">
            <summary>
            Converts a string to its snake_case equivalent.
            </summary>
            <remarks>
            Code borrowed from Newtonsoft.Json.
            See https://github.com/JamesNK/Newtonsoft.Json/blob/f012ba857f36fe75b1294a210b9104130a4db4d5/Src/Newtonsoft.Json/Utilities/StringUtils.cs#L200-L276.
            </remarks>
            <param name="value">The value to convert.</param>
        </member>
        <member name="T:Npgsql.NoSynchronizationContextScope">
             <summary>
             This mechanism is used to temporarily set the current synchronization context to null while
             executing Npgsql code, making all await continuations execute on the thread pool. This replaces
             the need to place ConfigureAwait(false) everywhere, and should be used in all surface async methods,
             without exception.
            
             Warning: do not use this directly in async methods, use it in sync wrappers of async methods
             (see https://github.com/npgsql/npgsql/issues/1593)
             </summary>
             <remarks>
             http://stackoverflow.com/a/28307965/640325
             </remarks>
        </member>
        <member name="T:Npgsql.NpgsqlBinaryExporter">
            <summary>
            Provides an API for a binary COPY TO operation, a high-performance data export mechanism from
            a PostgreSQL table. Initiated by <see cref="M:Npgsql.NpgsqlConnection.BeginBinaryExport(System.String)"/>
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlBinaryExporter.NumColumns">
            <summary>
            The number of columns, as returned from the backend in the CopyInResponse.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlBinaryExporter.StartRow">
            <summary>
            Starts reading a single row, must be invoked before reading any columns.
            </summary>
            <returns>
            The number of columns in the row. -1 if there are no further rows.
            Note: This will currently be the same value for all rows, but this may change in the future.
            </returns>
        </member>
        <member name="M:Npgsql.NpgsqlBinaryExporter.Read``1">
            <summary>
            Reads the current column, returns its value and moves ahead to the next column.
            If the column is null an exception is thrown.
            </summary>
            <typeparam name="T">
            The type of the column to be read. This must correspond to the actual type or data
            corruption will occur. If in doubt, use <see cref="M:Npgsql.NpgsqlBinaryExporter.Read``1(NpgsqlTypes.NpgsqlDbType)"/> to manually
            specify the type.
            </typeparam>
            <returns>The value of the column</returns>
        </member>
        <member name="M:Npgsql.NpgsqlBinaryExporter.Read``1(NpgsqlTypes.NpgsqlDbType)">
            <summary>
            Reads the current column, returns its value according to <paramref name="type"/> and
            moves ahead to the next column.
            If the column is null an exception is thrown.
            </summary>
            <param name="type">
            In some cases <typeparamref name="T"/> isn't enough to infer the data type coming in from the
            database. This parameter and be used to unambiguously specify the type. An example is the JSONB
            type, for which <typeparamref name="T"/> will be a simple string but for which
            <paramref name="type"/> must be specified as <see cref="F:NpgsqlTypes.NpgsqlDbType.Jsonb"/>.
            </param>
            <typeparam name="T">The .NET type of the column to be read.</typeparam>
            <returns>The value of the column</returns>
        </member>
        <member name="P:Npgsql.NpgsqlBinaryExporter.IsNull">
            <summary>
            Returns whether the current column is null.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlBinaryExporter.Skip">
            <summary>
            Skips the current column without interpreting its value.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlBinaryExporter.Cancel">
            <summary>
            Cancels an ongoing export.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlBinaryExporter.Dispose">
            <summary>
            Completes that binary export and sets the connection back to idle state
            </summary>
        </member>
        <member name="T:Npgsql.NpgsqlBinaryImporter">
            <summary>
            Provides an API for a binary COPY FROM operation, a high-performance data import mechanism to
            a PostgreSQL table. Initiated by <see cref="M:Npgsql.NpgsqlConnection.BeginBinaryImport(System.String)"/>
            </summary>
            <remarks>
            See http://www.postgresql.org/docs/current/static/sql-copy.html.
            </remarks>
        </member>
        <member name="F:Npgsql.NpgsqlBinaryImporter._column">
            <summary>
            The number of columns in the current (not-yet-written) row.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlBinaryImporter.NumColumns">
            <summary>
            The number of columns, as returned from the backend in the CopyInResponse.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlBinaryImporter.StartRow">
            <summary>
            Starts writing a single row, must be invoked before writing any columns.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlBinaryImporter.Write``1(``0)">
            <summary>
            Writes a single column in the current row.
            </summary>
            <param name="value">The value to be written</param>
            <typeparam name="T">
            The type of the column to be written. This must correspond to the actual type or data
            corruption will occur. If in doubt, use <see cref="M:Npgsql.NpgsqlBinaryImporter.Write``1(``0,NpgsqlTypes.NpgsqlDbType)"/> to manually
            specify the type.
            </typeparam>
        </member>
        <member name="M:Npgsql.NpgsqlBinaryImporter.Write``1(``0,NpgsqlTypes.NpgsqlDbType)">
            <summary>
            Writes a single column in the current row as type <paramref name="npgsqlDbType"/>.
            </summary>
            <param name="value">The value to be written</param>
            <param name="npgsqlDbType">
            In some cases <typeparamref name="T"/> isn't enough to infer the data type to be written to
            the database. This parameter and be used to unambiguously specify the type. An example is
            the JSONB type, for which <typeparamref name="T"/> will be a simple string but for which
            <paramref name="npgsqlDbType"/> must be specified as <see cref="F:NpgsqlTypes.NpgsqlDbType.Jsonb"/>.
            </param>
            <typeparam name="T">The .NET type of the column to be written.</typeparam>
        </member>
        <member name="M:Npgsql.NpgsqlBinaryImporter.Write``1(``0,System.String)">
            <summary>
            Writes a single column in the current row as type <paramref name="dataTypeName"/>.
            </summary>
            <param name="value">The value to be written</param>
            <param name="dataTypeName">
            In some cases <typeparamref name="T"/> isn't enough to infer the data type to be written to
            the database. This parameter and be used to unambiguously specify the type.
            </param>
            <typeparam name="T">The .NET type of the column to be written.</typeparam>
        </member>
        <member name="M:Npgsql.NpgsqlBinaryImporter.WriteNull">
            <summary>
            Writes a single null column value.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlBinaryImporter.WriteRow(System.Object[])">
            <summary>
            Writes an entire row of columns.
            Equivalent to calling <see cref="M:Npgsql.NpgsqlBinaryImporter.StartRow"/>, followed by multiple <see cref="M:Npgsql.NpgsqlBinaryImporter.Write``1(``0)"/>
            on each value.
            </summary>
            <param name="values">An array of column values to be written as a single row</param>
        </member>
        <member name="M:Npgsql.NpgsqlBinaryImporter.Complete">
            <summary>
            Completes the import operation. The writer is unusable after this operation.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlBinaryImporter.Dispose">
            <summary>
            Cancels that binary import and sets the connection back to idle state
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlBinaryImporter.Close">
            <summary>
            Completes the import process and signals to the database to write everything.
            </summary>
        </member>
        <member name="T:Npgsql.NpgsqlCommand">
            <summary>
            Represents a SQL statement or function (stored procedure) to execute
            against a PostgreSQL database. This class cannot be inherited.
            </summary>
        </member>
        <member name="F:Npgsql.NpgsqlCommand._connectorPreparedOn">
            <summary>
            If this command is (explicitly) prepared, references the connector on which the preparation happened.
            Used to detect when the connector was changed (i.e. connection open/close), meaning that the command
            is no longer prepared.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlCommand.Statements">
            <summary>
            Returns details about each statement that this command has executed.
            Is only populated when an Execute* method is called.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlCommand.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Npgsql.NpgsqlCommand">NpgsqlCommand</see> class.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlCommand.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Npgsql.NpgsqlCommand">NpgsqlCommand</see> class with the text of the query.
            </summary>
            <param name="cmdText">The text of the query.</param>
        </member>
        <member name="M:Npgsql.NpgsqlCommand.#ctor(System.String,Npgsql.NpgsqlConnection)">
            <summary>
            Initializes a new instance of the <see cref="T:Npgsql.NpgsqlCommand">NpgsqlCommand</see> class with the text of the query and a <see cref="T:Npgsql.NpgsqlConnection">NpgsqlConnection</see>.
            </summary>
            <param name="cmdText">The text of the query.</param>
            <param name="connection">A <see cref="T:Npgsql.NpgsqlConnection">NpgsqlConnection</see> that represents the connection to a PostgreSQL server.</param>
        </member>
        <member name="M:Npgsql.NpgsqlCommand.#ctor(System.String,Npgsql.NpgsqlConnection,Npgsql.NpgsqlTransaction)">
            <summary>
            Initializes a new instance of the <see cref="T:Npgsql.NpgsqlCommand">NpgsqlCommand</see> class with the text of the query, a <see cref="T:Npgsql.NpgsqlConnection">NpgsqlConnection</see>, and the <see cref="T:Npgsql.NpgsqlTransaction">NpgsqlTransaction</see>.
            </summary>
            <param name="cmdText">The text of the query.</param>
            <param name="connection">A <see cref="T:Npgsql.NpgsqlConnection">NpgsqlConnection</see> that represents the connection to a PostgreSQL server.</param>
            <param name="transaction">The <see cref="T:Npgsql.NpgsqlTransaction">NpgsqlTransaction</see> in which the <see cref="T:Npgsql.NpgsqlCommand">NpgsqlCommand</see> executes.</param>
        </member>
        <member name="P:Npgsql.NpgsqlCommand.CommandText">
            <summary>
            Gets or sets the SQL statement or function (stored procedure) to execute at the data source.
            </summary>
            <value>The Transact-SQL statement or stored procedure to execute. The default is an empty string.</value>
        </member>
        <member name="P:Npgsql.NpgsqlCommand.CommandTimeout">
            <summary>
            Gets or sets the wait time before terminating the attempt  to execute a command and generating an error.
            </summary>
            <value>The time (in seconds) to wait for the command to execute. The default value is 30 seconds.</value>
        </member>
        <member name="P:Npgsql.NpgsqlCommand.CommandType">
            <summary>
            Gets or sets a value indicating how the
            <see cref="P:Npgsql.NpgsqlCommand.CommandText">CommandText</see> property is to be interpreted.
            </summary>
            <value>One of the <see cref="T:System.Data.CommandType">CommandType</see> values. The default is <see cref="T:System.Data.CommandType">CommandType.Text</see>.</value>
        </member>
        <member name="P:Npgsql.NpgsqlCommand.DbConnection">
            <summary>
            DB connection.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlCommand.Connection">
            <summary>
            Gets or sets the <see cref="T:Npgsql.NpgsqlConnection">NpgsqlConnection</see>
            used by this instance of the <see cref="T:Npgsql.NpgsqlCommand">NpgsqlCommand</see>.
            </summary>
            <value>The connection to a data source. The default value is a null reference.</value>
        </member>
        <member name="P:Npgsql.NpgsqlCommand.DesignTimeVisible">
            <summary>
            Design time visible.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlCommand.UpdatedRowSource">
            <summary>
            Gets or sets how command results are applied to the DataRow when used by the
            DbDataAdapter.Update(DataSet) method.
            </summary>
            <value>One of the <see cref="T:System.Data.UpdateRowSource">UpdateRowSource</see> values.</value>
        </member>
        <member name="P:Npgsql.NpgsqlCommand.IsPrepared">
            <summary>
            Returns whether this query will execute as a prepared (compiled) query.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlCommand.AllResultTypesAreUnknown">
            <summary>
            Marks all of the query's result columns as either known or unknown.
            Unknown results column are requested them from PostgreSQL in text format, and Npgsql makes no
            attempt to parse them. They will be accessible as strings only.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlCommand.UnknownResultTypeList">
             <summary>
             Marks the query's result columns as known or unknown, on a column-by-column basis.
             Unknown results column are requested them from PostgreSQL in text format, and Npgsql makes no
             attempt to parse them. They will be accessible as strings only.
             </summary>
             <remarks>
             If the query includes several queries (e.g. SELECT 1; SELECT 2), this will only apply to the first
             one. The rest of the queries will be fetched and parsed as usual.
            
             The array size must correspond exactly to the number of result columns the query returns, or an
             error will be raised.
             </remarks>
        </member>
        <member name="P:Npgsql.NpgsqlCommand.ObjectResultTypes">
            <summary>
            Marks result types to be used when using GetValue on a data reader, on a column-by-column basis.
            Used for Entity Framework 5-6 compability.
            Only primitive numerical types and DateTimeOffset are supported.
            Set the whole array or just a value to null to use default type.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlCommand.State">
            <summary>
            Gets the current state of the connector
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlCommand.CreateDbParameter">
            <summary>
            Creates a new instance of an <see cref="T:System.Data.Common.DbParameter">DbParameter</see> object.
            </summary>
            <returns>An <see cref="T:System.Data.Common.DbParameter">DbParameter</see> object.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlCommand.CreateParameter">
            <summary>
            Creates a new instance of a <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> object.
            </summary>
            <returns>A <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> object.</returns>
        </member>
        <member name="P:Npgsql.NpgsqlCommand.DbParameterCollection">
            <summary>
            DB parameter collection.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlCommand.Parameters">
            <summary>
            Gets the <see cref="T:Npgsql.NpgsqlParameterCollection">NpgsqlParameterCollection</see>.
            </summary>
            <value>The parameters of the SQL statement or function (stored procedure). The default is an empty collection.</value>
        </member>
        <member name="M:Npgsql.NpgsqlCommand.Prepare">
            <summary>
            Creates a server-side prepared statement on the PostgreSQL server.
            This will make repeated future executions of this command much faster.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlCommand.PrepareAsync">
            <summary>
            Creates a server-side prepared statement on the PostgreSQL server.
            This will make repeated future executions of this command much faster.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlCommand.PrepareAsync(System.Threading.CancellationToken)">
            <summary>
            Creates a server-side prepared statement on the PostgreSQL server.
            This will make repeated future executions of this command much faster.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlCommand.Unprepare">
            <summary>
            Unprepares a command, closing server-side statements associated with it.
            Note that this only affects commands explicitly prepared with <see cref="M:Npgsql.NpgsqlCommand.Prepare"/>, not
            automatically prepared statements.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlCommand.ExecuteNonQuery">
            <summary>
            Executes a SQL statement against the connection and returns the number of rows affected.
            </summary>
            <returns>The number of rows affected if known; -1 otherwise.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlCommand.ExecuteNonQueryAsync(System.Threading.CancellationToken)">
            <summary>
            Asynchronous version of <see cref="M:Npgsql.NpgsqlCommand.ExecuteNonQuery"/>
            </summary>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A task representing the asynchronous operation, with the number of rows affected if known; -1 otherwise.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlCommand.ExecuteScalar">
            <summary>
            Executes the query, and returns the first column of the first row
            in the result set returned by the query. Extra columns or rows are ignored.
            </summary>
            <returns>The first column of the first row in the result set,
            or a null reference if the result set is empty.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlCommand.ExecuteScalarAsync(System.Threading.CancellationToken)">
            <summary>
            Asynchronous version of <see cref="M:Npgsql.NpgsqlCommand.ExecuteScalar"/>
            </summary>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A task representing the asynchronous operation, with the first column of the
            first row in the result set, or a null reference if the result set is empty.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlCommand.ExecuteReader">
            <summary>
            Executes the CommandText against the Connection, and returns an DbDataReader.
            </summary>
            <remarks>
            Unlike the ADO.NET method which it replaces, this method returns a Npgsql-specific
            DataReader.
            </remarks>
            <returns>A DbDataReader object.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlCommand.ExecuteReader(System.Data.CommandBehavior)">
            <summary>
            Executes the CommandText against the Connection, and returns an DbDataReader using one
            of the CommandBehavior values.
            </summary>
            <remarks>
            Unlike the ADO.NET method which it replaces, this method returns a Npgsql-specific
            DataReader.
            </remarks>
            <returns>A DbDataReader object.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(System.Data.CommandBehavior,System.Threading.CancellationToken)">
            <summary>
            Executes the command text against the connection.
            </summary>
            <param name="behavior">An instance of <see cref="T:System.Data.CommandBehavior"/>.</param>
            <param name="cancellationToken">A task representing the operation.</param>
            <returns></returns>
        </member>
        <member name="M:Npgsql.NpgsqlCommand.ExecuteDbDataReader(System.Data.CommandBehavior)">
            <summary>
            Executes the command text against the connection.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlCommand.DbTransaction">
            <summary>
            DB transaction.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlCommand.Transaction">
            <summary>
            Gets or sets the <see cref="T:Npgsql.NpgsqlTransaction">NpgsqlTransaction</see>
            within which the <see cref="T:Npgsql.NpgsqlCommand">NpgsqlCommand</see> executes.
            </summary>
            <value>The <see cref="T:Npgsql.NpgsqlTransaction">NpgsqlTransaction</see>.
            The default value is a null reference.</value>
        </member>
        <member name="M:Npgsql.NpgsqlCommand.Cancel">
            <summary>
            Attempts to cancel the execution of a <see cref="T:Npgsql.NpgsqlCommand">NpgsqlCommand</see>.
            </summary>
            <remarks>As per the specs, no exception will be thrown by this method in case of failure</remarks>
        </member>
        <member name="M:Npgsql.NpgsqlCommand.Dispose(System.Boolean)">
            <summary>
            Releases the resources used by the <see cref="T:Npgsql.NpgsqlCommand">NpgsqlCommand</see>.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlCommand.FixupRowDescription(Npgsql.BackendMessages.RowDescriptionMessage,System.Boolean)">
            <summary>
            Fixes up the text/binary flag on result columns.
            Since Prepare() describes a statement rather than a portal, the resulting RowDescription
            will have text format on all result columns. Fix that up.
            </summary>
            <remarks>
            Note that UnknownResultTypeList only applies to the first query, while AllResultTypesAreUnknown applies
            to all of them.
            </remarks>
        </member>
        <member name="M:Npgsql.NpgsqlCommand.System#ICloneable#Clone">
            <summary>
            Create a new command based on this one.
            </summary>
            <returns>A new NpgsqlCommand object.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlCommand.Clone">
            <summary>
            Create a new command based on this one.
            </summary>
            <returns>A new NpgsqlCommand object.</returns>
        </member>
        <member name="T:Npgsql.NpgsqlCommandBuilder">
            <summary>
             This class is responsible to create database commands for automatic insert, update and delete operations.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlCommandBuilder.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Npgsql.NpgsqlCommandBuilder"/> class.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlCommandBuilder.#ctor(Npgsql.NpgsqlDataAdapter)">
            <summary>
            Initializes a new instance of the <see cref="T:Npgsql.NpgsqlCommandBuilder"/> class.
            </summary>
            <param name="adapter">The adapter.</param>
        </member>
        <member name="P:Npgsql.NpgsqlCommandBuilder.QuotePrefix">
            <summary>
            Gets or sets the beginning character or characters to use when specifying database objects (for example, tables or columns) whose names contain characters such as spaces or reserved tokens.
            </summary>
            <returns>
            The beginning character or characters to use. The default is an empty string.
              </returns>
              <PermissionSet>
              <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
              </PermissionSet>
        </member>
        <member name="P:Npgsql.NpgsqlCommandBuilder.QuoteSuffix">
            <summary>
            Gets or sets the ending character or characters to use when specifying database objects (for example, tables or columns) whose names contain characters such as spaces or reserved tokens.
            </summary>
            <returns>
            The ending character or characters to use. The default is an empty string.
              </returns>
              <PermissionSet>
              <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
              </PermissionSet>
        </member>
        <member name="M:Npgsql.NpgsqlCommandBuilder.DeriveParameters(Npgsql.NpgsqlCommand)">
            <summary>
            
             This method is reponsible to derive the command parameter list with values obtained from function definition.
             It clears the Parameters collection of command. Also, if there is any parameter type which is not supported by Npgsql, an InvalidOperationException will be thrown.
             Parameters name will be parameter1, parameter2, ... for CommandType.StoredProcedure and named after the placeholder for CommandType.Text
            </summary>
             <param name="command">NpgsqlCommand whose function parameters will be obtained.</param>
        </member>
        <member name="M:Npgsql.NpgsqlCommandBuilder.GetInsertCommand">
            <summary>
            Gets the automatically generated <see cref="T:Npgsql.NpgsqlCommand"/> object required
            to perform insertions at the data source.
            </summary>
            <returns>
            The automatically generated <see cref="T:Npgsql.NpgsqlCommand"/> object required to perform insertions.
            </returns>
        </member>
        <member name="M:Npgsql.NpgsqlCommandBuilder.GetInsertCommand(System.Boolean)">
            <summary>
            Gets the automatically generated <see cref="T:Npgsql.NpgsqlCommand"/> object required to perform insertions
            at the data source, optionally using columns for parameter names.
            </summary>
            <param name="useColumnsForParameterNames">
            If <c>true</c>, generate parameter names matching column names, if possible.
            If <c>false</c>, generate @p1, @p2, and so on.
            </param>
            <returns>
            The automatically generated <see cref="T:Npgsql.NpgsqlCommand"/> object required to perform insertions.
            </returns>
        </member>
        <member name="M:Npgsql.NpgsqlCommandBuilder.GetUpdateCommand">
            <summary>
            Gets the automatically generated System.Data.Common.DbCommand object required
            to perform updates at the data source.
            </summary>
            <returns>
            The automatically generated System.Data.Common.DbCommand object required to perform updates.
            </returns>
        </member>
        <member name="M:Npgsql.NpgsqlCommandBuilder.GetUpdateCommand(System.Boolean)">
            <summary>
            Gets the automatically generated <see cref="T:Npgsql.NpgsqlCommand"/> object required to perform updates
            at the data source, optionally using columns for parameter names.
            </summary>
            <param name="useColumnsForParameterNames">
            If <c>true</c>, generate parameter names matching column names, if possible.
            If <c>false</c>, generate @p1, @p2, and so on.
            </param>
            <returns>
            The automatically generated <see cref="T:Npgsql.NpgsqlCommand"/> object required to perform updates.
            </returns>
        </member>
        <member name="M:Npgsql.NpgsqlCommandBuilder.GetDeleteCommand">
            <summary>
            Gets the automatically generated System.Data.Common.DbCommand object required
            to perform deletions at the data source.
            </summary>
            <returns>
            The automatically generated System.Data.Common.DbCommand object required to perform deletions.
            </returns>
        </member>
        <member name="M:Npgsql.NpgsqlCommandBuilder.GetDeleteCommand(System.Boolean)">
            <summary>
            Gets the automatically generated <see cref="T:Npgsql.NpgsqlCommand"/> object required to perform deletions
            at the data source, optionally using columns for parameter names.
            </summary>
            <param name="useColumnsForParameterNames">
            If <c>true</c>, generate parameter names matching column names, if possible.
            If <c>false</c>, generate @p1, @p2, and so on.
            </param>
            <returns>
            The automatically generated <see cref="T:Npgsql.NpgsqlCommand"/> object required to perform deletions.
            </returns>
        </member>
        <member name="M:Npgsql.NpgsqlCommandBuilder.ApplyParameterInfo(System.Data.Common.DbParameter,System.Data.DataRow,System.Data.StatementType,System.Boolean)">
            <summary>
            Applies the parameter information.
            </summary>
            <param name="p">The parameter.</param>
            <param name="row">The row.</param>
            <param name="statementType">Type of the statement.</param>
            <param name="whereClause">if set to <c>true</c> [where clause].</param>
        </member>
        <member name="M:Npgsql.NpgsqlCommandBuilder.GetParameterName(System.Int32)">
            <summary>
            Returns the name of the specified parameter in the format of @p#.
            </summary>
            <param name="parameterOrdinal">The number to be included as part of the parameter's name..</param>
            <returns>
            The name of the parameter with the specified number appended as part of the parameter name.
            </returns>
        </member>
        <member name="M:Npgsql.NpgsqlCommandBuilder.GetParameterName(System.String)">
            <summary>
            Returns the full parameter name, given the partial parameter name.
            </summary>
            <param name="parameterName">The partial name of the parameter.</param>
            <returns>
            The full parameter name corresponding to the partial parameter name requested.
            </returns>
        </member>
        <member name="M:Npgsql.NpgsqlCommandBuilder.GetParameterPlaceholder(System.Int32)">
            <summary>
            Returns the placeholder for the parameter in the associated SQL statement.
            </summary>
            <param name="parameterOrdinal">The number to be included as part of the parameter's name.</param>
            <returns>
            The name of the parameter with the specified number appended.
            </returns>
        </member>
        <member name="M:Npgsql.NpgsqlCommandBuilder.SetRowUpdatingHandler(System.Data.Common.DbDataAdapter)">
            <summary>
            Registers the <see cref="T:Npgsql.NpgsqlCommandBuilder" /> to handle the <see cref="E:Npgsql.NpgsqlDataAdapter.RowUpdating"/> event for a <see cref="T:Npgsql.NpgsqlDataAdapter" />.
            </summary>
            <param name="adapter">The <see cref="T:System.Data.Common.DbDataAdapter" /> to be used for the update.</param>
        </member>
        <member name="M:Npgsql.NpgsqlCommandBuilder.RowUpdatingHandler(System.Object,Npgsql.NpgsqlRowUpdatingEventArgs)">
            <summary>
            Adds an event handler for the <see cref="E:Npgsql.NpgsqlDataAdapter.RowUpdating"/> event.
            </summary>
            <param name="sender">The sender</param>
            <param name="e">A <see cref="T:Npgsql.NpgsqlRowUpdatingEventArgs"/> instance containing information about the event.</param>
        </member>
        <member name="M:Npgsql.NpgsqlCommandBuilder.QuoteIdentifier(System.String)">
            <summary>
            Given an unquoted identifier in the correct catalog case, returns the correct quoted form of that identifier, including properly escaping any embedded quotes in the identifier.
            </summary>
            <param name="unquotedIdentifier">The original unquoted identifier.</param>
            <returns>
            The quoted version of the identifier. Embedded quotes within the identifier are properly escaped.
            </returns>
            <PermissionSet>
              <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
              </PermissionSet>
            <exception cref="T:System.ArgumentNullException">Unquoted identifier parameter cannot be null</exception>
        </member>
        <member name="M:Npgsql.NpgsqlCommandBuilder.UnquoteIdentifier(System.String)">
            <summary>
            Given a quoted identifier, returns the correct unquoted form of that identifier, including properly un-escaping any embedded quotes in the identifier.
            </summary>
            <param name="quotedIdentifier">The identifier that will have its embedded quotes removed.</param>
            <returns>
            The unquoted identifier, with embedded quotes properly un-escaped.
            </returns>
            <PermissionSet>
              <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
              </PermissionSet>
            <exception cref="T:System.ArgumentNullException">Quoted identifier parameter cannot be null</exception>
        </member>
        <member name="T:Npgsql.NpgsqlConnection">
            <summary>
            This class represents a connection to a PostgreSQL server.
            </summary>
        </member>
        <member name="F:Npgsql.NpgsqlConnection._userFacingConnectionString">
            <summary>
            The connection string, without the password after open (unless Persist Security Info=true)
            </summary>
        </member>
        <member name="F:Npgsql.NpgsqlConnection._connectionString">
            <summary>
            The original connection string provided by the user, including the password.
            </summary>
        </member>
        <member name="F:Npgsql.NpgsqlConnection.Connector">
            <summary>
            The connector object connected to the backend.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnection.Settings">
            <summary>
            The parsed connection string set by the user
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnection.GlobalTypeMapper">
            <summary>
            The global type mapper, which contains defaults used by all new connections.
            Modify mappings on this mapper to affect your entire application.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnection.TypeMapper">
            <summary>
            The connection-specific type mapper - all modifications affect this connection only,
            and are lost when it is closed.
            </summary>
        </member>
        <member name="F:Npgsql.NpgsqlConnection.DefaultPort">
            
             <summary>
             The default TCP/IP port for PostgreSQL.
             </summary>
        </member>
        <member name="F:Npgsql.NpgsqlConnection.TimeoutLimit">
            <summary>
            Maximum value for connection timeout.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlConnection.#ctor">
            <summary>
            Initializes a new instance of the
            <see cref="T:Npgsql.NpgsqlConnection">NpgsqlConnection</see> class.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlConnection.#ctor(System.String)">
            <summary>
            Initializes a new instance of <see cref="T:Npgsql.NpgsqlConnection"/> with the given connection string.
            </summary>
            <param name="connectionString">The connection used to open the PostgreSQL database.</param>
        </member>
        <member name="M:Npgsql.NpgsqlConnection.Open">
            <summary>
            Opens a database connection with the property settings specified by the
            <see cref="P:Npgsql.NpgsqlConnection.ConnectionString">ConnectionString</see>.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlConnection.OpenAsync(System.Threading.CancellationToken)">
            <summary>
            This is the asynchronous version of <see cref="M:Npgsql.NpgsqlConnection.Open"/>.
            </summary>
            <remarks>
            Do not invoke other methods and properties of the <see cref="T:Npgsql.NpgsqlConnection"/> object until the returned Task is complete.
            </remarks>
            <param name="cancellationToken">The cancellation instruction.</param>
            <returns>A task representing the asynchronous operation.</returns>
        </member>
        <member name="P:Npgsql.NpgsqlConnection.ConnectionString">
            <summary>
            Gets or sets the string used to connect to a PostgreSQL database. See the manual for details.
            </summary>
            <value>The connection string that includes the server name,
            the database name, and other parameters needed to establish
            the initial connection. The default value is an empty string.
            </value>
        </member>
        <member name="P:Npgsql.NpgsqlConnection.Host">
            <summary>
            Backend server host name.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnection.Port">
            <summary>
            Backend server port.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnection.ConnectionTimeout">
            <summary>
            Gets the time to wait while trying to establish a connection
            before terminating the attempt and generating an error.
            </summary>
            <value>The time (in seconds) to wait for a connection to open. The default value is 15 seconds.</value>
        </member>
        <member name="P:Npgsql.NpgsqlConnection.CommandTimeout">
            <summary>
            Gets the time to wait while trying to execute a command
            before terminating the attempt and generating an error.
            </summary>
            <value>The time (in seconds) to wait for a command to complete. The default value is 20 seconds.</value>
        </member>
        <member name="P:Npgsql.NpgsqlConnection.Database">
            <summary>
             Gets the name of the current database or the database to be used after a connection is opened.
             </summary>
             <value>The name of the current database or the name of the database to be
             used after a connection is opened. The default value is the empty string.</value>
        </member>
        <member name="P:Npgsql.NpgsqlConnection.DataSource">
            <summary>
            Gets the string identifying the database server (host and port)
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnection.IntegratedSecurity">
            <summary>
            Whether to use Windows integrated security to log in.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnection.UserName">
            <summary>
            User name.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnection.FullState">
            <summary>
            Gets the current state of the connection.
            </summary>
            <value>A bitwise combination of the <see cref="T:System.Data.ConnectionState">ConnectionState</see> values. The default is <b>Closed</b>.</value>
        </member>
        <member name="P:Npgsql.NpgsqlConnection.State">
            <summary>
            Gets whether the current state of the connection is Open or Closed
            </summary>
            <value>ConnectionState.Open, ConnectionState.Closed or ConnectionState.Connecting</value>
        </member>
        <member name="M:Npgsql.NpgsqlConnection.CreateDbCommand">
            <summary>
            Creates and returns a <see cref="T:System.Data.Common.DbCommand">DbCommand</see>
            object associated with the <see cref="T:System.Data.Common.DbConnection">IDbConnection</see>.
            </summary>
            <returns>A <see cref="T:System.Data.Common.DbCommand">DbCommand</see> object.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlConnection.CreateCommand">
            <summary>
            Creates and returns a <see cref="T:Npgsql.NpgsqlCommand">NpgsqlCommand</see>
            object associated with the <see cref="T:Npgsql.NpgsqlConnection">NpgsqlConnection</see>.
            </summary>
            <returns>A <see cref="T:Npgsql.NpgsqlCommand">NpgsqlCommand</see> object.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlConnection.BeginDbTransaction(System.Data.IsolationLevel)">
            <summary>
            Begins a database transaction with the specified isolation level.
            </summary>
            <param name="isolationLevel">The <see cref="T:System.Data.IsolationLevel">isolation level</see> under which the transaction should run.</param>
            <returns>An <see cref="T:System.Data.Common.DbTransaction">DbTransaction</see>
            object representing the new transaction.</returns>
            <remarks>
            Currently the IsolationLevel ReadCommitted and Serializable are supported by the PostgreSQL backend.
            There's no support for nested transactions.
            </remarks>
        </member>
        <member name="M:Npgsql.NpgsqlConnection.BeginTransaction">
            <summary>
            Begins a database transaction.
            </summary>
            <returns>A <see cref="T:Npgsql.NpgsqlTransaction">NpgsqlTransaction</see>
            object representing the new transaction.</returns>
            <remarks>
            Currently there's no support for nested transactions. Transactions created by this method will have Read Committed isolation level.
            </remarks>
        </member>
        <member name="M:Npgsql.NpgsqlConnection.BeginTransaction(System.Data.IsolationLevel)">
            <summary>
            Begins a database transaction with the specified isolation level.
            </summary>
            <param name="level">The <see cref="T:System.Data.IsolationLevel">isolation level</see> under which the transaction should run.</param>
            <returns>A <see cref="T:Npgsql.NpgsqlTransaction">NpgsqlTransaction</see>
            object representing the new transaction.</returns>
            <remarks>
            Currently the IsolationLevel ReadCommitted and Serializable are supported by the PostgreSQL backend.
            There's no support for nested transactions.
            </remarks>
        </member>
        <member name="M:Npgsql.NpgsqlConnection.EnlistTransaction(System.Transactions.Transaction)">
            <summary>
            Enlist transation.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlConnection.Close">
            <summary>
            releases the connection to the database.  If the connection is pooled, it will be
            made available for re-use.  If it is non-pooled, the actual connection will be shutdown.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlConnection.Dispose(System.Boolean)">
            <summary>
            Releases all resources used by the
            <see cref="T:Npgsql.NpgsqlConnection">NpgsqlConnection</see>.
            </summary>
            <param name="disposing"><b>true</b> when called from Dispose();
            <b>false</b> when being called from the finalizer.</param>
        </member>
        <member name="E:Npgsql.NpgsqlConnection.Notice">
             <summary>
             Fires when PostgreSQL notices are received from PostgreSQL.
             </summary>
             <remarks>
             PostgreSQL notices are non-critical messages generated by PostgreSQL, either as a result of a user query
             (e.g. as a warning or informational notice), or due to outside activity (e.g. if the database administrator
             initiates a "fast" database shutdown).
            
             Note that notices are very different from notifications (see the <see cref="E:Npgsql.NpgsqlConnection.Notification"/> event).
             </remarks>
        </member>
        <member name="E:Npgsql.NpgsqlConnection.Notification">
             <summary>
             Fires when PostgreSQL notifications are received from PostgreSQL.
             </summary>
             <remarks>
             PostgreSQL notifications are sent when your connection has registered for notifications on a specific channel via the
             LISTEN command. NOTIFY can be used to generate such notifications, allowing for an inter-connection communication channel.
            
             Note that notifications are very different from notices (see the <see cref="E:Npgsql.NpgsqlConnection.Notice"/> event).
             </remarks>
        </member>
        <member name="P:Npgsql.NpgsqlConnection.IsSecure">
            <summary>
            Returns whether SSL is being used for the connection.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnection.ProvideClientCertificatesCallback">
            <summary>
            Selects the local Secure Sockets Layer (SSL) certificate used for authentication.
            </summary>
            <remarks>
            See <see href="https://msdn.microsoft.com/en-us/library/system.net.security.localcertificateselectioncallback(v=vs.110).aspx"/>
            </remarks>
        </member>
        <member name="P:Npgsql.NpgsqlConnection.UserCertificateValidationCallback">
            <summary>
            Verifies the remote Secure Sockets Layer (SSL) certificate used for authentication.
            Ignored if <see cref="P:Npgsql.NpgsqlConnectionStringBuilder.TrustServerCertificate"/> is set.
            </summary>
            <remarks>
            See <see href="https://msdn.microsoft.com/en-us/library/system.net.security.remotecertificatevalidationcallback(v=vs.110).aspx"/>
            </remarks>
        </member>
        <member name="P:Npgsql.NpgsqlConnection.PostgreSqlVersion">
            <summary>
            Version of the PostgreSQL backend.
            This can only be called when there is an active connection.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnection.ServerVersion">
            <summary>
            PostgreSQL server version.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnection.ProcessID">
            <summary>
            Process id of backend server.
            This can only be called when there is an active connection.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnection.HasIntegerDateTimes">
            <summary>
            Reports whether the backend uses the newer integer timestamp representation.
            Note that the old floating point representation is not supported.
            Meant for use by type plugins (e.g. Nodatime)
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnection.Timezone">
            <summary>
            The connection's timezone as reported by PostgreSQL, in the IANA/Olson database format.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnection.PostgresParameters">
            <summary>
            Holds all PostgreSQL parameters received for this connection. Is updated if the values change
            (e.g. as a result of a SET command).
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlConnection.BeginBinaryImport(System.String)">
            <summary>
            Begins a binary COPY FROM STDIN operation, a high-performance data import mechanism to a PostgreSQL table.
            </summary>
            <param name="copyFromCommand">A COPY FROM STDIN SQL command</param>
            <returns>A <see cref="T:Npgsql.NpgsqlBinaryImporter"/> which can be used to write rows and columns</returns>
            <remarks>
            See http://www.postgresql.org/docs/current/static/sql-copy.html.
            </remarks>
        </member>
        <member name="M:Npgsql.NpgsqlConnection.BeginBinaryExport(System.String)">
            <summary>
            Begins a binary COPY TO STDOUT operation, a high-performance data export mechanism from a PostgreSQL table.
            </summary>
            <param name="copyToCommand">A COPY TO STDOUT SQL command</param>
            <returns>A <see cref="T:Npgsql.NpgsqlBinaryExporter"/> which can be used to read rows and columns</returns>
            <remarks>
            See http://www.postgresql.org/docs/current/static/sql-copy.html.
            </remarks>
        </member>
        <member name="M:Npgsql.NpgsqlConnection.BeginTextImport(System.String)">
            <summary>
            Begins a textual COPY FROM STDIN operation, a data import mechanism to a PostgreSQL table.
            It is the user's responsibility to send the textual input according to the format specified
            in <paramref name="copyFromCommand"/>.
            </summary>
            <param name="copyFromCommand">A COPY FROM STDIN SQL command</param>
            <returns>
            A TextWriter that can be used to send textual data.</returns>
            <remarks>
            See http://www.postgresql.org/docs/current/static/sql-copy.html.
            </remarks>
        </member>
        <member name="M:Npgsql.NpgsqlConnection.BeginTextExport(System.String)">
            <summary>
            Begins a textual COPY TO STDOUT operation, a data export mechanism from a PostgreSQL table.
            It is the user's responsibility to parse the textual input according to the format specified
            in <paramref name="copyToCommand"/>.
            </summary>
            <param name="copyToCommand">A COPY TO STDOUT SQL command</param>
            <returns>
            A TextReader that can be used to read textual data.</returns>
            <remarks>
            See http://www.postgresql.org/docs/current/static/sql-copy.html.
            </remarks>
        </member>
        <member name="M:Npgsql.NpgsqlConnection.BeginRawBinaryCopy(System.String)">
            <summary>
            Begins a raw binary COPY operation (TO STDOUT or FROM STDIN), a high-performance data export/import mechanism to a PostgreSQL table.
            Note that unlike the other COPY API methods, <see cref="M:Npgsql.NpgsqlConnection.BeginRawBinaryCopy(System.String)"/> doesn't implement any encoding/decoding
            and is unsuitable for structured import/export operation. It is useful mainly for exporting a table as an opaque
            blob, for the purpose of importing it back later.
            </summary>
            <param name="copyCommand">A COPY TO STDOUT or COPY FROM STDIN SQL command</param>
            <returns>A <see cref="T:Npgsql.NpgsqlRawCopyStream"/> that can be used to read or write raw binary data.</returns>
            <remarks>
            See http://www.postgresql.org/docs/current/static/sql-copy.html.
            </remarks>
        </member>
        <member name="M:Npgsql.NpgsqlConnection.MapEnum``1(System.String,Npgsql.INpgsqlNameTranslator)">
             <summary>
             Maps a CLR enum to a PostgreSQL enum type for use with this connection.
             </summary>
             <remarks>
             CLR enum labels are mapped by name to PostgreSQL enum labels.
             The translation strategy can be controlled by the <paramref name="nameTranslator"/> parameter,
             which defaults to <see cref="T:Npgsql.NameTranslation.NpgsqlSnakeCaseNameTranslator"/>.
             You can also use the <see cref="T:NpgsqlTypes.PgNameAttribute"/> on your enum fields to manually specify a PostgreSQL enum label.
             If there is a discrepancy between the .NET and database labels while an enum is read or written,
             an exception will be raised.
            
             Can only be invoked on an open connection; if the connection is closed the mapping is lost.
            
             To avoid mapping the type for each connection, use the <see cref="M:Npgsql.NpgsqlConnection.MapEnumGlobally``1(System.String,Npgsql.INpgsqlNameTranslator)"/> method.
             </remarks>
             <param name="pgName">
             A PostgreSQL type name for the corresponding enum type in the database.
             If null, the name translator given in <paramref name="nameTranslator"/>will be used.
             </param>
             <param name="nameTranslator">
             A component which will be used to translate CLR names (e.g. SomeClass) into database names (e.g. some_class).
             Defaults to <see cref="T:Npgsql.NameTranslation.NpgsqlSnakeCaseNameTranslator"/>
             </param>
             <typeparam name="TEnum">The .NET enum type to be mapped</typeparam>
        </member>
        <member name="M:Npgsql.NpgsqlConnection.MapEnumGlobally``1(System.String,Npgsql.INpgsqlNameTranslator)">
             <summary>
             Maps a CLR enum to a PostgreSQL enum type for use with all connections created from now on. Existing connections aren't affected.
             </summary>
             <remarks>
             CLR enum labels are mapped by name to PostgreSQL enum labels.
             The translation strategy can be controlled by the <paramref name="nameTranslator"/> parameter,
             which defaults to <see cref="T:Npgsql.NameTranslation.NpgsqlSnakeCaseNameTranslator"/>.
             You can also use the <see cref="T:NpgsqlTypes.PgNameAttribute"/> on your enum fields to manually specify a PostgreSQL enum label.
             If there is a discrepancy between the .NET and database labels while an enum is read or written,
             an exception will be raised.
            
             To map the type for a specific connection, use the <see cref="M:Npgsql.NpgsqlConnection.MapEnum``1(System.String,Npgsql.INpgsqlNameTranslator)"/> method.
             </remarks>
             <param name="pgName">
             A PostgreSQL type name for the corresponding enum type in the database.
             If null, the name translator given in <paramref name="nameTranslator"/>will be used.
             </param>
             <param name="nameTranslator">
             A component which will be used to translate CLR names (e.g. SomeClass) into database names (e.g. some_class).
             Defaults to <see cref="T:Npgsql.NameTranslation.NpgsqlSnakeCaseNameTranslator"/>
             </param>
             <typeparam name="TEnum">The .NET enum type to be mapped</typeparam>
        </member>
        <member name="M:Npgsql.NpgsqlConnection.UnmapEnumGlobally``1(System.String,Npgsql.INpgsqlNameTranslator)">
            <summary>
            Removes a previous global enum mapping.
            </summary>
            <param name="pgName">
            A PostgreSQL type name for the corresponding enum type in the database.
            If null, the name translator given in <paramref name="nameTranslator"/>will be used.
            </param>
            <param name="nameTranslator">
            A component which will be used to translate CLR names (e.g. SomeClass) into database names (e.g. some_class).
            Defaults to <see cref="T:Npgsql.NameTranslation.NpgsqlSnakeCaseNameTranslator"/>
            </param>
        </member>
        <member name="M:Npgsql.NpgsqlConnection.MapComposite``1(System.String,Npgsql.INpgsqlNameTranslator)">
             <summary>
             Maps a CLR type to a PostgreSQL composite type for use with this connection.
             </summary>
             <remarks>
             CLR fields and properties by string to PostgreSQL enum labels.
             The translation strategy can be controlled by the <paramref name="nameTranslator"/> parameter,
             which defaults to <see cref="T:Npgsql.NameTranslation.NpgsqlSnakeCaseNameTranslator"/>.
             You can also use the <see cref="T:NpgsqlTypes.PgNameAttribute"/> on your members to manually specify a PostgreSQL enum label.
             If there is a discrepancy between the .NET and database labels while a composite is read or written,
             an exception will be raised.
            
             Can only be invoked on an open connection; if the connection is closed the mapping is lost.
            
             To avoid mapping the type for each connection, use the <see cref="M:Npgsql.NpgsqlConnection.MapCompositeGlobally``1(System.String,Npgsql.INpgsqlNameTranslator)"/> method.
             </remarks>
             <param name="pgName">
             A PostgreSQL type name for the corresponding enum type in the database.
             If null, the name translator given in <paramref name="nameTranslator"/>will be used.
             </param>
             <param name="nameTranslator">
             A component which will be used to translate CLR names (e.g. SomeClass) into database names (e.g. some_class).
             Defaults to <see cref="T:Npgsql.NameTranslation.NpgsqlSnakeCaseNameTranslator"/>
             </param>
             <typeparam name="T">The .NET type to be mapped</typeparam>
        </member>
        <member name="M:Npgsql.NpgsqlConnection.MapCompositeGlobally``1(System.String,Npgsql.INpgsqlNameTranslator)">
             <summary>
             Maps a CLR type to a PostgreSQL composite type for use with all connections created from now on. Existing connections aren't affected.
             </summary>
             <remarks>
             CLR fields and properties by string to PostgreSQL enum labels.
             The translation strategy can be controlled by the <paramref name="nameTranslator"/> parameter,
             which defaults to <see cref="T:Npgsql.NameTranslation.NpgsqlSnakeCaseNameTranslator"/>.
             You can also use the <see cref="T:NpgsqlTypes.PgNameAttribute"/> on your members to manually specify a PostgreSQL enum label.
             If there is a discrepancy between the .NET and database labels while a composite is read or written,
             an exception will be raised.
            
             To map the type for a specific connection, use the <see cref="M:Npgsql.NpgsqlConnection.MapEnum``1(System.String,Npgsql.INpgsqlNameTranslator)"/> method.
             </remarks>
             <param name="pgName">
             A PostgreSQL type name for the corresponding enum type in the database.
             If null, the name translator given in <paramref name="nameTranslator"/>will be used.
             </param>
             <param name="nameTranslator">
             A component which will be used to translate CLR names (e.g. SomeClass) into database names (e.g. some_class).
             Defaults to <see cref="T:Npgsql.NameTranslation.NpgsqlSnakeCaseNameTranslator"/>
             </param>
             <typeparam name="T">The .NET type to be mapped</typeparam>
        </member>
        <member name="M:Npgsql.NpgsqlConnection.UnmapCompositeGlobally``1(System.String,Npgsql.INpgsqlNameTranslator)">
            <summary>
            Removes a previous global enum mapping.
            </summary>
            <param name="pgName">
            A PostgreSQL type name for the corresponding enum type in the database.
            If null, the name translator given in <paramref name="nameTranslator"/>will be used.
            </param>
            <param name="nameTranslator">
            A component which will be used to translate CLR names (e.g. SomeClass) into database names (e.g. some_class).
            Defaults to <see cref="T:Npgsql.NameTranslation.NpgsqlSnakeCaseNameTranslator"/>
            </param>
        </member>
        <member name="M:Npgsql.NpgsqlConnection.Wait(System.Int32)">
            <summary>
            Waits until an asynchronous PostgreSQL messages (e.g. a notification) arrives, and
            exits immediately. The asynchronous message is delivered via the normal events
            (<see cref="E:Npgsql.NpgsqlConnection.Notification"/>, <see cref="E:Npgsql.NpgsqlConnection.Notice"/>).
            </summary>
            <param name="timeout">
            The time-out value, in milliseconds, passed to <see cref="P:System.Net.Sockets.Socket.ReceiveTimeout"/>.
            The default value is 0, which indicates an infinite time-out period.
            Specifying -1 also indicates an infinite time-out period.
            </param>
            <returns>true if an asynchronous message was received, false if timed out.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlConnection.Wait(System.TimeSpan)">
            <summary>
            Waits until an asynchronous PostgreSQL messages (e.g. a notification) arrives, and
            exits immediately. The asynchronous message is delivered via the normal events
            (<see cref="E:Npgsql.NpgsqlConnection.Notification"/>, <see cref="E:Npgsql.NpgsqlConnection.Notice"/>).
            </summary>
            <param name="timeout">
            The time-out value is passed to <see cref="P:System.Net.Sockets.Socket.ReceiveTimeout"/>.
            </param>
            <returns>true if an asynchronous message was received, false if timed out.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlConnection.Wait">
            <summary>
            Waits until an asynchronous PostgreSQL messages (e.g. a notification) arrives, and
            exits immediately. The asynchronous message is delivered via the normal events
            (<see cref="E:Npgsql.NpgsqlConnection.Notification"/>, <see cref="E:Npgsql.NpgsqlConnection.Notice"/>).
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlConnection.WaitAsync(System.Threading.CancellationToken)">
            <summary>
            Waits asynchronously until an asynchronous PostgreSQL messages (e.g. a notification)
            arrives, and exits immediately. The asynchronous message is delivered via the normal events
            (<see cref="E:Npgsql.NpgsqlConnection.Notification"/>, <see cref="E:Npgsql.NpgsqlConnection.Notice"/>).
            CancelationToken can not cancel wait operation if underlying NetworkStream does not support it
            (see https://stackoverflow.com/questions/12421989/networkstream-readasync-with-a-cancellation-token-never-cancels ).
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlConnection.WaitAsync">
            <summary>
            Waits asynchronously until an asynchronous PostgreSQL messages (e.g. a notification)
            arrives, and exits immediately. The asynchronous message is delivered via the normal events
            (<see cref="E:Npgsql.NpgsqlConnection.Notification"/>, <see cref="E:Npgsql.NpgsqlConnection.Notice"/>).
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlConnection.GetSchema">
            <summary>
            Returns the supported collections
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlConnection.GetSchema(System.String)">
            <summary>
            Returns the schema collection specified by the collection name.
            </summary>
            <param name="collectionName">The collection name.</param>
            <returns>The collection specified.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlConnection.GetSchema(System.String,System.String[])">
            <summary>
            Returns the schema collection specified by the collection name filtered by the restrictions.
            </summary>
            <param name="collectionName">The collection name.</param>
            <param name="restrictions">
            The restriction values to filter the results.  A description of the restrictions is contained
            in the Restrictions collection.
            </param>
            <returns>The collection specified.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlConnection.System#ICloneable#Clone">
            <summary>
            Creates a closed connection with the connection string and authentication details of this message.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlConnection.CloneWith(System.String)">
            <summary>
            Clones this connection, replacing its connection string with the given one.
            This allows creating a new connection with the same security information
            (password, SSL callbacks) while changing other connection parameters (e.g.
            database or pooling)
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlConnection.ChangeDatabase(System.String)">
            <summary>
            This method changes the current database by disconnecting from the actual
            database and connecting to the specified.
            </summary>
            <param name="dbName">The name of the database to use in place of the current database.</param>
        </member>
        <member name="P:Npgsql.NpgsqlConnection.DbProviderFactory">
            <summary>
            DB provider factory.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlConnection.ClearPool(Npgsql.NpgsqlConnection)">
            <summary>
            Clear connection pool.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlConnection.ClearAllPools">
            <summary>
            Clear all connection pools.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlConnection.UnprepareAll">
            <summary>
            Unprepares all prepared statements on this connection.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlConnection.ReloadTypes">
            <summary>
            Flushes the type cache for this connection's connection string and reloads the types for this connection only.
            Type changes will appear for other connections only after they are re-opened from the pool.
            </summary>
        </member>
        <member name="T:Npgsql.NoticeEventHandler">
            <summary>
            Represents a method that handles the <see cref="E:Npgsql.NpgsqlConnection.Notice"/> event.
            </summary>
            <param name="sender">The source of the event.</param>
            <param name="e">A <see cref="T:Npgsql.NpgsqlNoticeEventArgs"/> that contains the notice information (e.g. message, severity...).</param>
        </member>
        <member name="T:Npgsql.NotificationEventHandler">
            <summary>
            Represents a method that handles the <see cref="E:Npgsql.NpgsqlConnection.Notification"/> event.
            </summary>
            <param name="sender">The source of the event.</param>
            <param name="e">A <see cref="T:Npgsql.NpgsqlNotificationEventArgs"/> that contains the notification payload.</param>
        </member>
        <member name="T:Npgsql.ProvideClientCertificatesCallback">
            <summary>
            Represents the method that allows the application to provide a certificate collection to be used for SSL client authentication
            </summary>
            <param name="certificates">A <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection">X509CertificateCollection</see> to be filled with one or more client certificates.</param>
        </member>
        <member name="T:Npgsql.NpgsqlConnectionStringBuilder">
            <summary>
            Provides a simple way to create and manage the contents of connection strings used by
            the <see cref="T:Npgsql.NpgsqlConnection"/> class.
            </summary>
        </member>
        <member name="F:Npgsql.NpgsqlConnectionStringBuilder.PropertiesByKeyword">
            <summary>
            Makes all valid keywords for a property to that property (e.g. User Name -> Username, UserId -> Username...)
            </summary>
        </member>
        <member name="F:Npgsql.NpgsqlConnectionStringBuilder.PropertyNameToCanonicalKeyword">
            <summary>
            Maps CLR property names (e.g. BufferSize) to their canonical keyword name, which is the
            property's [DisplayName] (e.g. Buffer Size)
            </summary>
        </member>
        <member name="F:Npgsql.NpgsqlConnectionStringBuilder.PropertyDefaults">
            <summary>
            Maps each property to its [DefaultValue]
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlConnectionStringBuilder.#ctor">
            <summary>
            Initializes a new instance of the NpgsqlConnectionStringBuilder class.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlConnectionStringBuilder.#ctor(System.Boolean)">
            <summary>
            Initializes a new instance of the NpgsqlConnectionStringBuilder class, optionally using ODBC rules for quoting values.
            </summary>
            <param name="useOdbcRules">true to use {} to delimit fields; false to use quotation marks.</param>
        </member>
        <member name="M:Npgsql.NpgsqlConnectionStringBuilder.#ctor(System.String)">
            <summary>
            Initializes a new instance of the NpgsqlConnectionStringBuilder class and sets its <see cref="P:System.Data.Common.DbConnectionStringBuilder.ConnectionString"/>.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.Item(System.String)">
            <summary>
            Gets or sets the value associated with the specified key.
            </summary>
            <param name="keyword">The key of the item to get or set.</param>
            <returns>The value associated with the specified key.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlConnectionStringBuilder.Add(System.Collections.Generic.KeyValuePair{System.String,System.Object})">
            <summary>
            Adds an item to the <see cref="T:Npgsql.NpgsqlConnectionStringBuilder"/>.
            </summary>
            <param name="item">The key-value pair to be added.</param>
        </member>
        <member name="M:Npgsql.NpgsqlConnectionStringBuilder.Remove(System.String)">
            <summary>
            Removes the entry with the specified key from the DbConnectionStringBuilder instance.
            </summary>
            <param name="keyword">The key of the key/value pair to be removed from the connection string in this DbConnectionStringBuilder.</param>
            <returns><b>true</b> if the key existed within the connection string and was removed; <b>false</b> if the key did not exist.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlConnectionStringBuilder.Remove(System.Collections.Generic.KeyValuePair{System.String,System.Object})">
            <summary>
            Removes the entry from the DbConnectionStringBuilder instance.
            </summary>
            <param name="item">The key/value pair to be removed from the connection string in this DbConnectionStringBuilder.</param>
            <returns><b>true</b> if the key existed within the connection string and was removed; <b>false</b> if the key did not exist.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlConnectionStringBuilder.Clear">
            <summary>
            Clears the contents of the <see cref="T:Npgsql.NpgsqlConnectionStringBuilder"/> instance.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlConnectionStringBuilder.ContainsKey(System.String)">
            <summary>
            Determines whether the <see cref="T:Npgsql.NpgsqlConnectionStringBuilder"/> contains a specific key.
            </summary>
            <param name="keyword">The key to locate in the <see cref="T:Npgsql.NpgsqlConnectionStringBuilder"/>.</param>
            <returns><b>true</b> if the <see cref="T:Npgsql.NpgsqlConnectionStringBuilder"/> contains an entry with the specified key; otherwise <b>false</b>.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlConnectionStringBuilder.Contains(System.Collections.Generic.KeyValuePair{System.String,System.Object})">
            <summary>
            Determines whether the <see cref="T:Npgsql.NpgsqlConnectionStringBuilder"/> contains a specific key-value pair.
            </summary>
            <param name="item">The itemto locate in the <see cref="T:Npgsql.NpgsqlConnectionStringBuilder"/>.</param>
            <returns><b>true</b> if the <see cref="T:Npgsql.NpgsqlConnectionStringBuilder"/> contains the entry; otherwise <b>false</b>.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlConnectionStringBuilder.TryGetValue(System.String,System.Object@)">
            <summary>
            Retrieves a value corresponding to the supplied key from this <see cref="T:Npgsql.NpgsqlConnectionStringBuilder"/>.
            </summary>
            <param name="keyword">The key of the item to retrieve.</param>
            <param name="value">The value corresponding to the key.</param>
            <returns><b>true</b> if keyword was found within the connection string, <b>false</b> otherwise.</returns>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.Host">
            <summary>
            The hostname or IP address of the PostgreSQL server to connect to.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.Port">
            <summary>
            The TCP/IP port of the PostgreSQL server.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.Database">
            <summary>
             The PostgreSQL database to connect to.
             </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.Username">
            <summary>
            The username to connect with. Not required if using IntegratedSecurity.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.Password">
            <summary>
            The password to connect with. Not required if using IntegratedSecurity.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.Passfile">
            <summary>
            Path to a PostgreSQL password file (PGPASSFILE), from which the password would be taken.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.ApplicationName">
            <summary>
            The optional application name parameter to be sent to the backend during connection initiation.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.Enlist">
            <summary>
            Whether to enlist in an ambient TransactionScope.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.SearchPath">
            <summary>
            Gets or sets the schema search path.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.ClientEncoding">
            <summary>
            Gets or sets the client_encoding parameter.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.Encoding">
            <summary>
            Gets or sets the .NET encoding that will be used to encode/decode PostgreSQL string data.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.Timezone">
            <summary>
            Gets or sets the PostgreSQL session timezone, in Olson/IANA database format.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.SslMode">
            <summary>
            Controls whether SSL is required, disabled or preferred, depending on server support.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.TrustServerCertificate">
            <summary>
            Whether to trust the server certificate without validating it.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.CheckCertificateRevocation">
            <summary>
            Whether to check the certificate revocation list during authentication.
            False by default.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.UseSslStream">
            <summary>
            Npgsql uses its own internal implementation of TLS/SSL. Turn this on to use .NET SslStream instead.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.IntegratedSecurity">
            <summary>
            Whether to use Windows integrated security to log in.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.KerberosServiceName">
            <summary>
            The Kerberos service name to be used for authentication.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.IncludeRealm">
            <summary>
            The Kerberos realm to be used for authentication.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.PersistSecurityInfo">
            <summary>
            Gets or sets a Boolean value that indicates if security-sensitive information, such as the password, is not returned as part of the connection if the connection is open or has ever been in an open state.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.LogParameters">
            <summary>
            When enabled, parameter values are logged when commands are executed. Defaults to false.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.IncludeErrorDetails">
            <summary>
            When enabled, PostgreSQL error details are included on <see cref="P:Npgsql.PostgresException.Detail" /> and
            <see cref="P:Npgsql.PostgresNotice.Detail" />. These can contain sensitive data.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.Pooling">
            <summary>
            Whether connection pooling should be used.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.MinPoolSize">
            <summary>
            The minimum connection pool size.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.MaxPoolSize">
            <summary>
            The maximum connection pool size.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.ConnectionIdleLifetime">
            <summary>
            The time to wait before closing idle connections in the pool if the count
            of all connections exceeds MinPoolSize.
            </summary>
            <value>The time (in seconds) to wait. The default value is 300.</value>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.ConnectionPruningInterval">
            <summary>
            How many seconds the pool waits before attempting to prune idle connections that are beyond
            idle lifetime (<see cref="P:Npgsql.NpgsqlConnectionStringBuilder.ConnectionIdleLifetime"/>.
            </summary>
            <value>The interval (in seconds). The default value is 10.</value>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.Timeout">
            <summary>
            The time to wait (in seconds) while trying to establish a connection before terminating the attempt and generating an error.
            Defaults to 15 seconds.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.CommandTimeout">
            <summary>
            The time to wait (in seconds) while trying to execute a command before terminating the attempt and generating an error.
            Defaults to 30 seconds.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.InternalCommandTimeout">
            <summary>
            The time to wait (in seconds) while trying to execute a an internal command before terminating the attempt and generating an error.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.EntityTemplateDatabase">
            <summary>
            The database template to specify when creating a database in Entity Framework. If not specified,
            PostgreSQL defaults to "template1".
            </summary>
            <remarks>
            http://www.postgresql.org/docs/current/static/manage-ag-templatedbs.html
            </remarks>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.EntityAdminDatabase">
            <summary>
            The database admin to specify when creating and dropping a database in Entity Framework. This is needed because
            Npgsql needs to connect to a database in order to send the create/drop database command.
            If not specified, defaults to "template1". Check NpgsqlServices.UsingPostgresDBConnection for more information.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.KeepAlive">
            <summary>
            The number of seconds of connection inactivity before Npgsql sends a keepalive query.
            Set to 0 (the default) to disable.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.TcpKeepAlive">
            <summary>
            Whether to use TCP keepalive with system defaults if overrides isn't specified.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.TcpKeepAliveTime">
            <summary>
            The number of seconds of connection inactivity before a TCP keepalive query is sent.
            Use of this option is discouraged, use <see cref="P:Npgsql.NpgsqlConnectionStringBuilder.KeepAlive"/> instead if possible.
            Set to 0 (the default) to disable. Supported only on Windows.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.TcpKeepAliveInterval">
            <summary>
            The interval, in milliseconds, between when successive keep-alive packets are sent if no acknowledgement is received.
            Defaults to the value of <see cref="P:Npgsql.NpgsqlConnectionStringBuilder.TcpKeepAliveTime"/>. <see cref="P:Npgsql.NpgsqlConnectionStringBuilder.TcpKeepAliveTime"/> must be non-zero as well.
            Supported only on Windows.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.ReadBufferSize">
            <summary>
            Determines the size of the internal buffer Npgsql uses when reading. Increasing may improve performance if transferring large values from the database.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.WriteBufferSize">
            <summary>
            Determines the size of the internal buffer Npgsql uses when writing. Increasing may improve performance if transferring large values to the database.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.SocketReceiveBufferSize">
            <summary>
            Determines the size of socket read buffer.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.SocketSendBufferSize">
            <summary>
            Determines the size of socket send buffer.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.MaxAutoPrepare">
            <summary>
            The maximum number SQL statements that can be automatically prepared at any given point.
            Beyond this number the least-recently-used statement will be recycled.
            Zero (the default) disables automatic preparation.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.AutoPrepareMinUsages">
            <summary>
            The minimum number of usages an SQL statement is used before it's automatically prepared.
            Defaults to 5.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.UsePerfCounters">
            <summary>
            Writes connection performance information to performance counters.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.NoResetOnClose">
            <summary>
            If set to true, a pool connection's state won't be reset when it is closed (improves performance).
            Do not specify this unless you know what you're doing.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.LoadTableComposites">
            <summary>
            Load table composite type definitions, and not just free-standing composite types.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.ServerCompatibilityMode">
            <summary>
            A compatibility mode for special PostgreSQL server types.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.ConvertInfinityDateTime">
            <summary>
            Makes MaxValue and MinValue timestamps and dates readable as infinity and negative infinity.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.ConnectionLifeTime">
            <summary>
            Obsolete, see http://www.npgsql.org/doc/migration/3.1.html
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.ContinuousProcessing">
            <summary>
            Obsolete, see http://www.npgsql.org/doc/migration/3.1.html
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.BackendTimeouts">
            <summary>
            Obsolete, see http://www.npgsql.org/doc/migration/3.1.html
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.PreloadReader">
            <summary>
            Obsolete, see http://www.npgsql.org/doc/migration/3.0.html
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.UseExtendedTypes">
            <summary>
            Obsolete, see http://www.npgsql.org/doc/migration/3.0.html
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlConnectionStringBuilder.Equals(System.Object)">
            <summary>
            Determines whether the specified object is equal to the current object.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlConnectionStringBuilder.GetHashCode">
            <summary>
            Hash function.
            </summary>
            <returns></returns>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.Keys">
            <summary>
            Gets an ICollection{string} containing the keys of the <see cref="T:Npgsql.NpgsqlConnectionStringBuilder"/>.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.Values">
            <summary>
            Gets an ICollection{string} containing the values in the <see cref="T:Npgsql.NpgsqlConnectionStringBuilder"/>.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlConnectionStringBuilder.CopyTo(System.Collections.Generic.KeyValuePair{System.String,System.Object}[],System.Int32)">
            <summary>
            Copies the elements of the <see cref="T:Npgsql.NpgsqlConnectionStringBuilder"/> to an Array, starting at a particular Array index.
            </summary>
            <param name="array">
            The one-dimensional Array that is the destination of the elements copied from <see cref="T:Npgsql.NpgsqlConnectionStringBuilder"/>.
            The Array must have zero-based indexing.
            </param>
            <param name="arrayIndex">
            The zero-based index in array at which copying begins.
            </param>
        </member>
        <member name="M:Npgsql.NpgsqlConnectionStringBuilder.GetEnumerator">
            <summary>
            Returns an enumerator that iterates through the <see cref="T:Npgsql.NpgsqlConnectionStringBuilder"/>.
            </summary>
            <returns></returns>
        </member>
        <member name="T:Npgsql.NpgsqlConnectionStringPropertyAttribute">
            <summary>
            Marks on <see cref="T:Npgsql.NpgsqlConnectionStringBuilder"/> which participate in the connection
            string. Optionally holds a set of synonyms for the property.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringPropertyAttribute.Synonyms">
            <summary>
            Holds a list of synonyms for the property.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlConnectionStringPropertyAttribute.#ctor">
            <summary>
            Creates a <see cref="T:Npgsql.NpgsqlConnectionStringPropertyAttribute"/>.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlConnectionStringPropertyAttribute.#ctor(System.String[])">
            <summary>
            Creates a <see cref="T:Npgsql.NpgsqlConnectionStringPropertyAttribute"/>.
            </summary>
        </member>
        <member name="T:Npgsql.ServerCompatibilityMode">
            <summary>
            An option specified in the connection string that activates special compatibility features.
            </summary>
        </member>
        <member name="F:Npgsql.ServerCompatibilityMode.None">
            <summary>
            No special server compatibility mode is active
            </summary>
        </member>
        <member name="F:Npgsql.ServerCompatibilityMode.Redshift">
            <summary>
            The server is an Amazon Redshift instance.
            </summary>
        </member>
        <member name="F:Npgsql.ServerCompatibilityMode.NoTypeLoading">
            <summary>
            The server is doesn't support full type loading from the PostgreSQL catalogs, support the basic set
            of types via information hardcoded inside Npgsql.
            </summary>
        </member>
        <member name="T:Npgsql.SslMode">
            <summary>
            Specifies how to manage SSL.
            </summary>
        </member>
        <member name="F:Npgsql.SslMode.Disable">
            <summary>
            SSL is disabled. If the server requires SSL, the connection will fail.
            </summary>
        </member>
        <member name="F:Npgsql.SslMode.Prefer">
            <summary>
            Prefer SSL connections if the server allows them, but allow connections without SSL.
            </summary>
        </member>
        <member name="F:Npgsql.SslMode.Require">
            <summary>
            Fail the connection if the server doesn't suppotr SSL.
            </summary>
        </member>
        <member name="T:Npgsql.NpgsqlConnector">
            <summary>
            Represents a connection to a PostgreSQL backend. Unlike NpgsqlConnection objects, which are
            exposed to users, connectors are internal to Npgsql and are recycled by the connection pool.
            </summary>
        </member>
        <member name="T:Npgsql.NpgsqlConnector.GSSPasswordMessageStream">
            <summary>
            This Stream is placed between NegotiateStream and the socket's NetworkStream (or SSLStream). It intercepts
            traffic and performs the following operations:
            * Outgoing messages are framed in PostgreSQL's PasswordMessage, and incoming are stripped of it.
            * NegotiateStream frames payloads with a 5-byte header, which PostgreSQL doesn't understand. This header is
            stripped from outgoing messages and added to incoming ones.
            </summary>
            <remarks>
            See https://referencesource.microsoft.com/#System/net/System/Net/_StreamFramer.cs,16417e735f0e9530,references
            </remarks>
        </member>
        <member name="F:Npgsql.NpgsqlConnector._socket">
            <summary>
            The physical connection socket to the backend.
            </summary>
        </member>
        <member name="F:Npgsql.NpgsqlConnector._baseStream">
            <summary>
            The physical connection stream to the backend, without anything on top.
            </summary>
        </member>
        <member name="F:Npgsql.NpgsqlConnector._stream">
            <summary>
            The physical connection stream to the backend, layered with an SSL/TLS stream if in secure mode.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnector.ReadBuffer">
            <summary>
            Buffer used for reading data.
            </summary>
        </member>
        <member name="F:Npgsql.NpgsqlConnector._origReadBuffer">
            <summary>
            If we read a data row that's bigger than <see cref="P:Npgsql.NpgsqlConnector.ReadBuffer"/>, we allocate an oversize buffer.
            The original (smaller) buffer is stored here, and restored when the connection is reset.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnector.WriteBuffer">
            <summary>
            Buffer used for writing data.
            </summary>
        </member>
        <member name="F:Npgsql.NpgsqlConnector._backendSecretKey">
            <summary>
            The secret key of the backend for this connector, used for query cancellation.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnector.BackendProcessId">
            <summary>
            The process ID of the backend for this connector.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnector.Id">
            <summary>
            A unique ID identifying this connector, used for logging. Currently mapped to BackendProcessId
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnector.TransactionStatus">
            <summary>
            The current transaction status for this connector.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnector.Transaction">
            <summary>
            The transaction currently in progress, if any.
            </summary>
            <remarks>
            <para>
            Note that this doesn't mean a transaction request has actually been sent to the backend - for
            efficiency we defer sending the request to the first query after BeginTransaction is called.
            See <see cref="P:Npgsql.NpgsqlConnector.TransactionStatus"/> for the actual transaction status.
            </para>
            <para>
            Also, the user can initiate a transaction in SQL (i.e. BEGIN), in which case there will be no
            NpgsqlTransaction instance. As a result, never check <see cref="P:Npgsql.NpgsqlConnector.Transaction"/> to know whether
            a transaction is in progress, check <see cref="P:Npgsql.NpgsqlConnector.TransactionStatus"/> instead.
            </para>
            </remarks>
        </member>
        <member name="P:Npgsql.NpgsqlConnector.Connection">
            <summary>
            The NpgsqlConnection that (currently) owns this connector. Null if the connector isn't
            owned (i.e. idle in the pool)
            </summary>
        </member>
        <member name="F:Npgsql.NpgsqlConnector._pendingPrependedResponses">
            <summary>
            The number of messages that were prepended to the current message chain, but not yet sent.
            Note that this only tracks messages which produce a ReadyForQuery message
            </summary>
        </member>
        <member name="F:Npgsql.NpgsqlConnector.CurrentCopyOperation">
            <summary>
            If the connector is currently in COPY mode, holds a reference to the importer/exporter object.
            Otherwise null.
            </summary>
        </member>
        <member name="F:Npgsql.NpgsqlConnector.PostgresParameters">
            <summary>
            Holds all run-time parameters received from the backend (via ParameterStatus messages)
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnector.UserTimeout">
            <summary>
            The timeout for reading messages that are part of the user's command
            (i.e. which aren't internal prepended commands).
            </summary>
        </member>
        <member name="F:Npgsql.NpgsqlConnector._currentTimeout">
            <summary>
            Contains the current value of the socket's ReceiveTimeout, used to determine whether
            we need to change it when commands are received.
            </summary>
        </member>
        <member name="F:Npgsql.NpgsqlConnector._userLock">
            <summary>
            A lock that's taken while a user action is in progress, e.g. a command being executed.
            Only used when keepalive is enabled, otherwise null.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnector.CancelLock">
            <summary>
            A lock that's taken while a cancellation is being delivered; new queries are blocked until the
            cancellation is delivered. This reduces the chance that a cancellation meant for a previous
            command will accidentally cancel a later one, see #615.
            </summary>
        </member>
        <member name="F:Npgsql.NpgsqlConnector._currentCommand">
            <summary>
            The command currently being executed by the connector, null otherwise.
            Used only for concurrent use error reporting purposes.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnector.ReleaseTimestamp">
            <summary>
            If pooled, the timestamp when this connector was returned to the pool.
            </summary>
        </member>
        <member name="F:Npgsql.NpgsqlConnector.MinimumInternalCommandTimeout">
            <summary>
            The minimum timeout that can be set on internal commands such as COMMIT, ROLLBACK.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlConnector.#ctor(Npgsql.NpgsqlConnectionStringBuilder,System.String)">
            <summary>
            Creates a new connector with the given connection string.
            </summary>
            <param name="settings">The parsed connection string.</param>
            <param name="connectionString">The connection string.</param>
        </member>
        <member name="P:Npgsql.NpgsqlConnector.State">
            <summary>
            Gets the current state of the connector
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnector.IsConnected">
            <summary>
            Returns whether the connector is open, regardless of any task it is currently performing
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlConnector.Open(Npgsql.NpgsqlTimeout,System.Boolean,System.Threading.CancellationToken)">
            <summary>
            Opens the physical connection to the server.
            </summary>
            <remarks>Usually called by the RequestConnector
            Method of the connection pool manager.</remarks>
        </member>
        <member name="M:Npgsql.NpgsqlConnector.PrependInternalMessage(Npgsql.FrontendMessage)">
            <summary>
            Prepends a message to be sent at the beginning of the next message chain.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlConnector.SkipUntil(Npgsql.BackendMessageCode)">
            <summary>
            Reads backend messages and discards them, stopping only after a message of the given type has
            been seen. Only a sync I/O version of this method exists - in async flows we inline the loop
            rather than calling an additional async method, in order to avoid the overhead.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlConnector.ProcessNewTransactionStatus(Npgsql.TransactionStatus)">
            <summary>
            Handles a new transaction indicator received on a ReadyForQuery message
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnector.IsSecure">
            <summary>
            Returns whether SSL is being used for the connection
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlConnector.CancelRequest">
            <summary>
            Creates another connector and sends a cancel request through it for this connector.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlConnector.CloseOngoingOperations">
            <summary>
            Closes ongoing operations, i.e. an open reader exists or a COPY operation still in progress, as
            part of a connection close.
            Does nothing if the thread has been aborted - the connector will be closed immediately.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlConnector.UnexpectedMessageReceived(Npgsql.BackendMessageCode)">
            <summary>
            Called when an unexpected message has been received during an action. Breaks the
            connector and returns the appropriate message.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlConnector.Break">
            <summary>
            Called when a connector becomes completely unusable, e.g. when an unexpected I/O exception is raised or when
            we lose protocol sync.
            Note that fatal errors during the Open phase do *not* pass through here.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlConnector.Cleanup">
            <summary>
            Closes the socket and cleans up client-side resources associated with this connector.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlConnector.Reset">
            <summary>
            Called when a pooled connection is closed, and its connector is returned to the pool.
            Resets the connector back to its initial state, releasing server-side sources
            (e.g. prepared statements), resetting parameters to their defaults, and resetting client-side
            state
            </summary>
            <remarks>
            It's important that this method be idempotent, since some race conditions in the pool
            can cause it to be called twice (and also the user may close the connection right after
            allocating it, without doing anything).
            </remarks>
        </member>
        <member name="T:Npgsql.NpgsqlConnector.UserAction">
            <summary>
            An IDisposable wrapper around <see cref="M:Npgsql.NpgsqlConnector.EndUserAction"/>.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnector.Timezone">
            <summary>
            The connection's timezone as reported by PostgreSQL, in the IANA/Olson database format.
            </summary>
        </member>
        <member name="T:Npgsql.ConnectorState">
            <summary>
            Expresses the exact state of a connector.
            </summary>
        </member>
        <member name="F:Npgsql.ConnectorState.Closed">
            <summary>
            The connector has either not yet been opened or has been closed.
            </summary>
        </member>
        <member name="F:Npgsql.ConnectorState.Connecting">
            <summary>
            The connector is currently connecting to a Postgresql server.
            </summary>
        </member>
        <member name="F:Npgsql.ConnectorState.Ready">
            <summary>
            The connector is connected and may be used to send a new query.
            </summary>
        </member>
        <member name="F:Npgsql.ConnectorState.Executing">
            <summary>
            The connector is waiting for a response to a query which has been sent to the server.
            </summary>
        </member>
        <member name="F:Npgsql.ConnectorState.Fetching">
            <summary>
            The connector is currently fetching and processing query results.
            </summary>
        </member>
        <member name="F:Npgsql.ConnectorState.Waiting">
            <summary>
            The connector is currently waiting for asynchronous notifications to arrive.
            </summary>
        </member>
        <member name="F:Npgsql.ConnectorState.Broken">
            <summary>
            The connection was broken because an unexpected error occurred which left it in an unknown state.
            This state isn't implemented yet.
            </summary>
        </member>
        <member name="F:Npgsql.ConnectorState.Copy">
            <summary>
            The connector is engaged in a COPY operation.
            </summary>
        </member>
        <member name="F:Npgsql.TransactionStatus.Idle">
            <summary>
            Currently not in a transaction block
            </summary>
        </member>
        <member name="F:Npgsql.TransactionStatus.InTransactionBlock">
            <summary>
            Currently in a transaction block
            </summary>
        </member>
        <member name="F:Npgsql.TransactionStatus.InFailedTransactionBlock">
            <summary>
            Currently in a failed transaction block (queries will be rejected until block is ended)
            </summary>
        </member>
        <member name="F:Npgsql.TransactionStatus.Pending">
            <summary>
            A new transaction has been requested but not yet transmitted to the backend. It will be transmitted
            prepended to the next query.
            This is a client-side state option only, and is never transmitted from the backend.
            </summary>
        </member>
        <member name="T:Npgsql.DataRowLoadingMode">
            <summary>
            Specifies how to load/parse DataRow messages as they're received from the backend.
            </summary>
        </member>
        <member name="F:Npgsql.DataRowLoadingMode.NonSequential">
            <summary>
            Load DataRows in non-sequential mode
            </summary>
        </member>
        <member name="F:Npgsql.DataRowLoadingMode.Sequential">
            <summary>
            Load DataRows in sequential mode
            </summary>
        </member>
        <member name="F:Npgsql.DataRowLoadingMode.Skip">
            <summary>
            Skip DataRow messages altogether
            </summary>
        </member>
        <member name="T:Npgsql.NpgsqlRowUpdatedEventHandler">
            <summary>
            Represents the method that handles the <see cref="E:Npgsql.NpgsqlDataAdapter.RowUpdated">RowUpdated</see> events.
            </summary>
            <param name="sender">The source of the event.</param>
            <param name="e">A <see cref="T:Npgsql.NpgsqlRowUpdatedEventArgs">NpgsqlRowUpdatedEventArgs</see> that contains the event data.</param>
        </member>
        <member name="T:Npgsql.NpgsqlRowUpdatingEventHandler">
            <summary>
            Represents the method that handles the <see cref="E:Npgsql.NpgsqlDataAdapter.RowUpdating">RowUpdating</see> events.
            </summary>
            <param name="sender">The source of the event.</param>
            <param name="e">A <see cref="T:Npgsql.NpgsqlRowUpdatingEventArgs">NpgsqlRowUpdatingEventArgs</see> that contains the event data.</param>
        </member>
        <member name="T:Npgsql.NpgsqlDataAdapter">
            <summary>
            This class represents an adapter from many commands: select, update, insert and delete to fill <see cref="T:System.Data.DataSet">Datasets.</see>
            </summary>
        </member>
        <member name="E:Npgsql.NpgsqlDataAdapter.RowUpdated">
            <summary>
            Row updated event.
            </summary>
        </member>
        <member name="E:Npgsql.NpgsqlDataAdapter.RowUpdating">
            <summary>
            Row updating event.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlDataAdapter.#ctor">
            <summary>
            Default constructor.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlDataAdapter.#ctor(Npgsql.NpgsqlCommand)">
            <summary>
            Constructor.
            </summary>
            <param name="selectCommand"></param>
        </member>
        <member name="M:Npgsql.NpgsqlDataAdapter.#ctor(System.String,Npgsql.NpgsqlConnection)">
            <summary>
            Constructor.
            </summary>
            <param name="selectCommandText"></param>
            <param name="selectConnection"></param>
        </member>
        <member name="M:Npgsql.NpgsqlDataAdapter.#ctor(System.String,System.String)">
            <summary>
            Constructor.
            </summary>
            <param name="selectCommandText"></param>
            <param name="selectConnectionString"></param>
        </member>
        <member name="M:Npgsql.NpgsqlDataAdapter.CreateRowUpdatedEvent(System.Data.DataRow,System.Data.IDbCommand,System.Data.StatementType,System.Data.Common.DataTableMapping)">
            <summary>
            Create row updated event.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlDataAdapter.CreateRowUpdatingEvent(System.Data.DataRow,System.Data.IDbCommand,System.Data.StatementType,System.Data.Common.DataTableMapping)">
            <summary>
            Create row updating event.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlDataAdapter.OnRowUpdated(System.Data.Common.RowUpdatedEventArgs)">
            <summary>
            Raise the RowUpdated event.
            </summary>
            <param name="value"></param>
        </member>
        <member name="M:Npgsql.NpgsqlDataAdapter.OnRowUpdating(System.Data.Common.RowUpdatingEventArgs)">
            <summary>
            Raise the RowUpdating event.
            </summary>
            <param name="value"></param>
        </member>
        <member name="P:Npgsql.NpgsqlDataAdapter.DeleteCommand">
            <summary>
            Delete command.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlDataAdapter.SelectCommand">
            <summary>
            Select command.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlDataAdapter.UpdateCommand">
            <summary>
            Update command.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlDataAdapter.InsertCommand">
            <summary>
            Insert command.
            </summary>
        </member>
        <member name="T:Npgsql.NpgsqlDatabaseInfo">
            <summary>
            Base class for implementations which provide information about PostgreSQL and PostgreSQL-like databases
            (e.g. type definitions, capabilities...).
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlDatabaseInfo.Host">
            <summary>
            The hostname of IP address of the database.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlDatabaseInfo.Port">
            <summary>
            The TCP port of the database.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlDatabaseInfo.Name">
            <summary>
            The database name.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlDatabaseInfo.Version">
            <summary>
            The version of the PostgreSQL database we're connected to, as reported in the "server_version" parameter.
            Exposed via <see cref="P:Npgsql.NpgsqlConnection.PostgreSqlVersion"/>.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlDatabaseInfo.SupportsRangeTypes">
            <summary>
            Whether the backend supports range types.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlDatabaseInfo.SupportsEnumTypes">
            <summary>
            Whether the backend supports enum types.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlDatabaseInfo.SupportsCloseAll">
            <summary>
            Whether the backend supports the CLOSE ALL statement.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlDatabaseInfo.SupportsAdvisoryLocks">
            <summary>
            Whether the backend supports advisory locks.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlDatabaseInfo.SupportsDiscardSequences">
            <summary>
            Whether the backend supports the DISCARD SEQUENCES statement.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlDatabaseInfo.SupportsUnlisten">
            <summary>
            Whether the backend supports the UNLISTEN statement.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlDatabaseInfo.SupportsDiscardTemp">
            <summary>
            Whether the backend supports the DISCARD TEMP statement.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlDatabaseInfo.SupportsDiscard">
            <summary>
            Whether the backend supports the DISCARD statement.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlDatabaseInfo.HasIntegerDateTimes">
            <summary>
            Reports whether the backend uses the newer integer timestamp representation.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlDatabaseInfo.SupportsTransactions">
            <summary>
            Whether the database supports transactions.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlDatabaseInfo.ByOID">
            <summary>
            Indexes backend types by their type OID.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlDatabaseInfo.ByFullName">
            <summary>
            Indexes backend types by their PostgreSQL name, including namespace (e.g. pg_catalog.int4).
            Only used for enums and composites.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlDatabaseInfo.ByName">
            <summary>
            Indexes backend types by their PostgreSQL name, not including namespace.
            If more than one type exists with the same name (i.e. in different namespaces) this
            table will contain an entry with a null value.
            Only used for enums and composites.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlDatabaseInfo.GetTypes">
            <summary>
            Provides all PostgreSQL types detected in this database.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Npgsql.NpgsqlDatabaseInfo.ParseServerVersion(System.String)">
            <summary>
            Parses a PostgreSQL server version (e.g. 10.1, 9.6.3) and returns a CLR Version.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlDatabaseInfo.RegisterFactory(Npgsql.INpgsqlDatabaseInfoFactory)">
            <summary>
            Registers a new database info factory, which is used to load information about databases.
            </summary>
        </member>
        <member name="T:Npgsql.NpgsqlDataReader">
            <summary>
            Reads a forward-only stream of rows from a data source.
            </summary>
        </member>
        <member name="F:Npgsql.NpgsqlDataReader.Behavior">
            <summary>
            The behavior of the command with which this reader was executed.
            </summary>
        </member>
        <member name="F:Npgsql.NpgsqlDataReader._statements">
            <summary>
            Holds the list of statements being executed by this reader.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlDataReader.StatementIndex">
            <summary>
            The index of the current query resultset we're processing (within a multiquery)
            </summary>
        </member>
        <member name="F:Npgsql.NpgsqlDataReader.ColumnLen">
            <summary>
            For streaming types (e.g. bytea), holds the byte length of the column.
            Does not include the length prefix.
            </summary>
        </member>
        <member name="F:Npgsql.NpgsqlDataReader.RowDescription">
            <summary>
            The RowDescription message for the current resultset being processed
            </summary>
        </member>
        <member name="F:Npgsql.NpgsqlDataReader._hasRows">
            <summary>
            Whether the current result set has rows
            </summary>
        </member>
        <member name="E:Npgsql.NpgsqlDataReader.ReaderClosed">
            <summary>
            Is raised whenever Close() is called.
            </summary>
        </member>
        <member name="F:Npgsql.NpgsqlDataReader.ColumnStream">
            <summary>
            A stream that has been opened on a column.
            </summary>
        </member>
        <member name="F:Npgsql.NpgsqlDataReader._tempCharBuf">
            <summary>
            Used for internal temporary purposes
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlDataReader.Read">
            <summary>
            Advances the reader to the next record in a result set.
            </summary>
            <returns><b>true</b> if there are more rows; otherwise <b>false</b>.</returns>
            <remarks>
            The default position of a data reader is before the first record. Therefore, you must call Read to begin accessing data.
            </remarks>
        </member>
        <member name="M:Npgsql.NpgsqlDataReader.ReadAsync(System.Threading.CancellationToken)">
            <summary>
            This is the asynchronous version of <see cref="M:Npgsql.NpgsqlDataReader.Read"/> The cancellation token is currently ignored.
            </summary>
            <param name="cancellationToken">Ignored for now.</param>
            <returns>A task representing the asynchronous operation.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlDataReader.Read(System.Boolean)">
            <summary>
            Implementation of read
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlDataReader.NextResult">
            <summary>
            Advances the reader to the next result when reading the results of a batch of statements.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Npgsql.NpgsqlDataReader.NextResultAsync(System.Threading.CancellationToken)">
            <summary>
            This is the asynchronous version of NextResult.
            The <paramref name="cancellationToken"/> parameter is currently ignored.
            </summary>
            <param name="cancellationToken">Currently ignored.</param>
            <returns>A task representing the asynchronous operation.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlDataReader.NextResult(System.Boolean,System.Boolean)">
            <summary>
            Internal implementation of NextResult
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlDataReader.NextResultSchemaOnly(System.Boolean)">
            <summary>
            Note that in SchemaOnly mode there are no resultsets, and we read nothing from the backend (all
            RowDescriptions have already been processed and are available)
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlDataReader.Depth">
            <summary>
            Gets a value indicating the depth of nesting for the current row.  Always returns zero.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlDataReader.IsClosed">
            <summary>
            Gets a value indicating whether the data reader is closed.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlDataReader.RecordsAffected">
            <summary>
            Gets the number of rows changed, inserted, or deleted by execution of the SQL statement.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlDataReader.Statements">
             <summary>
             Returns details about each statement that this reader will or has executed.
             </summary>
             <remarks>
             Note that some fields (i.e. rows and oid) are only populated as the reader
             traverses the result.
            
             For commands with multiple queries, this exposes the number of rows affected on
             a statement-by-statement basis, unlike <see cref="P:Npgsql.NpgsqlDataReader.RecordsAffected"/>
             which exposes an aggregation across all statements.
             </remarks>
        </member>
        <member name="P:Npgsql.NpgsqlDataReader.HasRows">
            <summary>
            Gets a value that indicates whether this DbDataReader contains one or more rows.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlDataReader.IsOnRow">
            <summary>
            Indicates whether the reader is currently positioned on a row, i.e. whether reading a
            column is possible.
            This property is different from <see cref="P:Npgsql.NpgsqlDataReader.HasRows"/> in that <see cref="P:Npgsql.NpgsqlDataReader.HasRows"/> will
            return true even if attempting to read a column will fail, e.g. before <see cref="M:Npgsql.NpgsqlDataReader.Read"/>
            has been called
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlDataReader.GetName(System.Int32)">
            <summary>
            Gets the name of the column, given the zero-based column ordinal.
            </summary>
            <param name="ordinal">The zero-based column ordinal.</param>
            <returns>The name of the specified column.</returns>
        </member>
        <member name="P:Npgsql.NpgsqlDataReader.FieldCount">
            <summary>
            Gets the number of columns in the current row.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlDataReader.Consume(System.Boolean)">
            <summary>
            Consumes all result sets for this reader, leaving the connector ready for sending and processing further
            queries
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlDataReader.Dispose(System.Boolean)">
            <summary>
            Releases the resources used by the <see cref="T:Npgsql.NpgsqlDataReader">NpgsqlDataReader</see>.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlDataReader.Close">
            <summary>
            Closes the <see cref="T:Npgsql.NpgsqlDataReader"/> reader, allowing a new command to be executed.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlDataReader.CloseAsync">
            <summary>
            Closes the <see cref="T:Npgsql.NpgsqlDataReader"/> reader, allowing a new command to be executed.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlDataReader.GetBoolean(System.Int32)">
            <summary>
            Gets the value of the specified column as a Boolean.
            </summary>
            <param name="ordinal">The zero-based column ordinal.</param>
            <returns>The value of the specified column.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlDataReader.GetByte(System.Int32)">
            <summary>
            Gets the value of the specified column as a byte.
            </summary>
            <param name="ordinal">The zero-based column ordinal.</param>
            <returns>The value of the specified column.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlDataReader.GetChar(System.Int32)">
            <summary>
            Gets the value of the specified column as a single character.
            </summary>
            <param name="ordinal">The zero-based column ordinal.</param>
            <returns>The value of the specified column.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlDataReader.GetInt16(System.Int32)">
            <summary>
            Gets the value of the specified column as a 16-bit signed integer.
            </summary>
            <param name="ordinal">The zero-based column ordinal.</param>
            <returns>The value of the specified column.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlDataReader.GetInt32(System.Int32)">
            <summary>
            Gets the value of the specified column as a 32-bit signed integer.
            </summary>
            <param name="ordinal">The zero-based column ordinal.</param>
            <returns>The value of the specified column.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlDataReader.GetInt64(System.Int32)">
            <summary>
            Gets the value of the specified column as a 64-bit signed integer.
            </summary>
            <param name="ordinal">The zero-based column ordinal.</param>
            <returns>The value of the specified column.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlDataReader.GetDateTime(System.Int32)">
            <summary>
            Gets the value of the specified column as a <see cref="T:System.DateTime"/> object.
            </summary>
            <param name="ordinal">The zero-based column ordinal.</param>
            <returns>The value of the specified column.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlDataReader.GetString(System.Int32)">
            <summary>
            Gets the value of the specified column as an instance of <see cref="T:System.String"/>.
            </summary>
            <param name="ordinal">The zero-based column ordinal.</param>
            <returns>The value of the specified column.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlDataReader.GetDecimal(System.Int32)">
            <summary>
            Gets the value of the specified column as a <see cref="T:System.Decimal"/> object.
            </summary>
            <param name="ordinal">The zero-based column ordinal.</param>
            <returns>The value of the specified column.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlDataReader.GetDouble(System.Int32)">
            <summary>
            Gets the value of the specified column as a double-precision floating point number.
            </summary>
            <param name="ordinal">The zero-based column ordinal.</param>
            <returns>The value of the specified column.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlDataReader.GetFloat(System.Int32)">
            <summary>
            Gets the value of the specified column as a single-precision floating point number.
            </summary>
            <param name="ordinal">The zero-based column ordinal.</param>
            <returns>The value of the specified column.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlDataReader.GetGuid(System.Int32)">
            <summary>
            Gets the value of the specified column as a globally-unique identifier (GUID).
            </summary>
            <param name="ordinal">The zero-based column ordinal.</param>
            <returns>The value of the specified column.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlDataReader.GetValues(System.Object[])">
            <summary>
            Populates an array of objects with the column values of the current row.
            </summary>
            <param name="values">An array of Object into which to copy the attribute columns.</param>
            <returns>The number of instances of <see cref="T:System.Object"/> in the array.</returns>
        </member>
        <member name="P:Npgsql.NpgsqlDataReader.Item(System.Int32)">
            <summary>
            Gets the value of the specified column as an instance of <see cref="T:System.Object"/>.
            </summary>
            <param name="ordinal">The zero-based column ordinal.</param>
            <returns>The value of the specified column.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlDataReader.GetDate(System.Int32)">
            <summary>
            Gets the value of the specified column as an <see cref="T:NpgsqlTypes.NpgsqlDate"/>,
            Npgsql's provider-specific type for dates.
            </summary>
            <remarks>
            PostgreSQL's date type represents dates from 4713 BC to 5874897 AD, while .NET's DateTime
            only supports years from 1 to 1999. If you require years outside this range use this accessor.
            The standard <see cref="M:System.Data.Common.DbDataReader.GetProviderSpecificValue(System.Int32)"/> method will also return this type, but has
            the disadvantage of boxing the value.
            See http://www.postgresql.org/docs/current/static/datatype-datetime.html
            </remarks>
            <param name="ordinal">The zero-based column ordinal.</param>
            <returns>The value of the specified column.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlDataReader.GetTimeSpan(System.Int32)">
            <summary>
            Gets the value of the specified column as a TimeSpan,
            </summary>
            <remarks>
            PostgreSQL's interval type has has a resolution of 1 microsecond and ranges from
            -178000000 to 178000000 years, while .NET's TimeSpan has a resolution of 100 nanoseconds
            and ranges from roughly -29247 to 29247 years.
            See http://www.postgresql.org/docs/current/static/datatype-datetime.html
            </remarks>
            <param name="ordinal">The zero-based column ordinal.</param>
            <returns>The value of the specified column.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlDataReader.GetInterval(System.Int32)">
            <summary>
            Gets the value of the specified column as an <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/>,
            Npgsql's provider-specific type for time spans.
            </summary>
            <remarks>
            PostgreSQL's interval type has has a resolution of 1 microsecond and ranges from
            -178000000 to 178000000 years, while .NET's TimeSpan has a resolution of 100 nanoseconds
            and ranges from roughly -29247 to 29247 years. If you require values from outside TimeSpan's
            range use this accessor.
            The standard ADO.NET <see cref="M:System.Data.Common.DbDataReader.GetProviderSpecificValue(System.Int32)"/> method will also return this
            type, but has the disadvantage of boxing the value.
            See http://www.postgresql.org/docs/current/static/datatype-datetime.html
            </remarks>
            <param name="ordinal">The zero-based column ordinal.</param>
            <returns>The value of the specified column.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlDataReader.GetTimeStamp(System.Int32)">
            <summary>
            Gets the value of the specified column as an <see cref="T:NpgsqlTypes.NpgsqlDateTime"/>,
            Npgsql's provider-specific type for date/time timestamps. Note that this type covers
            both PostgreSQL's "timestamp with time zone" and "timestamp without time zone" types,
            which differ only in how they are converted upon input/output.
            </summary>
            <remarks>
            PostgreSQL's timestamp type represents dates from 4713 BC to 5874897 AD, while .NET's DateTime
            only supports years from 1 to 1999. If you require years outside this range use this accessor.
            The standard <see cref="M:System.Data.Common.DbDataReader.GetProviderSpecificValue(System.Int32)"/> method will also return this type, but has
            the disadvantage of boxing the value.
            See http://www.postgresql.org/docs/current/static/datatype-datetime.html
            </remarks>
            <param name="ordinal">The zero-based column ordinal.</param>
            <returns>The value of the specified column.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlDataReader.GetBytes(System.Int32,System.Int64,System.Byte[],System.Int32,System.Int32)">
            <summary>
            Reads a stream of bytes from the specified column, starting at location indicated by dataOffset, into the buffer, starting at the location indicated by bufferOffset.
            </summary>
            <param name="ordinal">The zero-based column ordinal.</param>
            <param name="dataOffset">The index within the row from which to begin the read operation.</param>
            <param name="buffer">The buffer into which to copy the data.</param>
            <param name="bufferOffset">The index with the buffer to which the data will be copied.</param>
            <param name="length">The maximum number of characters to read.</param>
            <returns>The actual number of bytes read.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlDataReader.GetStream(System.Int32)">
            <summary>
            Retrieves data as a <see cref="T:System.IO.Stream"/>.
            </summary>
            <param name="ordinal">The zero-based column ordinal.</param>
            <returns>The returned object.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlDataReader.GetStreamAsync(System.Int32)">
            <summary>
            Retrieves data as a <see cref="T:System.IO.Stream"/>.
            </summary>
            <param name="ordinal">The zero-based column ordinal.</param>
            <returns>The returned object.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlDataReader.GetChars(System.Int32,System.Int64,System.Char[],System.Int32,System.Int32)">
            <summary>
            Reads a stream of characters from the specified column, starting at location indicated by dataOffset, into the buffer, starting at the location indicated by bufferOffset.
            </summary>
            <param name="ordinal">The zero-based column ordinal.</param>
            <param name="dataOffset">The index within the row from which to begin the read operation.</param>
            <param name="buffer">The buffer into which to copy the data.</param>
            <param name="bufferOffset">The index with the buffer to which the data will be copied.</param>
            <param name="length">The maximum number of characters to read.</param>
            <returns>The actual number of characters read.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlDataReader.GetTextReader(System.Int32)">
            <summary>
            Retrieves data as a <see cref="T:System.IO.TextReader"/>.
            </summary>
            <param name="ordinal">The zero-based column ordinal.</param>
            <returns>The returned object.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlDataReader.GetTextReaderAsync(System.Int32)">
            <summary>
            Retrieves data as a <see cref="T:System.IO.TextReader"/>.
            </summary>
            <param name="ordinal">The zero-based column ordinal.</param>
            <returns>The returned object.</returns>
        </member>
        <member name="P:Npgsql.NpgsqlDataReader.Item(System.String)">
            <summary>
            Gets the value of the specified column as an instance of <see cref="T:System.Object"/>.
            </summary>
            <param name="name">The name of the column.</param>
            <returns>The value of the specified column.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlDataReader.GetOrdinal(System.String)">
            <summary>
            Gets the column ordinal given the name of the column.
            </summary>
            <param name="name">The name of the column.</param>
            <returns>The zero-based column ordinal.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlDataReader.GetPostgresType(System.Int32)">
            <summary>
            Gets a representation of the PostgreSQL data type for the specified field.
            The returned representation can be used to access various information about the field.
            </summary>
            <param name="ordinal">The zero-based column index.</param>
        </member>
        <member name="M:Npgsql.NpgsqlDataReader.GetDataTypeName(System.Int32)">
            <summary>
            Gets the data type information for the specified field.
            This will be the PostgreSQL type name (e.g. double precision), not the .NET type
            (see <see cref="M:Npgsql.NpgsqlDataReader.GetFieldType(System.Int32)"/> for that).
            </summary>
            <param name="ordinal">The zero-based column index.</param>
        </member>
        <member name="M:Npgsql.NpgsqlDataReader.GetDataTypeOID(System.Int32)">
            <summary>
            Gets the OID for the PostgreSQL type for the specified field, as it appears in the pg_type table.
            </summary>
            <remarks>
            This is a PostgreSQL-internal value that should not be relied upon and should only be used for
            debugging purposes.
            </remarks>
            <param name="ordinal">The zero-based column index.</param>
        </member>
        <member name="M:Npgsql.NpgsqlDataReader.GetFieldType(System.Int32)">
            <summary>
            Gets the data type of the specified column.
            </summary>
            <param name="ordinal">The zero-based column ordinal.</param>
            <returns>The data type of the specified column.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlDataReader.GetProviderSpecificFieldType(System.Int32)">
            <summary>
            Returns the provider-specific field type of the specified column.
            </summary>
            <param name="ordinal">The zero-based column ordinal.</param>
            <returns>The Type object that describes the data type of the specified column.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlDataReader.GetProviderSpecificValues(System.Object[])">
            <summary>
            Gets all provider-specific attribute columns in the collection for the current row.
            </summary>
            <param name="values">An array of Object into which to copy the attribute columns.</param>
            <returns>The number of instances of <see cref="T:System.Object"/> in the array.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlDataReader.GetEnumerator">
            <summary>
            Returns an <see cref="T:System.Collections.IEnumerator"/> that can be used to iterate through the rows in the data reader.
            </summary>
            <returns>An <see cref="T:System.Collections.IEnumerator"/> that can be used to iterate through the rows in the data reader.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlDataReader.GetColumnSchema">
            <summary>
            Returns schema information for the columns in the current resultset.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Npgsql.NpgsqlDataReader.GetSchemaTable">
            <summary>
            Returns a System.Data.DataTable that describes the column metadata of the DataReader.
            </summary>
        </member>
        <member name="T:Npgsql.NpgsqlDefaultDataReader">
            <summary>
            The default, non-sequential reader, which buffers entire rows in memory.
            </summary>
        </member>
        <member name="F:Npgsql.NpgsqlDefaultDataReader._column">
            <summary>
            The number of columns in the current row
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlDefaultDataReader.GetValue(System.Int32)">
            <summary>
            Gets the value of the specified column as an instance of <see cref="T:System.Object"/>.
            </summary>
            <param name="ordinal">The zero-based column ordinal.</param>
            <returns>The value of the specified column.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlDefaultDataReader.GetProviderSpecificValue(System.Int32)">
            <summary>
            Gets the value of the specified column as an instance of <see cref="T:System.Object"/>.
            </summary>
            <param name="ordinal">The zero-based column ordinal.</param>
            <returns>The value of the specified column.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlDefaultDataReader.IsDBNull(System.Int32)">
            <summary>
            Gets a value that indicates whether the column contains nonexistent or missing values.
            </summary>
            <param name="ordinal">The zero-based column ordinal.</param>
            <returns><b>true</b> if the specified column is equivalent to <see cref="T:System.DBNull"/>; otherwise <b>false</b>.</returns>
        </member>
        <member name="T:Npgsql.NpgsqlException">
            <summary>
            The exception that is thrown when server-related issues occur.
            </summary>
            <remarks>
            PostgreSQL errors (e.g. query SQL issues, constraint violations) are raised via
            <see cref="T:Npgsql.PostgresException"/> which is a subclass of this class.
            Purely Npgsql-related issues which aren't related to the server will be raised
            via the standard CLR exceptions (e.g. ArgumentException).
            </remarks>
        </member>
        <member name="M:Npgsql.NpgsqlException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Npgsql.NpgsqlException"/> class.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Npgsql.NpgsqlException"/> class with a specified error message and a reference to the inner exception that is the cause of this exception.
            </summary>
            <param name="message">The error message that explains the reason for the exception.</param>
            <param name="innerException">The exception that is the cause of the current exception, or a null reference (<string>Nothing</string> in Visual Basic) if no inner exception is specified.</param>
        </member>
        <member name="M:Npgsql.NpgsqlException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Npgsql.NpgsqlException"/> class with a specified error message.
            </summary>
            <param name="message">The message that describes the error.</param>
        </member>
        <member name="P:Npgsql.NpgsqlException.IsTransient">
            <summary>
            Specifies whether the exception is considered transient, that is, whether retrying to operation could
            succeed (e.g. a network error).
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the <see cref="T:Npgsql.NpgsqlException"/> class with serialized data.
            </summary>
            <param name="info">The SerializationInfo that holds the serialized object data about the exception being thrown.</param>
            <param name="context">The StreamingContext that contains contextual information about the source or destination.</param>
        </member>
        <member name="T:Npgsql.NpgsqlFactory">
            <summary>
            A factory to create instances of various Npgsql objects.
            </summary>
        </member>
        <member name="F:Npgsql.NpgsqlFactory.Instance">
            <summary>
            Gets an instance of the <see cref="T:Npgsql.NpgsqlFactory"/>.
            This can be used to retrieve strongly typed data objects.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlFactory.CreateCommand">
            <summary>
            Returns a strongly typed <see cref="T:System.Data.Common.DbCommand"/> instance.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlFactory.CreateConnection">
            <summary>
            Returns a strongly typed <see cref="T:System.Data.Common.DbConnection"/> instance.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlFactory.CreateParameter">
            <summary>
            Returns a strongly typed <see cref="T:System.Data.Common.DbParameter"/> instance.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlFactory.CreateConnectionStringBuilder">
            <summary>
            Returns a strongly typed <see cref="T:System.Data.Common.DbConnectionStringBuilder"/> instance.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlFactory.CreateCommandBuilder">
            <summary>
            Returns a strongly typed <see cref="T:System.Data.Common.DbCommandBuilder"/> instance.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlFactory.CreateDataAdapter">
            <summary>
            Returns a strongly typed <see cref="T:System.Data.Common.DbDataAdapter"/> instance.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlFactory.GetService(System.Type)">
            <summary>
            Gets the service object of the specified type.
            </summary>
            <param name="serviceType">An object that specifies the type of service object to get.</param>
            <returns>A service object of type serviceType, or null if there is no service object of type serviceType.</returns>
        </member>
        <member name="T:Npgsql.NpgsqlLargeObjectManager">
            <summary>
            Large object manager. This class can be used to store very large files in a PostgreSQL database.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlLargeObjectManager.MaxTransferBlockSize">
            <summary>
            The largest chunk size (in bytes) read and write operations will read/write each roundtrip to the network. Default 4 MB.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlLargeObjectManager.#ctor(Npgsql.NpgsqlConnection)">
            <summary>
            Creates an NpgsqlLargeObjectManager for this connection. The connection must be opened to perform remote operations.
            </summary>
            <param name="connection"></param>
        </member>
        <member name="M:Npgsql.NpgsqlLargeObjectManager.ExecuteFunction``1(System.String,System.Boolean,System.Object[])">
            <summary>
            Execute a function
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlLargeObjectManager.ExecuteFunctionGetBytes(System.String,System.Byte[],System.Int32,System.Int32,System.Boolean,System.Object[])">
            <summary>
            Execute a function that returns a byte array
            </summary>
            <returns></returns>
        </member>
        <member name="M:Npgsql.NpgsqlLargeObjectManager.Create(System.UInt32)">
            <summary>
            Create an empty large object in the database. If an oid is specified but is already in use, an PostgresException will be thrown.
            </summary>
            <param name="preferredOid">A preferred oid, or specify 0 if one should be automatically assigned</param>
            <returns>The oid for the large object created</returns>
            <exception cref="T:Npgsql.PostgresException">If an oid is already in use</exception>
        </member>
        <member name="M:Npgsql.NpgsqlLargeObjectManager.CreateAsync(System.UInt32,System.Threading.CancellationToken)">
            <summary>
            Create an empty large object in the database. If an oid is specified but is already in use, an PostgresException will be thrown.
            </summary>
            <param name="preferredOid">A preferred oid, or specify 0 if one should be automatically assigned</param>
            <param name="cancellationToken">Cancellation token.</param>
            <returns>The oid for the large object created</returns>
            <exception cref="T:Npgsql.PostgresException">If an oid is already in use</exception>
        </member>
        <member name="M:Npgsql.NpgsqlLargeObjectManager.OpenRead(System.UInt32)">
            <summary>
            Opens a large object on the backend, returning a stream controlling this remote object.
            A transaction snapshot is taken by the backend when the object is opened with only read permissions.
            When reading from this object, the contents reflects the time when the snapshot was taken.
            Note that this method, as well as operations on the stream must be wrapped inside a transaction.
            </summary>
            <param name="oid">Oid of the object</param>
            <returns>An NpgsqlLargeObjectStream</returns>
        </member>
        <member name="M:Npgsql.NpgsqlLargeObjectManager.OpenReadAsync(System.UInt32,System.Threading.CancellationToken)">
            <summary>
            Opens a large object on the backend, returning a stream controlling this remote object.
            A transaction snapshot is taken by the backend when the object is opened with only read permissions.
            When reading from this object, the contents reflects the time when the snapshot was taken.
            Note that this method, as well as operations on the stream must be wrapped inside a transaction.
            </summary>
            <param name="oid">Oid of the object</param>
            <param name="cancellationToken">Cancellation token.</param>
            <returns>An NpgsqlLargeObjectStream</returns>
        </member>
        <member name="M:Npgsql.NpgsqlLargeObjectManager.OpenReadWrite(System.UInt32)">
            <summary>
            Opens a large object on the backend, returning a stream controlling this remote object.
            Note that this method, as well as operations on the stream must be wrapped inside a transaction.
            </summary>
            <param name="oid">Oid of the object</param>
            <returns>An NpgsqlLargeObjectStream</returns>
        </member>
        <member name="M:Npgsql.NpgsqlLargeObjectManager.OpenReadWriteAsync(System.UInt32,System.Threading.CancellationToken)">
            <summary>
            Opens a large object on the backend, returning a stream controlling this remote object.
            Note that this method, as well as operations on the stream must be wrapped inside a transaction.
            </summary>
            <param name="oid">Oid of the object</param>
            <param name="cancellationToken">Cancellation token.</param>
            <returns>An NpgsqlLargeObjectStream</returns>
        </member>
        <member name="M:Npgsql.NpgsqlLargeObjectManager.Unlink(System.UInt32)">
            <summary>
            Deletes a large object on the backend.
            </summary>
            <param name="oid">Oid of the object to delete</param>
        </member>
        <member name="M:Npgsql.NpgsqlLargeObjectManager.UnlinkAsync(System.UInt32,System.Threading.CancellationToken)">
            <summary>
            Deletes a large object on the backend.
            </summary>
            <param name="oid">Oid of the object to delete</param>
            <param name="cancellationToken">Cancellation token.</param>
        </member>
        <member name="M:Npgsql.NpgsqlLargeObjectManager.ExportRemote(System.UInt32,System.String)">
            <summary>
            Exports a large object stored in the database to a file on the backend. This requires superuser permissions.
            </summary>
            <param name="oid">Oid of the object to export</param>
            <param name="path">Path to write the file on the backend</param>
        </member>
        <member name="M:Npgsql.NpgsqlLargeObjectManager.ExportRemoteAsync(System.UInt32,System.String,System.Threading.CancellationToken)">
            <summary>
            Exports a large object stored in the database to a file on the backend. This requires superuser permissions.
            </summary>
            <param name="oid">Oid of the object to export</param>
            <param name="path">Path to write the file on the backend</param>
            <param name="cancellationToken">Cancellation token.</param>
        </member>
        <member name="M:Npgsql.NpgsqlLargeObjectManager.ImportRemote(System.String,System.UInt32)">
            <summary>
            Imports a large object to be stored as a large object in the database from a file stored on the backend. This requires superuser permissions.
            </summary>
            <param name="path">Path to read the file on the backend</param>
            <param name="oid">A preferred oid, or specify 0 if one should be automatically assigned</param>
        </member>
        <member name="M:Npgsql.NpgsqlLargeObjectManager.ImportRemoteAsync(System.String,System.UInt32,System.Threading.CancellationToken)">
            <summary>
            Imports a large object to be stored as a large object in the database from a file stored on the backend. This requires superuser permissions.
            </summary>
            <param name="path">Path to read the file on the backend</param>
            <param name="oid">A preferred oid, or specify 0 if one should be automatically assigned</param>
            <param name="cancellationToken">Cancellation token.</param>
        </member>
        <member name="P:Npgsql.NpgsqlLargeObjectManager.Has64BitSupport">
            <summary>
            Since PostgreSQL 9.3, large objects larger than 2GB can be handled, up to 4TB.
            This property returns true whether the PostgreSQL version is >= 9.3.
            </summary>
        </member>
        <member name="T:Npgsql.NpgsqlLargeObjectStream">
            <summary>
            An interface to remotely control the seekable stream for an opened large object on a PostgreSQL server.
            Note that the OpenRead/OpenReadWrite method as well as all operations performed on this stream must be wrapped inside a database transaction.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlLargeObjectStream.Has64BitSupport">
            <summary>
            Since PostgreSQL 9.3, large objects larger than 2GB can be handled, up to 4TB.
            This property returns true whether the PostgreSQL version is >= 9.3.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlLargeObjectStream.Read(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Reads <i>count</i> bytes from the large object. The only case when fewer bytes are read is when end of stream is reached.
            </summary>
            <param name="buffer">The buffer where read data should be stored.</param>
            <param name="offset">The offset in the buffer where the first byte should be read.</param>
            <param name="count">The maximum number of bytes that should be read.</param>
            <returns>How many bytes actually read, or 0 if end of file was already reached.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlLargeObjectStream.ReadAsync(System.Byte[],System.Int32,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Reads <i>count</i> bytes from the large object. The only case when fewer bytes are read is when end of stream is reached.
            </summary>
            <param name="buffer">The buffer where read data should be stored.</param>
            <param name="offset">The offset in the buffer where the first byte should be read.</param>
            <param name="count">The maximum number of bytes that should be read.</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>How many bytes actually read, or 0 if end of file was already reached.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlLargeObjectStream.Write(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Writes <i>count</i> bytes to the large object.
            </summary>
            <param name="buffer">The buffer to write data from.</param>
            <param name="offset">The offset in the buffer at which to begin copying bytes.</param>
            <param name="count">The number of bytes to write.</param>
        </member>
        <member name="M:Npgsql.NpgsqlLargeObjectStream.WriteAsync(System.Byte[],System.Int32,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Writes <i>count</i> bytes to the large object.
            </summary>
            <param name="buffer">The buffer to write data from.</param>
            <param name="offset">The offset in the buffer at which to begin copying bytes.</param>
            <param name="count">The number of bytes to write.</param>
            <param name="cancellationToken">Cancellation token.</param>
        </member>
        <member name="P:Npgsql.NpgsqlLargeObjectStream.CanTimeout">
            <summary>
            CanTimeout always returns false.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlLargeObjectStream.CanRead">
            <summary>
            CanRead always returns true, unless the stream has been closed.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlLargeObjectStream.CanWrite">
            <summary>
            CanWrite returns true if the stream was opened with write permissions, and the stream has not been closed.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlLargeObjectStream.CanSeek">
            <summary>
            CanSeek always returns true, unless the stream has been closed.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlLargeObjectStream.Position">
            <summary>
            Returns the current position in the stream. Getting the current position does not need a round-trip to the server, however setting the current position does.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlLargeObjectStream.Length">
            <summary>
            Gets the length of the large object. This internally seeks to the end of the stream to retrieve the length, and then back again.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlLargeObjectStream.GetLengthAsync">
            <summary>
            Gets the length of the large object. This internally seeks to the end of the stream to retrieve the length, and then back again.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlLargeObjectStream.Seek(System.Int64,System.IO.SeekOrigin)">
            <summary>
            Seeks in the stream to the specified position. This requires a round-trip to the backend.
            </summary>
            <param name="offset">A byte offset relative to the <i>origin</i> parameter.</param>
            <param name="origin">A value of type SeekOrigin indicating the reference point used to obtain the new position.</param>
            <returns></returns>
        </member>
        <member name="M:Npgsql.NpgsqlLargeObjectStream.SeekAsync(System.Int64,System.IO.SeekOrigin,System.Threading.CancellationToken)">
            <summary>
            Seeks in the stream to the specified position. This requires a round-trip to the backend.
            </summary>
            <param name="offset">A byte offset relative to the <i>origin</i> parameter.</param>
            <param name="origin">A value of type SeekOrigin indicating the reference point used to obtain the new position.</param>
            <param name="cancellationToken">Cancellation token.</param>
            <returns></returns>
        </member>
        <member name="M:Npgsql.NpgsqlLargeObjectStream.Flush">
            <summary>
            Does nothing.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlLargeObjectStream.SetLength(System.Int64)">
            <summary>
            Truncates or enlarges the large object to the given size. If enlarging, the large object is extended with null bytes.
            For PostgreSQL versions earlier than 9.3, the value must fit in an Int32.
            </summary>
            <param name="value">Number of bytes to either truncate or enlarge the large object.</param>
        </member>
        <member name="M:Npgsql.NpgsqlLargeObjectStream.SetLength(System.Int64,System.Threading.CancellationToken)">
            <summary>
            Truncates or enlarges the large object to the given size. If enlarging, the large object is extended with null bytes.
            For PostgreSQL versions earlier than 9.3, the value must fit in an Int32.
            </summary>
            <param name="value">Number of bytes to either truncate or enlarge the large object.</param>
            <param name="cancellationToken">Cancellation token.</param>
        </member>
        <member name="M:Npgsql.NpgsqlLargeObjectStream.Close">
            <summary>
            Releases resources at the backend allocated for this stream.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlLargeObjectStream.Dispose(System.Boolean)">
            <summary>
            Releases resources at the backend allocated for this stream, iff disposing is true.
            </summary>
            <param name="disposing">Whether to release resources allocated at the backend.</param>
        </member>
        <member name="T:Npgsql.NpgsqlLengthCache">
             <summary>
             An array of cached lengths for the parameters sending process.
            
             When sending parameters, lengths need to be calculated more than once (once for Bind, once for
             an array, once for the string within that array). This cache optimized that. Lengths are added
             to the cache, and then retrieved at the same order.
             </summary>
        </member>
        <member name="M:Npgsql.NpgsqlLengthCache.Set(System.Int32)">
            <summary>
            Stores a length value in the cache, to be fetched later via <see cref="M:Npgsql.NpgsqlLengthCache.Get"/>.
            Called at the <see cref="M:Npgsql.TypeHandling.NpgsqlTypeHandler.ValidateAndGetLength``1(``0,Npgsql.NpgsqlLengthCache@,Npgsql.NpgsqlParameter)"/> phase.
            </summary>
            <returns>The length parameter.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlLengthCache.Get">
            <summary>
            Retrieves a length value previously stored in the cache via <see cref="M:Npgsql.NpgsqlLengthCache.Set(System.Int32)"/>.
            Called at the writing phase, after validation has already occurred and the length cache is populated.
            </summary>
            <returns></returns>
        </member>
        <member name="T:Npgsql.NpgsqlNotificationEventArgs">
            <summary>
            Provides information on a PostgreSQL notification. Notifications are sent when your connection has registered for
            notifications on a specific channel via the LISTEN command. NOTIFY can be used to generate such notifications,
            allowing for an inter-connection communication channel.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlNotificationEventArgs.PID">
            <summary>
            Process ID of the PostgreSQL backend that sent this notification.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlNotificationEventArgs.Condition">
            <summary>
            The channel on which the notification was sent.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlNotificationEventArgs.AdditionalInformation">
            <summary>
            An optional payload string that was sent with this notification.
            </summary>
        </member>
        <member name="T:Npgsql.NpgsqlOperationInProgressException">
            <summary>
            Thrown when trying to use a connection that is already busy performing some other operation.
            Provides information on the already-executing operation to help with debugging.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlOperationInProgressException.CommandInProgress">
            <summary>
            If the connection is busy with another command, this will contain a reference to that command.
            Otherwise, if the connection if busy with another type of operation (e.g. COPY), contains null.
            </summary>
        </member>
        <member name="T:Npgsql.NpgsqlParameter">
            <summary>
             This class represents a parameter to a command that will be sent to server
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlParameter.ConvertedValue">
            <summary>
            Can be used to communicate a value from the validation phase to the writing phase.
            To be used by type handlers only.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlParameter.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> class.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlParameter.#ctor(System.String,System.Object)">
            <summary>
            Initializes a new instance of the <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see>
            class with the parameter name and a value of the new <b>NpgsqlParameter</b>.
            </summary>
            <param name="parameterName">The name of the parameter to map.</param>
            <param name="value">An <see cref="T:System.Object">Object</see> that is the value of the <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see>.</param>
            <remarks>
            <p>When you specify an <see cref="T:System.Object">Object</see>
            in the value parameter, the <see cref="T:System.Data.DbType">DbType</see> is
            inferred from the .NET Framework type of the <b>Object</b>.</p>
            <p>When using this constructor, you must be aware of a possible misuse of the constructor which takes a DbType parameter.
            This happens when calling this constructor passing an int 0 and the compiler thinks you are passing a value of DbType.
            Use <code> Convert.ToInt32(value) </code> for example to have compiler calling the correct constructor.</p>
            </remarks>
        </member>
        <member name="M:Npgsql.NpgsqlParameter.#ctor(System.String,NpgsqlTypes.NpgsqlDbType)">
            <summary>
            Initializes a new instance of the <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see>
            class with the parameter name and the data type.
            </summary>
            <param name="parameterName">The name of the parameter to map.</param>
            <param name="parameterType">One of the <see cref="T:System.Data.DbType">DbType</see> values.</param>
        </member>
        <member name="M:Npgsql.NpgsqlParameter.#ctor(System.String,System.Data.DbType)">
            <summary>
            Initializes a new instance of the <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see>.
            </summary>
            <param name="parameterName">The name of the parameter to map.</param>
            <param name="parameterType">One of the <see cref="T:System.Data.DbType">DbType</see> values.</param>
        </member>
        <member name="M:Npgsql.NpgsqlParameter.#ctor(System.String,NpgsqlTypes.NpgsqlDbType,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see>.
            </summary>
            <param name="parameterName">The name of the parameter to map.</param>
            <param name="parameterType">One of the <see cref="T:NpgsqlTypes.NpgsqlDbType">NpgsqlDbType</see> values.</param>
            <param name="size">The length of the parameter.</param>
        </member>
        <member name="M:Npgsql.NpgsqlParameter.#ctor(System.String,System.Data.DbType,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see>.
            </summary>
            <param name="parameterName">The name of the parameter to map.</param>
            <param name="parameterType">One of the <see cref="T:System.Data.DbType">DbType</see> values.</param>
            <param name="size">The length of the parameter.</param>
        </member>
        <member name="M:Npgsql.NpgsqlParameter.#ctor(System.String,NpgsqlTypes.NpgsqlDbType,System.Int32,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see>
            </summary>
            <param name="parameterName">The name of the parameter to map.</param>
            <param name="parameterType">One of the <see cref="T:NpgsqlTypes.NpgsqlDbType">NpgsqlDbType</see> values.</param>
            <param name="size">The length of the parameter.</param>
            <param name="sourceColumn">The name of the source column.</param>
        </member>
        <member name="M:Npgsql.NpgsqlParameter.#ctor(System.String,System.Data.DbType,System.Int32,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see>.
            </summary>
            <param name="parameterName">The name of the parameter to map.</param>
            <param name="parameterType">One of the <see cref="T:System.Data.DbType">DbType</see> values.</param>
            <param name="size">The length of the parameter.</param>
            <param name="sourceColumn">The name of the source column.</param>
        </member>
        <member name="M:Npgsql.NpgsqlParameter.#ctor(System.String,NpgsqlTypes.NpgsqlDbType,System.Int32,System.String,System.Data.ParameterDirection,System.Boolean,System.Byte,System.Byte,System.Data.DataRowVersion,System.Object)">
            <summary>
            Initializes a new instance of the <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see>.
            </summary>
            <param name="parameterName">The name of the parameter to map.</param>
            <param name="parameterType">One of the <see cref="T:NpgsqlTypes.NpgsqlDbType">NpgsqlDbType</see> values.</param>
            <param name="size">The length of the parameter.</param>
            <param name="sourceColumn">The name of the source column.</param>
            <param name="direction">One of the <see cref="T:System.Data.ParameterDirection">ParameterDirection</see> values.</param>
            <param name="isNullable"><b>true</b> if the value of the field can be null, otherwise <b>false</b>.</param>
            <param name="precision">The total number of digits to the left and right of the decimal point to which
            <see cref="P:Npgsql.NpgsqlParameter.Value">Value</see> is resolved.</param>
            <param name="scale">The total number of decimal places to which
            <see cref="P:Npgsql.NpgsqlParameter.Value">Value</see> is resolved.</param>
            <param name="sourceVersion">One of the <see cref="T:System.Data.DataRowVersion">DataRowVersion</see> values.</param>
            <param name="value">An <see cref="T:System.Object">Object</see> that is the value
            of the <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see>.</param>
        </member>
        <member name="M:Npgsql.NpgsqlParameter.#ctor(System.String,System.Data.DbType,System.Int32,System.String,System.Data.ParameterDirection,System.Boolean,System.Byte,System.Byte,System.Data.DataRowVersion,System.Object)">
            <summary>
            Initializes a new instance of the <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see>.
            </summary>
            <param name="parameterName">The name of the parameter to map.</param>
            <param name="parameterType">One of the <see cref="T:System.Data.DbType">DbType</see> values.</param>
            <param name="size">The length of the parameter.</param>
            <param name="sourceColumn">The name of the source column.</param>
            <param name="direction">One of the <see cref="T:System.Data.ParameterDirection">ParameterDirection</see> values.</param>
            <param name="isNullable"><b>true</b> if the value of the field can be null, otherwise <b>false</b>.</param>
            <param name="precision">The total number of digits to the left and right of the decimal point to which
            <see cref="P:Npgsql.NpgsqlParameter.Value">Value</see> is resolved.</param>
            <param name="scale">The total number of decimal places to which
            <see cref="P:Npgsql.NpgsqlParameter.Value">Value</see> is resolved.</param>
            <param name="sourceVersion">One of the <see cref="T:System.Data.DataRowVersion">DataRowVersion</see> values.</param>
            <param name="value">An <see cref="T:System.Object">Object</see> that is the value
            of the <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see>.</param>
        </member>
        <member name="P:Npgsql.NpgsqlParameter.ParameterName">
            <summary>
            Gets or sets The name of the <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see>.
            </summary>
            <value>The name of the <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see>.
            The default is an empty string.</value>
        </member>
        <member name="P:Npgsql.NpgsqlParameter.Value">
            <inheritdoc />
        </member>
        <member name="P:Npgsql.NpgsqlParameter.NpgsqlValue">
            <summary>
            Gets or sets the value of the parameter.
            </summary>
            <value>An <see cref="T:System.Object">Object</see> that is the value of the parameter.
            The default value is null.</value>
        </member>
        <member name="P:Npgsql.NpgsqlParameter.DbType">
            <summary>
            Gets or sets the <see cref="T:System.Data.DbType">DbType</see> of the parameter.
            </summary>
            <value>One of the <see cref="T:System.Data.DbType">DbType</see> values. The default is <b>Object</b>.</value>
        </member>
        <member name="P:Npgsql.NpgsqlParameter.NpgsqlDbType">
            <summary>
            Gets or sets the <see cref="T:NpgsqlTypes.NpgsqlDbType">NpgsqlDbType</see> of the parameter.
            </summary>
            <value>One of the <see cref="T:NpgsqlTypes.NpgsqlDbType">NpgsqlDbType</see> values. The default is <b>Unknown</b>.</value>
        </member>
        <member name="P:Npgsql.NpgsqlParameter.DataTypeName">
            <summary>
            Used to specify which PostgreSQL type will be sent to the database for this parameter.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlParameter.IsNullable">
            <inheritdoc />
        </member>
        <member name="P:Npgsql.NpgsqlParameter.Direction">
            <inheritdoc />
        </member>
        <member name="P:Npgsql.NpgsqlParameter.Precision">
            <summary>
            Gets or sets the maximum number of digits used to represent the
            <see cref="P:Npgsql.NpgsqlParameter.Value">Value</see> property.
            </summary>
            <value>The maximum number of digits used to represent the
            <see cref="P:Npgsql.NpgsqlParameter.Value">Value</see> property.
            The default value is 0, which indicates that the data provider
            sets the precision for <b>Value</b>.</value>
        </member>
        <member name="P:Npgsql.NpgsqlParameter.Scale">
            <summary>
            Gets or sets the number of decimal places to which
            <see cref="P:Npgsql.NpgsqlParameter.Value">Value</see> is resolved.
            </summary>
            <value>The number of decimal places to which
            <see cref="P:Npgsql.NpgsqlParameter.Value">Value</see> is resolved. The default is 0.</value>
        </member>
        <member name="P:Npgsql.NpgsqlParameter.Size">
            <inheritdoc />
        </member>
        <member name="P:Npgsql.NpgsqlParameter.SourceColumn">
            <inheritdoc />
        </member>
        <member name="P:Npgsql.NpgsqlParameter.SourceVersion">
            <inheritdoc />
        </member>
        <member name="P:Npgsql.NpgsqlParameter.SourceColumnNullMapping">
            <inheritdoc />
        </member>
        <member name="P:Npgsql.NpgsqlParameter.Collection">
            <summary>
            The collection to which this parameter belongs, if any.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlParameter.PostgresType">
            <summary>
            The PostgreSQL data type, such as int4 or text, as discovered from pg_type.
            This property is automatically set if parameters have been derived via
            <see cref="M:Npgsql.NpgsqlCommandBuilder.DeriveParameters(Npgsql.NpgsqlCommand)"/> and can be used to
            acquire additional information about the parameters' data type.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlParameter.IsTypeExplicitlySet">
            <summary>
            Returns whether this parameter has had its type set explicitly via DbType or NpgsqlDbType
            (and not via type inference)
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlParameter.ResetDbType">
            <inheritdoc />
        </member>
        <member name="M:Npgsql.NpgsqlParameter.Clone">
            <summary>
            Creates a new <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> that
            is a copy of the current instance.
            </summary>
            <returns>A new <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> that is a copy of this instance.</returns>
        </member>
        <member name="T:Npgsql.NpgsqlParameterCollection">
            <summary>
            Represents a collection of parameters relevant to a <see cref="T:Npgsql.NpgsqlCommand">NpgsqlCommand</see>
            as well as their respective mappings to columns in a DataSet.
            This class cannot be inherited.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlParameterCollection.#ctor">
            <summary>
            Initializes a new instance of the NpgsqlParameterCollection class.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlParameterCollection.InvalidateHashLookups">
            <summary>
            Invalidate the hash lookup tables.  This should be done any time a change
            may throw the lookups out of sync with the list.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlParameterCollection.Item(System.String)">
            <summary>
            Gets the <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> with the specified name.
            </summary>
            <param name="parameterName">The name of the <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> to retrieve.</param>
            <value>The <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> with the specified name, or a null reference if the parameter is not found.</value>
        </member>
        <member name="P:Npgsql.NpgsqlParameterCollection.Item(System.Int32)">
            <summary>
            Gets the <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> at the specified index.
            </summary>
            <param name="index">The zero-based index of the <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> to retrieve.</param>
            <value>The <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> at the specified index.</value>
        </member>
        <member name="M:Npgsql.NpgsqlParameterCollection.Add(Npgsql.NpgsqlParameter)">
            <summary>
            Adds the specified <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> object to the <see cref="T:Npgsql.NpgsqlParameterCollection">NpgsqlParameterCollection</see>.
            </summary>
            <param name="value">The <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> to add to the collection.</param>
            <returns>The index of the new <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> object.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlParameterCollection.System#Collections#Generic#ICollection{Npgsql#NpgsqlParameter}#Add(Npgsql.NpgsqlParameter)">
            <inheritdoc />
        </member>
        <member name="M:Npgsql.NpgsqlParameterCollection.AddWithValue(System.String,System.Object)">
            <summary>
            Adds a <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> to the <see cref="T:Npgsql.NpgsqlParameterCollection">NpgsqlParameterCollection</see> given the specified parameter name and value.
            </summary>
            <param name="parameterName">The name of the <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see>.</param>
            <param name="value">The Value of the <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> to add to the collection.</param>
            <returns>The paramater that was added.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlParameterCollection.AddWithValue(System.String,NpgsqlTypes.NpgsqlDbType,System.Object)">
            <summary>
            Adds a <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> to the <see cref="T:Npgsql.NpgsqlParameterCollection">NpgsqlParameterCollection</see> given the specified parameter name, data type and value.
            </summary>
            <param name="parameterName">The name of the <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see>.</param>
            <param name="parameterType">One of the NpgsqlDbType values.</param>
            <param name="value">The Value of the <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> to add to the collection.</param>
            <returns>The paramater that was added.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlParameterCollection.AddWithValue(System.String,NpgsqlTypes.NpgsqlDbType,System.Int32,System.Object)">
            <summary>
            Adds a <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> to the <see cref="T:Npgsql.NpgsqlParameterCollection">NpgsqlParameterCollection</see> given the specified parameter name and value.
            </summary>
            <param name="parameterName">The name of the <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see>.</param>
            <param name="value">The Value of the <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> to add to the collection.</param>
            <param name="parameterType">One of the NpgsqlDbType values.</param>
            <param name="size">The length of the column.</param>
            <returns>The paramater that was added.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlParameterCollection.AddWithValue(System.String,NpgsqlTypes.NpgsqlDbType,System.Int32,System.String,System.Object)">
            <summary>
            Adds a <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> to the <see cref="T:Npgsql.NpgsqlParameterCollection">NpgsqlParameterCollection</see> given the specified parameter name and value.
            </summary>
            <param name="parameterName">The name of the <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see>.</param>
            <param name="value">The Value of the <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> to add to the collection.</param>
            <param name="parameterType">One of the NpgsqlDbType values.</param>
            <param name="size">The length of the column.</param>
            <param name="sourceColumn">The name of the source column.</param>
            <returns>The paramater that was added.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlParameterCollection.AddWithValue(System.Object)">
            <summary>
            Adds a <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> to the <see cref="T:Npgsql.NpgsqlParameterCollection">NpgsqlParameterCollection</see> given the specified value.
            </summary>
            <param name="value">The Value of the <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> to add to the collection.</param>
            <returns>The paramater that was added.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlParameterCollection.AddWithValue(NpgsqlTypes.NpgsqlDbType,System.Object)">
            <summary>
            Adds a <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> to the <see cref="T:Npgsql.NpgsqlParameterCollection">NpgsqlParameterCollection</see> given the specified data type and value.
            </summary>
            <param name="parameterType">One of the NpgsqlDbType values.</param>
            <param name="value">The Value of the <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> to add to the collection.</param>
            <returns>The paramater that was added.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlParameterCollection.Add(System.String,NpgsqlTypes.NpgsqlDbType)">
            <summary>
            Adds a <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> to the <see cref="T:Npgsql.NpgsqlParameterCollection">NpgsqlParameterCollection</see> given the parameter name and the data type.
            </summary>
            <param name="parameterName">The name of the parameter.</param>
            <param name="parameterType">One of the DbType values.</param>
            <returns>The index of the new <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> object.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlParameterCollection.Add(System.String,NpgsqlTypes.NpgsqlDbType,System.Int32)">
            <summary>
            Adds a <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> to the <see cref="T:Npgsql.NpgsqlParameterCollection">NpgsqlParameterCollection</see> with the parameter name, the data type, and the column length.
            </summary>
            <param name="parameterName">The name of the parameter.</param>
            <param name="parameterType">One of the DbType values.</param>
            <param name="size">The length of the column.</param>
            <returns>The index of the new <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> object.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlParameterCollection.Add(System.String,NpgsqlTypes.NpgsqlDbType,System.Int32,System.String)">
            <summary>
            Adds a <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> to the <see cref="T:Npgsql.NpgsqlParameterCollection">NpgsqlParameterCollection</see> with the parameter name, the data type, the column length, and the source column name.
            </summary>
            <param name="parameterName">The name of the parameter.</param>
            <param name="parameterType">One of the DbType values.</param>
            <param name="size">The length of the column.</param>
            <param name="sourceColumn">The name of the source column.</param>
            <returns>The index of the new <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> object.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlParameterCollection.RemoveAt(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Npgsql.NpgsqlParameterCollection.Contains(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Npgsql.NpgsqlParameterCollection.IndexOf(System.String)">
            <inheritdoc />
        </member>
        <member name="P:Npgsql.NpgsqlParameterCollection.IsReadOnly">
            <inheritdoc />
        </member>
        <member name="M:Npgsql.NpgsqlParameterCollection.RemoveAt(System.Int32)">
            <summary>
            Removes the specified <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> from the collection using a specific index.
            </summary>
            <param name="index">The zero-based index of the parameter.</param>
        </member>
        <member name="M:Npgsql.NpgsqlParameterCollection.Insert(System.Int32,System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Npgsql.NpgsqlParameterCollection.Remove(System.String)">
            <summary>
            Removes the specified <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> from the collection.
            </summary>
            <param name="parameterName">The name of the <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> to remove from the collection.</param>
        </member>
        <member name="M:Npgsql.NpgsqlParameterCollection.Remove(System.Object)">
            <summary>
            Removes the specified <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> from the collection.
            </summary>
            <param name="value">The <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> to remove from the collection.</param>
        </member>
        <member name="M:Npgsql.NpgsqlParameterCollection.Contains(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Npgsql.NpgsqlParameterCollection.TryGetValue(System.String,Npgsql.NpgsqlParameter@)">
            <summary>
            Gets a value indicating whether a <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> with the specified parameter name exists in the collection.
            </summary>
            <param name="parameterName">The name of the <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> object to find.</param>
            <param name="parameter">A reference to the requested parameter is returned in this out param if it is found in the list.  This value is null if the parameter is not found.</param>
            <returns><b>true</b> if the collection contains the parameter and param will contain the parameter; otherwise, <b>false</b>.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlParameterCollection.Clear">
            <summary>
            Removes all items from the collection.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlParameterCollection.IndexOf(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Npgsql.NpgsqlParameterCollection.Add(System.Object)">
            <inheritdoc />
        </member>
        <member name="P:Npgsql.NpgsqlParameterCollection.IsFixedSize">
            <inheritdoc />
        </member>
        <member name="P:Npgsql.NpgsqlParameterCollection.IsSynchronized">
            <inheritdoc />
        </member>
        <member name="P:Npgsql.NpgsqlParameterCollection.Count">
            <summary>
            Gets the number of <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> objects in the collection.
            </summary>
            <value>The number of <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> objects in the collection.</value>
        </member>
        <member name="M:Npgsql.NpgsqlParameterCollection.CopyTo(System.Array,System.Int32)">
            <inheritdoc />
        </member>
        <member name="P:Npgsql.NpgsqlParameterCollection.System#Collections#Generic#ICollection{Npgsql#NpgsqlParameter}#IsReadOnly">
            <inheritdoc />
        </member>
        <member name="P:Npgsql.NpgsqlParameterCollection.SyncRoot">
            <inheritdoc />
        </member>
        <member name="M:Npgsql.NpgsqlParameterCollection.GetEnumerator">
            <inheritdoc />
        </member>
        <member name="M:Npgsql.NpgsqlParameterCollection.AddRange(System.Array)">
            <inheritdoc />
        </member>
        <member name="M:Npgsql.NpgsqlParameterCollection.GetParameter(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Npgsql.NpgsqlParameterCollection.GetParameter(System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:Npgsql.NpgsqlParameterCollection.SetParameter(System.String,System.Data.Common.DbParameter)">
            <inheritdoc />
        </member>
        <member name="M:Npgsql.NpgsqlParameterCollection.SetParameter(System.Int32,System.Data.Common.DbParameter)">
            <inheritdoc />
        </member>
        <member name="M:Npgsql.NpgsqlParameterCollection.IndexOf(Npgsql.NpgsqlParameter)">
            <summary>
            Report the offset within the collection of the given parameter.
            </summary>
            <param name="item">Parameter to find.</param>
            <returns>Index of the parameter, or -1 if the parameter is not present.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlParameterCollection.Insert(System.Int32,Npgsql.NpgsqlParameter)">
            <summary>
            Insert the specified parameter into the collection.
            </summary>
            <param name="index">Index of the existing parameter before which to insert the new one.</param>
            <param name="item">Parameter to insert.</param>
        </member>
        <member name="M:Npgsql.NpgsqlParameterCollection.Contains(Npgsql.NpgsqlParameter)">
            <summary>
            Report whether the specified parameter is present in the collection.
            </summary>
            <param name="item">Parameter to find.</param>
            <returns>True if the parameter was found, otherwise false.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlParameterCollection.Remove(Npgsql.NpgsqlParameter)">
            <summary>
            Remove the specified parameter from the collection.
            </summary>
            <param name="item">Parameter to remove.</param>
            <returns>True if the parameter was found and removed, otherwise false.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlParameterCollection.CopyTo(Npgsql.NpgsqlParameter[],System.Int32)">
            <summary>
            Convert collection to a System.Array.
            </summary>
            <param name="array">Destination array.</param>
            <param name="arrayIndex">Starting index in destination array.</param>
        </member>
        <member name="M:Npgsql.NpgsqlParameterCollection.ToArray">
            <summary>
            Convert collection to a System.Array.
            </summary>
            <returns>NpgsqlParameter[]</returns>
        </member>
        <member name="T:Npgsql.NpgsqlParameter`1">
            <summary>
            A generic version of <see cref="T:Npgsql.NpgsqlParameter"/> which provides more type safety and
            avoids boxing of value types. Use <see cref="P:Npgsql.NpgsqlParameter`1.TypedValue"/> instead of <see cref="P:Npgsql.NpgsqlParameter.Value"/>.
            </summary>
            <typeparam name="T">The type of the value that will be stored in the parameter.</typeparam>
        </member>
        <member name="P:Npgsql.NpgsqlParameter`1.TypedValue">
            <summary>
            Gets or sets the strongly-typed value of the parameter.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlParameter`1.Value">
            <summary>
            Gets or sets the value of the parameter. This delegates to <see cref="P:Npgsql.NpgsqlParameter`1.TypedValue"/>.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlParameter`1.#ctor">
            <summary>
            Initializes a new instance of <see cref="T:Npgsql.NpgsqlParameter`1" />.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlParameter`1.#ctor(System.String,`0)">
            <summary>
            Initializes a new instance of <see cref="T:Npgsql.NpgsqlParameter`1" /> with a parameter name and value.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlParameter`1.#ctor(System.String,NpgsqlTypes.NpgsqlDbType)">
            <summary>
            Initializes a new instance of <see cref="T:Npgsql.NpgsqlParameter`1" /> with a parameter name and type.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlParameter`1.#ctor(System.String,System.Data.DbType)">
            <summary>
            Initializes a new instance of <see cref="T:Npgsql.NpgsqlParameter`1" /> with a parameter name and type.
            </summary>
        </member>
        <member name="T:Npgsql.NpgsqlRawCopyStream">
            <summary>
            Provides an API for a raw binary COPY operation, a high-performance data import/export mechanism to
            a PostgreSQL table. Initiated by <see cref="M:Npgsql.NpgsqlConnection.BeginRawBinaryCopy(System.String)"/>
            </summary>
            <remarks>
            See http://www.postgresql.org/docs/current/static/sql-copy.html.
            </remarks>
        </member>
        <member name="F:Npgsql.NpgsqlRawCopyStream.BinarySignature">
            <summary>
            The copy binary format header signature
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlRawCopyStream.Cancel">
            <summary>
            Cancels and terminates an ongoing operation. Any data already written will be discarded.
            </summary>
        </member>
        <member name="T:Npgsql.NpgsqlCopyTextWriter">
            <summary>
            Writer for a text import, initiated by <see cref="M:Npgsql.NpgsqlConnection.BeginTextImport(System.String)"/>.
            </summary>
            <remarks>
            See http://www.postgresql.org/docs/current/static/sql-copy.html.
            </remarks>
        </member>
        <member name="M:Npgsql.NpgsqlCopyTextWriter.Cancel">
            <summary>
            Cancels and terminates an ongoing import. Any data already written will be discarded.
            </summary>
        </member>
        <member name="T:Npgsql.NpgsqlCopyTextReader">
            <summary>
            Reader for a text export, initiated by <see cref="M:Npgsql.NpgsqlConnection.BeginTextExport(System.String)"/>.
            </summary>
            <remarks>
            See http://www.postgresql.org/docs/current/static/sql-copy.html.
            </remarks>
        </member>
        <member name="M:Npgsql.NpgsqlCopyTextReader.Cancel">
            <summary>
            Cancels and terminates an ongoing import.
            </summary>
        </member>
        <member name="T:Npgsql.NpgsqlReadBuffer">
            <summary>
            A buffer used by Npgsql to read data from the socket efficiently.
            Provides methods which decode different values types and tracks the current position.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlReadBuffer.Size">
            <summary>
            The total byte length of the buffer.
            </summary>
        </member>
        <member name="F:Npgsql.NpgsqlReadBuffer.MinimumSize">
            <summary>
            The minimum buffer size possible.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlReadBuffer.Ensure(System.Int32,System.Boolean)">
            <summary>
            Ensures that <paramref name="count"/> bytes are available in the buffer, and if
            not, reads from the socket until enough is available.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlReadBuffer.Skip(System.Int64)">
            <summary>
            Does not perform any I/O - assuming that the bytes to be skipped are in the memory buffer.
            </summary>
            <param name="len"></param>
        </member>
        <member name="M:Npgsql.NpgsqlReadBuffer.Skip(System.Int64,System.Boolean)">
            <summary>
            Skip a given number of bytes.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlReadBuffer.ReadNullTerminatedString">
            <summary>
            Seeks the first null terminator (\0) and returns the string up to it. The buffer must already
            contain the entire string and its terminator.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlReadBuffer.ReadNullTerminatedString(System.Text.Encoding)">
            <summary>
            Seeks the first null terminator (\0) and returns the string up to it. The buffer must already
            contain the entire string and its terminator.
            </summary>
            <param name="encoding">Decodes the messages with this encoding.</param>
        </member>
        <member name="T:Npgsql.NpgsqlSchema">
            <summary>
            Provides the underlying mechanism for reading schema information.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlSchema.GetMetaDataCollections">
            <summary>
            Returns the MetaDataCollections that lists all possible collections.
            </summary>
            <returns>The MetaDataCollections</returns>
        </member>
        <member name="M:Npgsql.NpgsqlSchema.GetRestrictions">
            <summary>
            Returns the Restrictions that contains the meaning and position of the values in the restrictions array.
            </summary>
            <returns>The Restrictions</returns>
        </member>
        <member name="M:Npgsql.NpgsqlSchema.GetDatabases(Npgsql.NpgsqlConnection,System.String[])">
            <summary>
            Returns the Databases that contains a list of all accessable databases.
            </summary>
            <param name="conn">The database connection on which to run the metadataquery.</param>
            <param name="restrictions">The restrictions to filter the collection.</param>
            <returns>The Databases</returns>
        </member>
        <member name="M:Npgsql.NpgsqlSchema.GetTables(Npgsql.NpgsqlConnection,System.String[])">
            <summary>
            Returns the Tables that contains table and view names and the database and schema they come from.
            </summary>
            <param name="conn">The database connection on which to run the metadataquery.</param>
            <param name="restrictions">The restrictions to filter the collection.</param>
            <returns>The Tables</returns>
        </member>
        <member name="M:Npgsql.NpgsqlSchema.GetColumns(Npgsql.NpgsqlConnection,System.String[])">
            <summary>
            Returns the Columns that contains information about columns in tables.
            </summary>
            <param name="conn">The database connection on which to run the metadataquery.</param>
            <param name="restrictions">The restrictions to filter the collection.</param>
            <returns>The Columns.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlSchema.GetViews(Npgsql.NpgsqlConnection,System.String[])">
            <summary>
            Returns the Views that contains view names and the database and schema they come from.
            </summary>
            <param name="conn">The database connection on which to run the metadataquery.</param>
            <param name="restrictions">The restrictions to filter the collection.</param>
            <returns>The Views</returns>
        </member>
        <member name="M:Npgsql.NpgsqlSchema.GetUsers(Npgsql.NpgsqlConnection,System.String[])">
            <summary>
            Returns the Users containing user names and the sysid of those users.
            </summary>
            <param name="conn">The database connection on which to run the metadataquery.</param>
            <param name="restrictions">The restrictions to filter the collection.</param>
            <returns>The Users.</returns>
        </member>
        <member name="F:Npgsql.NpgsqlSchema.ReservedKeywords">
            <summary>
            List of keywords taken from PostgreSQL 9.0 reserved words documentation.
            </summary>
        </member>
        <member name="T:Npgsql.NpgsqlSequentialDataReader">
            <summary>
            A sequential reader, which does not buffer rows in memory, and requires columns to be
            read in-order only. Returned when <see cref="F:System.Data.CommandBehavior.SequentialAccess"/> is passed
            to <see cref="M:Npgsql.NpgsqlCommand.ExecuteReader"/>.
            </summary>
            <remarks>
            This reader is suitable in scenarios where a single row is very large, and holding
            it in memory is undesirable.
            </remarks>
        </member>
        <member name="F:Npgsql.NpgsqlSequentialDataReader._numColumns">
            <summary>
            The number of columns in the current row
            </summary>
        </member>
        <member name="F:Npgsql.NpgsqlSequentialDataReader._column">
            <summary>
            The index of the column that we're on, i.e. that has already been parsed, is
            is memory and can be retrieved. Initialized to -1
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlSequentialDataReader.GetValue(System.Int32)">
            <summary>
            Gets the value of the specified column as an instance of <see cref="T:System.Object"/>.
            </summary>
            <param name="column">The zero-based column ordinal.</param>
            <returns>The value of the specified column.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlSequentialDataReader.GetProviderSpecificValue(System.Int32)">
            <summary>
            Gets the value of the specified column as an instance of <see cref="T:System.Object"/>.
            </summary>
            <param name="column">The zero-based column ordinal.</param>
            <returns>The value of the specified column.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlSequentialDataReader.SeekToColumn(System.Int32,System.Boolean)">
            <summary>
            Seeks to the given column. The 4-byte length is read and stored in <see cref="F:Npgsql.NpgsqlDataReader.ColumnLen"/>.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlSequentialDataReader.IsDBNull(System.Int32)">
            <summary>
            Gets a value that indicates whether the column contains nonexistent or missing values.
            </summary>
            <param name="ordinal">The zero-based column ordinal.</param>
            <returns><b>true</b> if the specified column is equivalent to <see cref="T:System.DBNull"/>; otherwise <b>false</b>.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlSequentialDataReader.IsDBNullAsync(System.Int32,System.Threading.CancellationToken)">
            <summary>
            An asynchronous version of <see cref="M:Npgsql.NpgsqlSequentialDataReader.IsDBNull(System.Int32)"/>, which gets a value that indicates whether the column contains non-existent or missing values.
            The <paramref name="cancellationToken"/> parameter is currently ignored.
            </summary>
            <param name="ordinal">The zero-based column to be retrieved.</param>
            <param name="cancellationToken">Currently ignored.</param>
            <returns><b>true</b> if the specified column value is equivalent to <see cref="T:System.DBNull"/> otherwise <b>false</b>.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlSequentialDataReader.GetBytes(System.Int32,System.Int64,System.Byte[],System.Int32,System.Int32)">
            <summary>
            Reads a stream of bytes from the specified column, starting at location indicated by dataOffset, into the buffer, starting at the location indicated by bufferOffset.
            </summary>
            <param name="ordinal">The zero-based column ordinal.</param>
            <param name="dataOffset">The index within the row from which to begin the read operation.</param>
            <param name="buffer">The buffer into which to copy the data.</param>
            <param name="bufferOffset">The index with the buffer to which the data will be copied.</param>
            <param name="length">The maximum number of characters to read.</param>
            <returns>The actual number of bytes read.</returns>
        </member>
        <member name="T:Npgsql.NpgsqlStatement">
             <summary>
             Represents a single SQL statement within Npgsql.
            
             Instances aren't constructed directly; users should construct an <see cref="T:Npgsql.NpgsqlCommand"/>
             object and populate its <see cref="P:Npgsql.NpgsqlCommand.CommandText"/> property as in standard ADO.NET.
             Npgsql will analyze that property and constructed instances of <see cref="T:Npgsql.NpgsqlStatement"/>
             internally.
            
             Users can retrieve instances from <see cref="P:Npgsql.NpgsqlDataReader.Statements"/>
             and access information about statement execution (e.g. affected rows).
             </summary>
        </member>
        <member name="P:Npgsql.NpgsqlStatement.SQL">
            <summary>
            The SQL text of the statement.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlStatement.StatementType">
            <summary>
            Specifies the type of query, e.g. SELECT.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlStatement.Rows">
            <summary>
            The number of rows affected or retrieved.
            </summary>
            <remarks>
            See the command tag in the CommandComplete message,
            http://www.postgresql.org/docs/current/static/protocol-message-formats.html
            </remarks>
        </member>
        <member name="P:Npgsql.NpgsqlStatement.OID">
            <summary>
            For an INSERT, the object ID of the inserted row if <see cref="P:Npgsql.NpgsqlStatement.Rows"/> is 1 and
            the target table has OIDs; otherwise 0.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlStatement.InputParameters">
            <summary>
            The input parameters sent with this statement.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlStatement.Description">
            <summary>
            The RowDescription message for this query. If null, the query does not return rows (e.g. INSERT)
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlStatement.PreparedStatement">
            <summary>
            If this statement has been automatically prepared, references the <see cref="P:Npgsql.NpgsqlStatement.PreparedStatement"/>.
            Null otherwise.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlStatement.StatementName">
            <summary>
            Holds the server-side (prepared) statement name. Empty string for non-prepared statements.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlStatement.IsPrepared">
            <summary>
            Whether this statement has already been prepared (including automatic preparation).
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlStatement.ToString">
            <summary>
            Returns the SQL text of the statement.
            </summary>
        </member>
        <member name="T:Npgsql.NpgsqlTransaction">
            <summary>
            Represents a transaction to be made in a PostgreSQL database. This class cannot be inherited.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlTransaction.Connection">
            <summary>
            Specifies the <see cref="T:Npgsql.NpgsqlConnection"/> object associated with the transaction.
            </summary>
            <value>The <see cref="T:Npgsql.NpgsqlConnection"/> object associated with the transaction.</value>
        </member>
        <member name="P:Npgsql.NpgsqlTransaction.IsCompleted">
            <summary>
            Specifies the completion state of the transaction.
            </summary>
            <value>The completion state of the transaction.</value>
        </member>
        <member name="P:Npgsql.NpgsqlTransaction.DbConnection">
            <summary>
            Specifies the <see cref="T:Npgsql.NpgsqlConnection"/> object associated with the transaction.
            </summary>
            <value>The <see cref="T:Npgsql.NpgsqlConnection"/> object associated with the transaction.</value>
        </member>
        <member name="P:Npgsql.NpgsqlTransaction.IsolationLevel">
            <summary>
            Specifies the <see cref="T:System.Data.IsolationLevel">IsolationLevel</see> for this transaction.
            </summary>
            <value>The <see cref="T:System.Data.IsolationLevel">IsolationLevel</see> for this transaction.
            The default is <b>ReadCommitted</b>.</value>
        </member>
        <member name="M:Npgsql.NpgsqlTransaction.Commit">
            <summary>
            Commits the database transaction.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlTransaction.CommitAsync(System.Threading.CancellationToken)">
            <summary>
            Commits the database transaction.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlTransaction.CommitAsync">
            <summary>
            Commits the database transaction.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlTransaction.Rollback">
            <summary>
            Rolls back a transaction from a pending state.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlTransaction.RollbackAsync(System.Threading.CancellationToken)">
            <summary>
            Rolls back a transaction from a pending state.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlTransaction.RollbackAsync">
            <summary>
            Rolls back a transaction from a pending state.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlTransaction.Save(System.String)">
            <summary>
            Creates a transaction save point.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlTransaction.Rollback(System.String)">
            <summary>
            Rolls back a transaction from a pending savepoint state.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlTransaction.Release(System.String)">
            <summary>
            Rolls back a transaction from a pending savepoint state.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlTransaction.Dispose(System.Boolean)">
            <summary>
            Disposes the transaction, rolling it back if it is still pending.
            </summary>
        </member>
        <member name="T:Npgsql.NpgsqlWriteBuffer">
            <summary>
            A buffer used by Npgsql to write data to the socket efficiently.
            Provides methods which encode different values types and tracks the current position.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlWriteBuffer.Size">
            <summary>
            The total byte length of the buffer.
            </summary>
        </member>
        <member name="F:Npgsql.NpgsqlWriteBuffer.MinimumSize">
            <summary>
            The minimum buffer size possible.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlWriteBuffer.GetContents">
            <summary>
            Returns all contents currently written to the buffer (but not flushed).
            Useful for pregenerating messages.
            </summary>
        </member>
        <member name="T:Npgsql.PgPassFile">
            <summary>
            Represents a .pgpass file, which contains passwords for noninteractive connections
            </summary>
        </member>
        <member name="P:Npgsql.PgPassFile.FileName">
            <summary>
            File name being parsed for credentials
            </summary>
        </member>
        <member name="M:Npgsql.PgPassFile.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Npgsql.PgPassFile"/> class
            </summary>
            <param name="fileName"></param>
        </member>
        <member name="P:Npgsql.PgPassFile.Entries">
            <summary>
            Parses file content and gets all credentials from the file
            </summary>
            <returns><see cref="T:System.Collections.Generic.IEnumerable`1"/> corresponding to all lines in the .pgpass file</returns>
        </member>
        <member name="M:Npgsql.PgPassFile.GetFirstMatchingEntry(System.String,System.Nullable{System.Int32},System.String,System.String)">
            <summary>
            Searches queries loaded from .PGPASS file to find first entry matching the provided parameters.
            </summary>
            <param name="host">Hostname to query. Use null to match any.</param>
            <param name="port">Port to query. Use null to match any.</param>
            <param name="database">Database to query. Use null to match any.</param>
            <param name="username">User name to query. Use null to match any.</param>
            <returns>Matching <see cref="T:Npgsql.PgPassFile.Entry"/> if match was found. Otherwise, returns null.</returns>
        </member>
        <member name="M:Npgsql.PgPassFile.GetSystemPgPassFilePath">
            <summary>
            Retrieves the full system path to the pgpass file. Does not check whether the
            file actually exist.
            </summary>
            <remarks>
            See https://www.postgresql.org/docs/current/static/libpq-pgpass.html
            </remarks>
            <returns>Path to the pgpass file</returns>
        </member>
        <member name="T:Npgsql.PgPassFile.Entry">
            <summary>
            Represents a hostname, port, database, username, and password combination that has been retrieved from a .pgpass file
            </summary>
        </member>
        <member name="P:Npgsql.PgPassFile.Entry.Host">
            <summary>
            Hostname parsed from the .pgpass file
            </summary>
        </member>
        <member name="P:Npgsql.PgPassFile.Entry.Port">
            <summary>
            Port parsed from the .pgpass file
            </summary>
        </member>
        <member name="P:Npgsql.PgPassFile.Entry.Database">
            <summary>
            Database parsed from the .pgpass file
            </summary>
        </member>
        <member name="P:Npgsql.PgPassFile.Entry.Username">
            <summary>
            User name parsed from the .pgpass file
            </summary>
        </member>
        <member name="P:Npgsql.PgPassFile.Entry.Password">
            <summary>
            Password parsed from the .pgpass file
            </summary>
        </member>
        <member name="M:Npgsql.PgPassFile.Entry.#ctor(System.String,System.Nullable{System.Int32},System.String,System.String,System.String)">
            <summary>
            This class represents an entry from the .pgpass file
            </summary>
            <param name="host">Hostname parsed from the .pgpass file</param>
            <param name="port">Port parsed from the .pgpass file</param>
            <param name="database">Database parsed from the .pgpass file</param>
            <param name="username">User name parsed from the .pgpass file</param>
            <param name="password">Password parsed from the .pgpass file</param>
        </member>
        <member name="M:Npgsql.PgPassFile.Entry.Parse(System.String)">
            <summary>
            Creates new <see cref="T:Npgsql.PgPassFile.Entry"/> based on string in the format hostname:port:database:username:password. The : and \ characters should be escaped with a \.
            </summary>
            <param name="serializedEntry">string for the entry from the pgpass file</param>
            <returns>New instance of <see cref="T:Npgsql.PgPassFile.Entry"/> for the string</returns>
            <exception cref="T:System.FormatException">Entry is not formatted as hostname:port:database:username:password or non-wildcard port is not a number</exception>
        </member>
        <member name="M:Npgsql.PgPassFile.Entry.IsMatch(System.String,System.Nullable{System.Int32},System.String,System.String)">
            <summary>
            Checks whether this <see cref="T:Npgsql.PgPassFile.Entry"/> matches the parameters supplied
            </summary>
            <param name="host">Hostname to check against this entry</param>
            <param name="port">Port to check against this entry</param>
            <param name="database">Database to check against this entry</param>
            <param name="username">Username to check against this entry</param>
            <returns>True if the entry is a match. False otherwise.</returns>
        </member>
        <member name="M:Npgsql.PgPassFile.Entry.AreValuesMatched(System.String,System.String)">
            <summary>
            Checks if 2 strings are a match for a <see cref="T:Npgsql.PgPassFile.Entry"/> considering that either value can be a wildcard (*)
            </summary>
            <param name="query">Value being searched</param>
            <param name="actual">Value from the PGPASS entry</param>
            <returns>True if the values are a match. False otherwise.</returns>
        </member>
        <member name="T:Npgsql.NpgsqlTimeout">
            <summary>
            Represents a timeout that will expire at some point.
            </summary>
        </member>
        <member name="T:Npgsql.PoolManager">
            <summary>
            Provides lookup for a pool based on a connection string.
            </summary>
            <remarks>
            <see cref="M:Npgsql.PoolManager.TryGetValue(System.String,Npgsql.ConnectorPool@)"/> is lock-free, to avoid contention, but the same isn't
            true of <see cref="M:Npgsql.PoolManager.GetOrAdd(System.String,Npgsql.ConnectorPool)"/>, which acquires a lock. The calling code always tries
            <see cref="M:Npgsql.PoolManager.TryGetValue(System.String,Npgsql.ConnectorPool@)"/> before trying to <see cref="M:Npgsql.PoolManager.GetOrAdd(System.String,Npgsql.ConnectorPool)"/>.
            </remarks>
        </member>
        <member name="M:Npgsql.PoolManager.Reset">
            <summary>
            Resets the pool manager to its initial state, for test purposes only.
            Assumes that no other threads are accessing the pool.
            </summary>
        </member>
        <member name="T:Npgsql.PostgresDatabaseInfoFactory">
            <summary>
            The default implementation of <see cref="T:Npgsql.INpgsqlDatabaseInfoFactory"/>, for standard PostgreSQL databases..
            </summary>
        </member>
        <member name="M:Npgsql.PostgresDatabaseInfoFactory.Load(Npgsql.NpgsqlConnection,Npgsql.NpgsqlTimeout,System.Boolean)">
            <inheritdoc />
        </member>
        <member name="T:Npgsql.PostgresDatabaseInfo">
            <summary>
            The default implementation of NpgsqlDatabase, for standard PostgreSQL databases.
            </summary>
        </member>
        <member name="F:Npgsql.PostgresDatabaseInfo.Log">
            <summary>
            The Npgsql logger instance.
            </summary>
        </member>
        <member name="F:Npgsql.PostgresDatabaseInfo._types">
            <summary>
            The PostgreSQL types detected in the database.
            </summary>
        </member>
        <member name="M:Npgsql.PostgresDatabaseInfo.GetTypes">
            <inheritdoc />
        </member>
        <member name="P:Npgsql.PostgresDatabaseInfo.IsRedshift">
            <summary>
            True if the backend is Amazon Redshift; otherwise, false.
            </summary>
        </member>
        <member name="P:Npgsql.PostgresDatabaseInfo.SupportsUnlisten">
            <inheritdoc />
        </member>
        <member name="P:Npgsql.PostgresDatabaseInfo.HasEnumSortOrder">
            <summary>
            True if the 'pg_enum' table includes the 'enumsortorder' column; otherwise, false.
            </summary>
        </member>
        <member name="M:Npgsql.PostgresDatabaseInfo.LoadPostgresInfo(Npgsql.NpgsqlConnection,Npgsql.NpgsqlTimeout,System.Boolean)">
            <summary>
            Loads database information from the PostgreSQL database specified by <paramref name="conn"/>.
            </summary>
            <param name="conn">The database connection.</param>
            <param name="timeout">The timeout while loading types from the backend.</param>
            <param name="async">True to load types asynchronously.</param>
            <returns>
            A task representing the asynchronous operation.
            </returns>
        </member>
        <member name="M:Npgsql.PostgresDatabaseInfo.GenerateTypesQuery(System.Boolean,System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            Generates a raw SQL query string to select type information.
            </summary>
            <param name="withRange">True to load range types.</param>
            <param name="withEnum">True to load enum types.</param>
            <param name="withEnumSortOrder"></param>
            <param name="loadTableComposites">True to load table composites.</param>
            <returns>
            A raw SQL query string that selects type information.
            </returns>
            <remarks>
            Select all types (base, array which is also base, enum, range, composite).
            Note that arrays are distinguished from primitive types through them having typreceive=array_recv.
            Order by primitives first, container later.
            For arrays and ranges, join in the element OID and type (to filter out arrays of unhandled
            types).
            </remarks>
        </member>
        <member name="M:Npgsql.PostgresDatabaseInfo.LoadBackendTypes(Npgsql.NpgsqlConnection,Npgsql.NpgsqlTimeout,System.Boolean)">
            <summary>
            Loads type information from the backend specified by <paramref name="conn"/>.
            </summary>
            <param name="conn">The database connection.</param>
            <param name="timeout">The timeout while loading types from the backend.</param>
            <param name="async">True to load types asynchronously.</param>
            <returns>
            A collection of types loaded from the backend.
            </returns>
            <exception cref="T:System.TimeoutException" />
            <exception cref="T:System.ArgumentOutOfRangeException">Unknown typtype for type '{internalName}' in pg_type: {typeChar}.</exception>
        </member>
        <member name="M:Npgsql.PostgresDatabaseInfo.LoadCompositeFields(System.Data.Common.DbDataReader,System.Collections.Generic.Dictionary{System.UInt32,Npgsql.PostgresTypes.PostgresType})">
            <summary>
            Loads composite fields for the composite type specified by the OID.
            </summary>
            <param name="reader">The reader from which to read composite fields.</param>
            <param name="byOID">The OID of the composite type for which fields are read.</param>
        </member>
        <member name="M:Npgsql.PostgresDatabaseInfo.LoadEnumLabels(System.Data.Common.DbDataReader,System.Collections.Generic.Dictionary{System.UInt32,Npgsql.PostgresTypes.PostgresType})">
            <summary>
            Loads enum labels for the enum type specified by the OID.
            </summary>
            <param name="reader">The reader from which to read enum labels.</param>
            <param name="byOID">The OID of the enum type for which labels are read.</param>
        </member>
        <member name="T:Npgsql.PostgresException">
            <summary>
            The exception that is thrown when the PostgreSQL backend reports errors (e.g. query
            SQL issues, constraint violations).
            </summary>
            <remarks>
            This exception only corresponds to a PostgreSQL-delivered error.
            Other errors (e.g. network issues) will be raised via <see cref="T:Npgsql.NpgsqlException"/>,
            and purely Npgsql-related issues which aren't related to the server will be raised
            via the standard CLR exceptions (e.g. ArgumentException).
            
            See http://www.postgresql.org/docs/current/static/errcodes-appendix.html,
            http://www.postgresql.org/docs/current/static/protocol-error-fields.html
            </remarks>
        </member>
        <member name="P:Npgsql.PostgresException.Severity">
            <summary>
            Severity of the error or notice.
            Always present.
            </summary>
        </member>
        <member name="P:Npgsql.PostgresException.SqlState">
            <summary>
            The SQLSTATE code for the error.
            </summary>
            <remarks>
            Always present.
            See http://www.postgresql.org/docs/current/static/errcodes-appendix.html
            </remarks>
        </member>
        <member name="P:Npgsql.PostgresException.Code">
            <summary>
            The SQLSTATE code for the error.
            </summary>
            <remarks>
            Always present.
            See http://www.postgresql.org/docs/current/static/errcodes-appendix.html
            </remarks>
        </member>
        <member name="P:Npgsql.PostgresException.MessageText">
            <summary>
            The primary human-readable error message. This should be accurate but terse.
            </summary>
            <remarks>
            Always present.
            </remarks>
        </member>
        <member name="P:Npgsql.PostgresException.Detail">
            <summary>
            An optional secondary error message carrying more detail about the problem.
            May run to multiple lines.
            </summary>
        </member>
        <member name="P:Npgsql.PostgresException.Hint">
            <summary>
            An optional suggestion what to do about the problem.
            This is intended to differ from Detail in that it offers advice (potentially inappropriate) rather than hard facts.
            May run to multiple lines.
            </summary>
        </member>
        <member name="P:Npgsql.PostgresException.Position">
            <summary>
            The field value is a decimal ASCII integer, indicating an error cursor position as an index into the original query string.
            The first character has index 1, and positions are measured in characters not bytes.
            0 means not provided.
            </summary>
        </member>
        <member name="P:Npgsql.PostgresException.InternalPosition">
            <summary>
            This is defined the same as the <see cref="P:Npgsql.PostgresException.Position"/> field, but it is used when the cursor position refers to an internally generated command rather than the one submitted by the client.
            The <see cref="P:Npgsql.PostgresException.InternalQuery" /> field will always appear when this field appears.
            0 means not provided.
            </summary>
        </member>
        <member name="P:Npgsql.PostgresException.InternalQuery">
            <summary>
            The text of a failed internally-generated command.
            This could be, for example, a SQL query issued by a PL/pgSQL function.
            </summary>
        </member>
        <member name="P:Npgsql.PostgresException.Where">
            <summary>
            An indication of the context in which the error occurred.
            Presently this includes a call stack traceback of active PL functions.
            The trace is one entry per line, most recent first.
            </summary>
        </member>
        <member name="P:Npgsql.PostgresException.SchemaName">
            <summary>
            If the error was associated with a specific database object, the name of the schema containing that object, if any.
            </summary>
            <remarks>PostgreSQL 9.3 and up.</remarks>
        </member>
        <member name="P:Npgsql.PostgresException.TableName">
            <summary>
            Table name: if the error was associated with a specific table, the name of the table.
            (Refer to the schema name field for the name of the table's schema.)
            </summary>
            <remarks>PostgreSQL 9.3 and up.</remarks>
        </member>
        <member name="P:Npgsql.PostgresException.ColumnName">
            <summary>
            If the error was associated with a specific table column, the name of the column.
            (Refer to the schema and table name fields to identify the table.)
            </summary>
            <remarks>PostgreSQL 9.3 and up.</remarks>
        </member>
        <member name="P:Npgsql.PostgresException.DataTypeName">
            <summary>
            If the error was associated with a specific data type, the name of the data type.
            (Refer to the schema name field for the name of the data type's schema.)
            </summary>
            <remarks>PostgreSQL 9.3 and up.</remarks>
        </member>
        <member name="P:Npgsql.PostgresException.ConstraintName">
            <summary>
            If the error was associated with a specific constraint, the name of the constraint.
            Refer to fields listed above for the associated table or domain.
            (For this purpose, indexes are treated as constraints, even if they weren't created with constraint syntax.)
            </summary>
            <remarks>PostgreSQL 9.3 and up.</remarks>
        </member>
        <member name="P:Npgsql.PostgresException.File">
            <summary>
            The file name of the source-code location where the error was reported.
            </summary>
            <remarks>PostgreSQL 9.3 and up.</remarks>
        </member>
        <member name="P:Npgsql.PostgresException.Line">
            <summary>
            The line number of the source-code location where the error was reported.
            </summary>
        </member>
        <member name="P:Npgsql.PostgresException.Routine">
            <summary>
            The name of the source-code routine reporting the error.
            </summary>
        </member>
        <member name="M:Npgsql.PostgresException.#ctor">
            <summary>
            Creates a new instance.
            </summary>
        </member>
        <member name="P:Npgsql.PostgresException.Message">
            <summary>
            Gets a the PostgreSQL error message and code.
            </summary>
        </member>
        <member name="P:Npgsql.PostgresException.IsTransient">
            <summary>
            Specifies whether the exception is considered transient, that is, whether retrying to operation could
            succeed (e.g. a network error). Check <see cref="P:Npgsql.PostgresException.SqlState"/>.
            </summary>
        </member>
        <member name="P:Npgsql.PostgresException.Statement">
            <summary>
            Returns the statement which triggered this exception.
            </summary>
        </member>
        <member name="P:Npgsql.PostgresException.Data">
            <summary>
            Gets a collection of key/value pairs that provide additional PostgreSQL fields about the exception.
            </summary>
        </member>
        <member name="M:Npgsql.PostgresException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Populates a <see cref="T:System.Runtime.Serialization.SerializationInfo"/> with the data needed to serialize the target object.
            </summary>
            <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo"/> to populate with data.</param>
            <param name="context">The destination (see <see cref="T:System.Runtime.Serialization.StreamingContext"/>) for this serialization.</param>
        </member>
        <member name="T:Npgsql.PostgresNotice">
            <summary>
            PostgreSQL notices are non-critical messages generated by PostgreSQL, either as a result of a user query
            (e.g. as a warning or informational notice), or due to outside activity (e.g. if the database administrator
            initiates a "fast" database shutdown).
            </summary>
            <remarks>
            http://www.postgresql.org/docs/current/static/protocol-flow.html#PROTOCOL-ASYNC
            </remarks>
        </member>
        <member name="P:Npgsql.PostgresNotice.Severity">
            <summary>
            Severity of the error or notice.
            Always present.
            </summary>
        </member>
        <member name="P:Npgsql.PostgresNotice.SqlState">
            <summary>
            The SQLSTATE code for the error.
            </summary>
            <remarks>
            Always present.
            See http://www.postgresql.org/docs/current/static/errcodes-appendix.html
            </remarks>
        </member>
        <member name="P:Npgsql.PostgresNotice.Code">
            <summary>
            The SQLSTATE code for the error.
            </summary>
            <remarks>
            Always present.
            See http://www.postgresql.org/docs/current/static/errcodes-appendix.html
            </remarks>
        </member>
        <member name="P:Npgsql.PostgresNotice.MessageText">
            <summary>
            The primary human-readable error message. This should be accurate but terse.
            </summary>
            <remarks>
            Always present.
            </remarks>
        </member>
        <member name="P:Npgsql.PostgresNotice.Detail">
            <summary>
            An optional secondary error message carrying more detail about the problem.
            May run to multiple lines.
            </summary>
        </member>
        <member name="P:Npgsql.PostgresNotice.Hint">
            <summary>
            An optional suggestion what to do about the problem.
            This is intended to differ from Detail in that it offers advice (potentially inappropriate) rather than hard facts.
            May run to multiple lines.
            </summary>
        </member>
        <member name="P:Npgsql.PostgresNotice.Position">
            <summary>
            The field value is a decimal ASCII integer, indicating an error cursor position as an index into the original query string.
            The first character has index 1, and positions are measured in characters not bytes.
            0 means not provided.
            </summary>
        </member>
        <member name="P:Npgsql.PostgresNotice.InternalPosition">
            <summary>
            This is defined the same as the <see cref="P:Npgsql.PostgresNotice.Position"/> field, but it is used when the cursor position refers to an internally generated command rather than the one submitted by the client.
            The <see cref="P:Npgsql.PostgresNotice.InternalQuery" /> field will always appear when this field appears.
            0 means not provided.
            </summary>
        </member>
        <member name="P:Npgsql.PostgresNotice.InternalQuery">
            <summary>
            The text of a failed internally-generated command.
            This could be, for example, a SQL query issued by a PL/pgSQL function.
            </summary>
        </member>
        <member name="P:Npgsql.PostgresNotice.Where">
            <summary>
            An indication of the context in which the error occurred.
            Presently this includes a call stack traceback of active PL functions.
            The trace is one entry per line, most recent first.
            </summary>
        </member>
        <member name="P:Npgsql.PostgresNotice.SchemaName">
            <summary>
            If the error was associated with a specific database object, the name of the schema containing that object, if any.
            </summary>
            <remarks>PostgreSQL 9.3 and up.</remarks>
        </member>
        <member name="P:Npgsql.PostgresNotice.TableName">
            <summary>
            Table name: if the error was associated with a specific table, the name of the table.
            (Refer to the schema name field for the name of the table's schema.)
            </summary>
            <remarks>PostgreSQL 9.3 and up.</remarks>
        </member>
        <member name="P:Npgsql.PostgresNotice.ColumnName">
            <summary>
            If the error was associated with a specific table column, the name of the column.
            (Refer to the schema and table name fields to identify the table.)
            </summary>
            <remarks>PostgreSQL 9.3 and up.</remarks>
        </member>
        <member name="P:Npgsql.PostgresNotice.DataTypeName">
            <summary>
            If the error was associated with a specific data type, the name of the data type.
            (Refer to the schema name field for the name of the data type's schema.)
            </summary>
            <remarks>PostgreSQL 9.3 and up.</remarks>
        </member>
        <member name="P:Npgsql.PostgresNotice.ConstraintName">
            <summary>
            If the error was associated with a specific constraint, the name of the constraint.
            Refer to fields listed above for the associated table or domain.
            (For this purpose, indexes are treated as constraints, even if they weren't created with constraint syntax.)
            </summary>
            <remarks>PostgreSQL 9.3 and up.</remarks>
        </member>
        <member name="P:Npgsql.PostgresNotice.File">
            <summary>
            The file name of the source-code location where the error was reported.
            </summary>
            <remarks>PostgreSQL 9.3 and up.</remarks>
        </member>
        <member name="P:Npgsql.PostgresNotice.Line">
            <summary>
            The line number of the source-code location where the error was reported.
            </summary>
        </member>
        <member name="P:Npgsql.PostgresNotice.Routine">
            <summary>
            The name of the source-code routine reporting the error.
            </summary>
        </member>
        <member name="M:Npgsql.PostgresNotice.#ctor">
            <summary>
            Creates a new instance.
            </summary>
        </member>
        <member name="T:Npgsql.NpgsqlNoticeEventArgs">
            <summary>
            Provides data for a PostgreSQL notice event.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlNoticeEventArgs.Notice">
            <summary>
            The Notice that was sent from the database.
            </summary>
        </member>
        <member name="T:Npgsql.PostgresTypes.PostgresArrayType">
            <summary>
            Represents a PostgreSQL array data type, which can hold several multiple values in a single column.
            </summary>
            <remarks>
            See https://www.postgresql.org/docs/current/static/arrays.html.
            </remarks>
        </member>
        <member name="P:Npgsql.PostgresTypes.PostgresArrayType.Element">
            <summary>
            The PostgreSQL data type of the element contained within this array.
            </summary>
        </member>
        <member name="M:Npgsql.PostgresTypes.PostgresArrayType.#ctor(System.String,System.String,System.UInt32,Npgsql.PostgresTypes.PostgresType)">
            <summary>
            Constructs a representation of a PostgreSQL array data type.
            </summary>
        </member>
        <member name="M:Npgsql.PostgresTypes.PostgresArrayType.GetPartialNameWithFacets(System.Int32)">
            <inheritdoc/>
        </member>
        <member name="T:Npgsql.PostgresTypes.PostgresBaseType">
            <summary>
            Represents a PostgreSQL base data type, which is a simple scalar value.
            </summary>
        </member>
        <member name="M:Npgsql.PostgresTypes.PostgresBaseType.#ctor(System.String,System.String,System.UInt32)">
            <inheritdoc/>
        </member>
        <member name="M:Npgsql.PostgresTypes.PostgresBaseType.GetPartialNameWithFacets(System.Int32)">
            <inheritdoc/>
        </member>
        <member name="T:Npgsql.PostgresTypes.PostgresCompositeType">
            <summary>
            Represents a PostgreSQL composite data type, which can hold multiple fields of varying types in a single column.
            </summary>
            <remarks>
            See https://www.postgresql.org/docs/current/static/rowtypes.html.
            </remarks>
        </member>
        <member name="P:Npgsql.PostgresTypes.PostgresCompositeType.Fields">
            <summary>
            Holds the name and types for all fields.
            </summary>
        </member>
        <member name="M:Npgsql.PostgresTypes.PostgresCompositeType.#ctor(System.String,System.String,System.UInt32)">
            <summary>
            Constructs a representation of a PostgreSQL array data type.
            </summary>
        </member>
        <member name="T:Npgsql.PostgresTypes.PostgresCompositeType.Field">
            <summary>
            Represents a field in a PostgreSQL composite data type.
            </summary>
        </member>
        <member name="P:Npgsql.PostgresTypes.PostgresCompositeType.Field.Name">
            <summary>
            The name of the composite field.
            </summary>
        </member>
        <member name="P:Npgsql.PostgresTypes.PostgresCompositeType.Field.Type">
            <summary>
            The type of the composite field.
            </summary>
        </member>
        <member name="M:Npgsql.PostgresTypes.PostgresCompositeType.Field.ToString">
            <inheritdoc />
        </member>
        <member name="T:Npgsql.PostgresTypes.PostgresDomainType">
             <summary>
             Represents a PostgreSQL domain type.
             </summary>
             <remarks>
             See https://www.postgresql.org/docs/current/static/sql-createdomain.html.
            
             When PostgreSQL returns a RowDescription for a domain type, the type OID is the base type's
             (so fetching a domain type over text returns a RowDescription for text).
             However, when a composite type is returned, the type OID there is that of the domain,
             so we provide "clean" support for domain types.
             </remarks>
        </member>
        <member name="P:Npgsql.PostgresTypes.PostgresDomainType.BaseType">
            <summary>
            The PostgreSQL data type of the base type, i.e. the type this domain is based on.
            </summary>
        </member>
        <member name="M:Npgsql.PostgresTypes.PostgresDomainType.#ctor(System.String,System.String,System.UInt32,Npgsql.PostgresTypes.PostgresType)">
            <summary>
            Constructs a representation of a PostgreSQL domain data type.
            </summary>
        </member>
        <member name="T:Npgsql.PostgresTypes.PostgresEnumType">
            <summary>
            Represents a PostgreSQL enum data type.
            </summary>
            <remarks>
            See https://www.postgresql.org/docs/current/static/datatype-enum.html.
            </remarks>
        </member>
        <member name="P:Npgsql.PostgresTypes.PostgresEnumType.Labels">
            <summary>
            The enum's fields.
            </summary>
        </member>
        <member name="M:Npgsql.PostgresTypes.PostgresEnumType.#ctor(System.String,System.String,System.UInt32)">
            <summary>
            Constructs a representation of a PostgreSQL enum data type.
            </summary>
        </member>
        <member name="T:Npgsql.PostgresTypes.PostgresRangeType">
            <summary>
            Represents a PostgreSQL range data type.
            </summary>
            <remarks>
            See https://www.postgresql.org/docs/current/static/rangetypes.html.
            </remarks>
        </member>
        <member name="P:Npgsql.PostgresTypes.PostgresRangeType.Subtype">
            <summary>
            The PostgreSQL data type of the subtype of this range.
            </summary>
        </member>
        <member name="M:Npgsql.PostgresTypes.PostgresRangeType.#ctor(System.String,System.String,System.UInt32,Npgsql.PostgresTypes.PostgresType)">
            <summary>
            Constructs a representation of a PostgreSQL range data type.
            </summary>
        </member>
        <member name="T:Npgsql.PostgresTypes.PostgresType">
            <summary>
            Represents a PostgreSQL data type, such as int4 or text, as discovered from pg_type.
            This class is abstract, see derived classes for concrete types of PostgreSQL types.
            </summary>
            <remarks>
            Instances of this class are shared between connections to the same databases.
            For more info about what this class and its subclasses represent, see
            https://www.postgresql.org/docs/current/static/catalog-pg-type.html.
            </remarks>
        </member>
        <member name="M:Npgsql.PostgresTypes.PostgresType.#ctor(System.String,System.String,System.UInt32)">
            <summary>
            Constructs a representation of a PostgreSQL data type.
            </summary>
            <param name="ns">The data type's namespace (or schema).</param>
            <param name="name">The data type's name.</param>
            <param name="oid">The data type's OID.</param>
        </member>
        <member name="M:Npgsql.PostgresTypes.PostgresType.#ctor(System.String,System.String,System.String,System.UInt32)">
            <summary>
            Constructs a representation of a PostgreSQL data type.
            </summary>
            <param name="ns">The data type's namespace (or schema).</param>
            <param name="name">The data type's name.</param>
            <param name="internalName">The data type's internal name (e.g. _int4 for integer[]).</param>
            <param name="oid">The data type's OID.</param>
        </member>
        <member name="P:Npgsql.PostgresTypes.PostgresType.OID">
            <summary>
            The data type's OID - a unique id identifying the data type in a given database (in pg_type).
            </summary>
        </member>
        <member name="P:Npgsql.PostgresTypes.PostgresType.Namespace">
            <summary>
            The data type's namespace (or schema).
            </summary>
        </member>
        <member name="P:Npgsql.PostgresTypes.PostgresType.Name">
            <summary>
            The data type's name.
            </summary>
            <remarks>
            Note that this is the standard, user-displayable type name (e.g. integer[]) rather than the internal
            PostgreSQL name as it is in pg_type (_int4). See <see cref="P:Npgsql.PostgresTypes.PostgresType.InternalName"/> for the latter.
            </remarks>
        </member>
        <member name="P:Npgsql.PostgresTypes.PostgresType.FullName">
            <summary>
            The full name of the backend type, including its namespace.
            </summary>
        </member>
        <member name="P:Npgsql.PostgresTypes.PostgresType.DisplayName">
            <summary>
            A display name for this backend type, including the namespace unless it is pg_catalog (the namespace
            for all built-in types).
            </summary>
        </member>
        <member name="P:Npgsql.PostgresTypes.PostgresType.InternalName">
            <summary>
            The data type's internal PostgreSQL name (e.g. integer[] not _int4).
            See <see cref="P:Npgsql.PostgresTypes.PostgresType.Name"/> for a more user-friendly name.
            </summary>
        </member>
        <member name="P:Npgsql.PostgresTypes.PostgresType.Array">
            <summary>
            If a PostgreSQL array type exists for this type, it will be referenced here.
            Otherwise null.
            </summary>
        </member>
        <member name="P:Npgsql.PostgresTypes.PostgresType.Range">
            <summary>
            If a PostgreSQL range type exists for this type, it will be referenced here.
            Otherwise null.
            </summary>
        </member>
        <member name="M:Npgsql.PostgresTypes.PostgresType.GetDisplayNameWithFacets(System.Int32)">
            <summary>
            Generates the type name including any facts (size, precision, scale), given the PostgreSQL type modifier.
            </summary>
        </member>
        <member name="M:Npgsql.PostgresTypes.PostgresType.ToString">
            <summary>
            Returns a string that represents the current object.
            </summary>
        </member>
        <member name="T:Npgsql.PostgresTypes.UnknownBackendType">
            <summary>
            Represents a PostgreSQL data type that isn't known to Npgsql and cannot be handled.
            </summary>
        </member>
        <member name="M:Npgsql.PostgresTypes.UnknownBackendType.#ctor">
            <summary>
            Constructs a the unknown backend type.
            </summary>
        </member>
        <member name="T:Npgsql.PreparedStatement">
            <summary>
            Internally represents a statement has been prepared, is in the process of being prepared, or is a
            candidate for preparation (i.e. awaiting further usages).
            </summary>
        </member>
        <member name="P:Npgsql.PreparedStatement.IsExplicit">
            <summary>
            If true, the user explicitly requested this statement be prepared. It does not get closed as part of
            the automatic preparation LRU mechanism.
            </summary>
        </member>
        <member name="F:Npgsql.PreparedStatement.StatementBeingReplaced">
            <summary>
            If this statement is about to be prepared, but replaces a previous statement which needs to be closed,
            this holds the name of the previous statement. Otherwise null.
            </summary>
        </member>
        <member name="P:Npgsql.PreparedStatement.HandlerParamTypes">
            <summary>
            Contains the handler types for a prepared statement's parameters, for overloaded cases (same SQL, different param types)
            Only populated after the statement has been prepared (i.e. null for candidates).
            </summary>
        </member>
        <member name="T:Npgsql.PreparedState">
            <summary>
            The state of a <see cref="T:Npgsql.PreparedStatement"/>.
            </summary>
        </member>
        <member name="F:Npgsql.PreparedState.NotPrepared">
            <summary>
            The statement hasn't been prepared yet, nor is it in the process of being prepared.
            This is the value for autoprepare candidates which haven't been prepared yet, and is also
            a temporary state during preparation.
            </summary>
        </member>
        <member name="F:Npgsql.PreparedState.ToBePrepared">
            <summary>
            The statement has been selected for preparation, but the preparation hasn't started yet.
            This is a temporary state that only occurs during preparation, and indicates that no
            no protocol message (Parse) has been sent yet.
            </summary>
        </member>
        <member name="F:Npgsql.PreparedState.BeingPrepared">
            <summary>
            The statement is in the process of being prepared. This is a temporary state that only occurs during
            preparation, and indicates that a Parse protocol message for the statement has already been written
            to the write buffer.
            </summary>
        </member>
        <member name="F:Npgsql.PreparedState.Prepared">
            <summary>
            The statement has been fully prepared and can be executed.
            </summary>
        </member>
        <member name="F:Npgsql.PreparedState.BeingUnprepared">
            <summary>
            The statement is in the process of being unprepared. This is a temporary state that only occurs during
            unpreparation. Specifically, it means that a Close message for the statement has already been written
            to the write buffer.
            </summary>
        </member>
        <member name="F:Npgsql.PreparedState.Unprepared">
            <summary>
            The statement has been unprepared and is no longer usable.
            </summary>
        </member>
        <member name="F:Npgsql.PreparedStatementManager.NumPrepared">
            <summary>
            Total number of current prepared statements (whether explicit or automatic).
            </summary>
        </member>
        <member name="M:Npgsql.Schema.DbColumnSchemaGenerator.GenerateOldColumnsQuery(System.String)">
            <summary>
            Stripped-down version of <see cref="M:Npgsql.Schema.DbColumnSchemaGenerator.GenerateColumnsQuery(System.Version,System.String)"/>, mainly to support Amazon Redshift.
            </summary>
        </member>
        <member name="M:Npgsql.Schema.DbColumnSchemaGenerator.ColumnPostConfig(Npgsql.Schema.NpgsqlDbColumn,System.Int32)">
            <summary>
            Performs some post-setup configuration that's common to both table columns and non-columns.
            </summary>
        </member>
        <member name="T:Npgsql.Schema.NpgsqlDbColumn">
             <summary>
             Provides schema information about a column.
             </summary>
             <remarks>
             Note that this can correspond to a field returned in a query which isn't an actual table column
            
             See https://msdn.microsoft.com/en-us/library/system.data.sqlclient.sqldatareader.getschematable(v=vs.110).aspx
             for information on the meaning of the different fields.
             </remarks>
        </member>
        <member name="M:Npgsql.SqlQueryParser.ParseRawQuery(System.String,System.Boolean,Npgsql.NpgsqlParameterCollection,System.Collections.Generic.List{Npgsql.NpgsqlStatement},System.Boolean)">
            <summary>
            Receives a raw SQL query as passed in by the user, and performs some processing necessary
            before sending to the backend.
            This includes doing parameter placebolder processing (@p => $1), and splitting the query
            up by semicolons if needed (SELECT 1; SELECT 2)
            </summary>
            <param name="sql">Raw user-provided query.</param>
            <param name="standardConformantStrings">Whether the PostgreSQL session is configured to use standard conformant strings.</param>
            <param name="parameters">The parameters configured on the <see cref="T:Npgsql.NpgsqlCommand"/> of this query
            or an empty <see cref="T:Npgsql.NpgsqlParameterCollection"/> if deriveParameters is set to true.</param>
            <param name="statements">An empty list to be populated with the statements parsed by this method</param>
            <param name="deriveParameters">A bool indicating whether parameters contains a list of preconfigured parameters or an empty list to be filled with derived parameters.</param>
        </member>
        <member name="M:Npgsql.TaskExtensions.WithTimeout``1(System.Threading.Tasks.Task{``0},Npgsql.NpgsqlTimeout)">
            <summary>
            Utility that simplifies awaiting a task with a timeout. If the given task does not
            complete within <paramref name="timeout"/>, a <see cref="T:System.TimeoutException"/> is thrown.
            </summary>
            <param name="task">The task to be awaited</param>
            <param name="timeout">How much time to allow <paramref name="task"/> to complete before throwing a <see cref="T:System.TimeoutException"/></param>
            <returns>An awaitable task that represents the original task plus the timeout</returns>
        </member>
        <member name="M:Npgsql.TaskExtensions.WithTimeout(System.Threading.Tasks.Task,Npgsql.NpgsqlTimeout)">
            <summary>
            Utility that simplifies awaiting a task with a timeout. If the given task does not
            complete within <paramref name="timeout"/>, a <see cref="T:System.TimeoutException"/> is thrown.
            </summary>
            <param name="task">The task to be awaited</param>
            <param name="timeout">How much time to allow <paramref name="task"/> to complete before throwing a <see cref="T:System.TimeoutException"/></param>
            <returns>An awaitable task that represents the original task plus the timeout</returns>
        </member>
        <member name="M:Npgsql.TaskExtensions.WithCancellation``1(System.Threading.Tasks.Task{``0},System.Threading.CancellationToken)">
            <summary>
            Allows you to cancel awaiting for a non-cancellable task.
            </summary>
            <remarks>
            Read http://blogs.msdn.com/b/pfxteam/archive/2012/10/05/how-do-i-cancel-non-cancelable-async-operations.aspx
            and be very careful with this.
            </remarks>
        </member>
        <member name="M:Npgsql.TaskExtensions.WithCancellation(System.Threading.Tasks.Task,System.Threading.CancellationToken)">
            <summary>
            Allows you to cancel awaiting for a non-cancellable task.
            </summary>
            <remarks>
            Read http://blogs.msdn.com/b/pfxteam/archive/2012/10/05/how-do-i-cancel-non-cancelable-async-operations.aspx
            and be very careful with this.
            </remarks>
        </member>
        <member name="M:Npgsql.Tls.TlsClientStream.#ctor(System.IO.Stream)">
            <summary>
            Creates a new TlsClientStream with the given underlying stream.
            The handshake must be manually initiated with the method PerformInitialHandshake.
            </summary>
            <param name="baseStream">Base stream</param>
        </member>
        <member name="M:Npgsql.Tls.TlsClientStream.ReadRecord(System.Boolean)">
            <summary>
            Makes sure there is at least one full record available at _readStart.
            Also sets _packetLen (does not include packet header of 5 bytes).
            </summary>
            <returns>True on success, false on End Of Stream.</returns>
        </member>
        <member name="M:Npgsql.Tls.TlsClientStream.Encrypt(System.Int32,System.Int32)">
            <summary>
            Encrypts a record.
            A header should be at startPos containing TLS record type and version.
            At startPos + 5 + ivLen the plaintext should start.
            </summary>
            <param name="startPos">Should point to the beginning of the record (content type)</param>
            <param name="len">Plaintext length (without header)</param>
            <returns>The byte position after the last byte in this encrypted record</returns>
        </member>
        <member name="M:Npgsql.Tls.TlsClientStream.HasBufferedReadData(System.Boolean)">
            <summary>
            This method checks whether there are at least 1 byte that can be read in the buffer.
            If not, but there are renegotiation messages in the buffer, these are first processed.
            This method should be called between each Read and Write to make sure the buffer is empty before writing.
            Only when this method returns false it is safe to call Write.
            </summary>
            <param name="checkNetworkStream">Whether we should also look in the underlying NetworkStream</param>
            <returns>Whether there is available application data</returns>
        </member>
        <member name="M:Npgsql.Tls.Utils.PRF(System.Security.Cryptography.HMAC,System.String,System.Byte[],System.Int32)">
            <summary>
            hmac should be initialized with the secret key
            </summary>
            <param name="hmac"></param>
            <param name="label"></param>
            <param name="seed"></param>
            <param name="bytesNeeded"></param>
            <returns></returns>
        </member>
        <member name="M:Npgsql.TypeHandlers.ArrayHandler.CreateArrayHandler(Npgsql.PostgresTypes.PostgresType)">
            <inheritdoc />
        </member>
        <member name="M:Npgsql.TypeHandlers.ArrayHandler.CreateRangeHandler(Npgsql.PostgresTypes.PostgresType)">
            <inheritdoc />
        </member>
        <member name="T:Npgsql.TypeHandlers.ArrayHandler`1">
            <summary>
            Base class for all type handlers which handle PostgreSQL arrays.
            </summary>
            <remarks>
            http://www.postgresql.org/docs/current/static/arrays.html
            </remarks>
        </member>
        <member name="T:Npgsql.TypeHandlers.ArrayHandlerWithPsv`2">
            <remarks>
            http://www.postgresql.org/docs/current/static/arrays.html
            </remarks>
            <typeparam name="TElement">The .NET type contained as an element within this array</typeparam>
            <typeparam name="TElementPsv">The .NET provider-specific type contained as an element within this array</typeparam>
        </member>
        <member name="T:Npgsql.TypeHandlers.BitStringHandler">
            <summary>
            Handler for the PostgreSQL bit string type.
            Note that for BIT(1), this handler will return a bool by default, to align with SQLClient
            (see discussion https://github.com/npgsql/npgsql/pull/362#issuecomment-59622101).
            </summary>
            <remarks>
            http://www.postgresql.org/docs/current/static/datatype-bit.html
            </remarks>
        </member>
        <member name="T:Npgsql.TypeHandlers.BitStringArrayHandler">
            <summary>
            A special handler for arrays of bit strings.
            Differs from the standard array handlers in that it returns arrays of bool for BIT(1) and arrays
            of BitArray otherwise (just like the scalar BitStringHandler does).
            </summary>
        </member>
        <member name="T:Npgsql.TypeHandlers.BoolHandler">
            <remarks>
            http://www.postgresql.org/docs/current/static/datatype-boolean.html
            </remarks>
        </member>
        <member name="T:Npgsql.TypeHandlers.ByteaHandler">
            <remarks>
            http://www.postgresql.org/docs/current/static/datatype-binary.html
            </remarks>
        </member>
        <member name="M:Npgsql.TypeHandlers.ByteaHandler.Read(Npgsql.NpgsqlReadBuffer,System.Int32,System.Boolean,Npgsql.BackendMessages.FieldDescription)">
            <inheritdoc />
        </member>
        <member name="M:Npgsql.TypeHandlers.ByteaHandler.ValidateAndGetLength(System.Byte[],Npgsql.NpgsqlLengthCache@,Npgsql.NpgsqlParameter)">
            <inheritdoc />
        </member>
        <member name="M:Npgsql.TypeHandlers.ByteaHandler.ValidateAndGetLength(System.ArraySegment{System.Byte},Npgsql.NpgsqlLengthCache@,Npgsql.NpgsqlParameter)">
            <inheritdoc />
        </member>
        <member name="M:Npgsql.TypeHandlers.ByteaHandler.Write(System.Byte[],Npgsql.NpgsqlWriteBuffer,Npgsql.NpgsqlLengthCache,Npgsql.NpgsqlParameter,System.Boolean)">
            <inheritdoc />
        </member>
        <member name="M:Npgsql.TypeHandlers.ByteaHandler.Write(System.ArraySegment{System.Byte},Npgsql.NpgsqlWriteBuffer,Npgsql.NpgsqlLengthCache,Npgsql.NpgsqlParameter,System.Boolean)">
            <inheritdoc />
        </member>
        <member name="T:Npgsql.TypeHandlers.DateTimeHandlers.DateHandler">
            <remarks>
            http://www.postgresql.org/docs/current/static/datatype-datetime.html
            </remarks>
        </member>
        <member name="F:Npgsql.TypeHandlers.DateTimeHandlers.DateHandler._convertInfinityDateTime">
            <summary>
            Whether to convert positive and negative infinity values to DateTime.{Max,Min}Value when
            a DateTime is requested
            </summary>
        </member>
        <member name="M:Npgsql.TypeHandlers.DateTimeHandlers.DateHandler.ReadPsv(Npgsql.NpgsqlReadBuffer,System.Int32,Npgsql.BackendMessages.FieldDescription)">
            <remarks>
            Copied wholesale from Postgresql backend/utils/adt/datetime.c:j2date
            </remarks>
        </member>
        <member name="T:Npgsql.TypeHandlers.DateTimeHandlers.IntervalHandler">
            <remarks>
            http://www.postgresql.org/docs/current/static/datatype-datetime.html
            </remarks>
        </member>
        <member name="F:Npgsql.TypeHandlers.DateTimeHandlers.IntervalHandler._integerFormat">
            <summary>
            A deprecated compile-time option of PostgreSQL switches to a floating-point representation of some date/time
            fields. Some PostgreSQL-like databases (e.g. CrateDB) use floating-point representation by default and do not
            provide the option of switching to integer format.
            </summary>
        </member>
        <member name="T:Npgsql.TypeHandlers.DateTimeHandlers.TimeHandler">
            <remarks>
            http://www.postgresql.org/docs/current/static/datatype-datetime.html
            </remarks>
        </member>
        <member name="F:Npgsql.TypeHandlers.DateTimeHandlers.TimeHandler._integerFormat">
            <summary>
            A deprecated compile-time option of PostgreSQL switches to a floating-point representation of some date/time
            fields. Some PostgreSQL-like databases (e.g. CrateDB) use floating-point representation by default and do not
            provide the option of switching to integer format.
            </summary>
        </member>
        <member name="T:Npgsql.TypeHandlers.DateTimeHandlers.TimestampHandler">
            <remarks>
            http://www.postgresql.org/docs/current/static/datatype-datetime.html
            </remarks>
        </member>
        <member name="F:Npgsql.TypeHandlers.DateTimeHandlers.TimestampHandler._integerFormat">
            <summary>
            A deprecated compile-time option of PostgreSQL switches to a floating-point representation of some date/time
            fields. Some PostgreSQL-like databases (e.g. CrateDB) use floating-point representation by default and do not
            provide the option of switching to integer format.
            </summary>
        </member>
        <member name="F:Npgsql.TypeHandlers.DateTimeHandlers.TimestampHandler.ConvertInfinityDateTime">
            <summary>
            Whether to convert positive and negative infinity values to DateTime.{Max,Min}Value when
            a DateTime is requested
            </summary>
        </member>
        <member name="T:Npgsql.TypeHandlers.DateTimeHandlers.TimestampTzHandler">
            <remarks>
            http://www.postgresql.org/docs/current/static/datatype-datetime.html
            </remarks>
        </member>
        <member name="T:Npgsql.TypeHandlers.DateTimeHandlers.TimeTzHandler">
            <remarks>
            http://www.postgresql.org/docs/current/static/datatype-datetime.html
            </remarks>
        </member>
        <member name="F:Npgsql.TypeHandlers.DateTimeHandlers.TimeTzHandler._integerFormat">
            <summary>
            A deprecated compile-time option of PostgreSQL switches to a floating-point representation of some date/time
            fields. Some PostgreSQL-like databases (e.g. CrateDB) use floating-point representation by default and do not
            provide the option of switching to integer format.
            </summary>
        </member>
        <member name="T:Npgsql.TypeHandlers.IEnumHandler">
            <summary>
            Interface implemented by all concrete handlers which handle enums
            </summary>
        </member>
        <member name="P:Npgsql.TypeHandlers.IEnumHandler.EnumType">
            <summary>
            The CLR enum type mapped to the PostgreSQL enum
            </summary>
        </member>
        <member name="T:Npgsql.TypeHandlers.IEnumTypeHandlerFactory">
            <summary>
            Interface implemented by all enum handler factories.
            Used to expose the name translator for those reflecting enum mappings (e.g. EF Core).
            </summary>
        </member>
        <member name="P:Npgsql.TypeHandlers.IEnumTypeHandlerFactory.NameTranslator">
            <summary>
            The name translator used for this enum.
            </summary>
        </member>
        <member name="T:Npgsql.TypeHandlers.FullTextSearchHandlers.TsQueryHandler">
            <summary>
            http://www.postgresql.org/docs/current/static/datatype-textsearch.html
            </summary>
        </member>
        <member name="T:Npgsql.TypeHandlers.FullTextSearchHandlers.TsVectorHandler">
            <summary>
            http://www.postgresql.org/docs/current/static/datatype-textsearch.html
            </summary>
        </member>
        <member name="T:Npgsql.TypeHandlers.GeometricHandlers.BoxHandler">
            <summary>
            Type handler for the PostgreSQL geometric box type.
            </summary>
            <remarks>
            http://www.postgresql.org/docs/current/static/datatype-geometric.html
            </remarks>
        </member>
        <member name="T:Npgsql.TypeHandlers.GeometricHandlers.CircleHandler">
            <summary>
            Type handler for the PostgreSQL geometric circle type.
            </summary>
            <remarks>
            http://www.postgresql.org/docs/current/static/datatype-geometric.html
            </remarks>
        </member>
        <member name="T:Npgsql.TypeHandlers.GeometricHandlers.LineHandler">
            <summary>
            Type handler for the PostgreSQL geometric line type.
            </summary>
            <remarks>
            http://www.postgresql.org/docs/current/static/datatype-geometric.html
            </remarks>
        </member>
        <member name="T:Npgsql.TypeHandlers.GeometricHandlers.LineSegmentHandler">
            <summary>
            Type handler for the PostgreSQL geometric line segment type.
            </summary>
            <remarks>
            http://www.postgresql.org/docs/current/static/datatype-geometric.html
            </remarks>
        </member>
        <member name="T:Npgsql.TypeHandlers.GeometricHandlers.PathHandler">
            <summary>
            Type handler for the PostgreSQL geometric path segment type (open or closed).
            </summary>
            <remarks>
            http://www.postgresql.org/docs/current/static/datatype-geometric.html
            </remarks>
        </member>
        <member name="T:Npgsql.TypeHandlers.GeometricHandlers.PointHandler">
            <summary>
            Type handler for the PostgreSQL geometric point type.
            </summary>
            <remarks>
            http://www.postgresql.org/docs/current/static/datatype-geometric.html
            </remarks>
        </member>
        <member name="T:Npgsql.TypeHandlers.GeometricHandlers.PolygonHandler">
            <summary>
            Type handler for the PostgreSQL geometric polygon type.
            </summary>
            <remarks>
            http://www.postgresql.org/docs/current/static/datatype-geometric.html
            </remarks>
        </member>
        <member name="F:Npgsql.TypeHandlers.HstoreHandler._textHandler">
            <summary>
            The text handler to which we delegate encoding/decoding of the actual strings
            </summary>
        </member>
        <member name="T:Npgsql.TypeHandlers.InternalCharHandler">
            <summary>
            Type handler for the Postgresql "char" type, used only internally
            </summary>
            <remarks>
            http://www.postgresql.org/docs/current/static/datatype-character.html
            </remarks>
        </member>
        <member name="T:Npgsql.TypeHandlers.InternalTypesHandlers.Int2VectorHandler">
            <summary>
            An int2vector is simply a regular array of shorts, with the sole exception that its lower bound must
            be 0 (we send 1 for regular arrays).
            </summary>
        </member>
        <member name="T:Npgsql.TypeHandlers.InternalTypesHandlers.OIDVectorHandler">
            <summary>
            An OIDVector is simply a regular array of uints, with the sole exception that its lower bound must
            be 0 (we send 1 for regular arrays).
            </summary>
        </member>
        <member name="T:Npgsql.TypeHandlers.JsonbHandler">
            <summary>
            JSONB binary encoding is a simple UTF8 string, but prepended with a version number.
            </summary>
        </member>
        <member name="F:Npgsql.TypeHandlers.JsonbHandler.JsonbProtocolVersion">
            <summary>
            Prepended to the string in the wire encoding
            </summary>
        </member>
        <member name="P:Npgsql.TypeHandlers.IMappedCompositeHandler.CompositeType">
            <summary>
            The CLR type mapped to the PostgreSQL composite type.
            </summary>
        </member>
        <member name="T:Npgsql.TypeHandlers.IMappedCompositeTypeHandlerFactory">
            <summary>
            Interface implemented by all mapped composite handler factories.
            Used to expose the name translator for those reflecting enum mappings (e.g. EF Core).
            </summary>
        </member>
        <member name="P:Npgsql.TypeHandlers.IMappedCompositeTypeHandlerFactory.NameTranslator">
            <summary>
            The name translator used for this enum.
            </summary>
        </member>
        <member name="P:Npgsql.TypeHandlers.IMappedEnumHandler.EnumType">
            <summary>
            The CLR type mapped to the PostgreSQL composite type.
            </summary>
        </member>
        <member name="T:Npgsql.TypeHandlers.NetworkHandlers.CidrHandler">
            <remarks>
            http://www.postgresql.org/docs/current/static/datatype-net-types.html
            </remarks>
        </member>
        <member name="T:Npgsql.TypeHandlers.NetworkHandlers.InetHandler">
            <remarks>
            http://www.postgresql.org/docs/current/static/datatype-net-types.html
            </remarks>
        </member>
        <member name="T:Npgsql.TypeHandlers.NetworkHandlers.Macaddr8Handler">
            <remarks>
            http://www.postgresql.org/docs/current/static/datatype-net-types.html
            </remarks>
        </member>
        <member name="T:Npgsql.TypeHandlers.NetworkHandlers.MacaddrHandler">
            <remarks>
            http://www.postgresql.org/docs/current/static/datatype-net-types.html
            </remarks>
        </member>
        <member name="T:Npgsql.TypeHandlers.NumericHandlers.DoubleHandler">
            <remarks>
            http://www.postgresql.org/docs/current/static/datatype-numeric.html
            </remarks>
        </member>
        <member name="T:Npgsql.TypeHandlers.NumericHandlers.Int16Handler">
            <remarks>
            http://www.postgresql.org/docs/current/static/datatype-numeric.html
            </remarks>
        </member>
        <member name="T:Npgsql.TypeHandlers.NumericHandlers.Int32Handler">
            <remarks>
            http://www.postgresql.org/docs/current/static/datatype-numeric.html
            </remarks>
        </member>
        <member name="T:Npgsql.TypeHandlers.NumericHandlers.Int64Handler">
            <remarks>
            http://www.postgresql.org/docs/current/static/datatype-numeric.html
            </remarks>
        </member>
        <member name="T:Npgsql.TypeHandlers.NumericHandlers.MoneyHandler">
            <remarks>
            http://www.postgresql.org/docs/current/static/datatype-money.html
            </remarks>
        </member>
        <member name="T:Npgsql.TypeHandlers.NumericHandlers.NumericHandler">
            <remarks>
            http://www.postgresql.org/docs/current/static/datatype-numeric.html
            </remarks>
        </member>
        <member name="T:Npgsql.TypeHandlers.NumericHandlers.SingleHandler">
            <remarks>
            http://www.postgresql.org/docs/current/static/datatype-numeric.html
            </remarks>
        </member>
        <member name="T:Npgsql.TypeHandlers.NumericHandlers.UInt32Handler">
            <remarks>
            http://www.postgresql.org/docs/current/static/datatype-oid.html
            </remarks>
        </member>
        <member name="T:Npgsql.TypeHandlers.RangeHandler`1">
            <summary>
            Type handler for PostgreSQL range types
            </summary>
            <remarks>
            Introduced in PostgreSQL 9.2.
            http://www.postgresql.org/docs/current/static/rangetypes.html
            </remarks>
            <typeparam name="TElement">the range subtype</typeparam>
        </member>
        <member name="F:Npgsql.TypeHandlers.RangeHandler`1._elementHandler">
            <summary>
            The type handler for the element that this range type holds
            </summary>
        </member>
        <member name="M:Npgsql.TypeHandlers.RangeHandler`1.CreateArrayHandler(Npgsql.PostgresTypes.PostgresType)">
            <inheritdoc />
        </member>
        <member name="T:Npgsql.TypeHandlers.RecordHandler">
             <summary>
             Type handler for PostgreSQL record types.
             </summary>
             <remarks>
             http://www.postgresql.org/docs/current/static/datatype-pseudo.html
            
             Encoding (identical to composite):
             A 32-bit integer with the number of columns, then for each column:
             * An OID indicating the type of the column
             * The length of the column(32-bit integer), or -1 if null
             * The column data encoded as binary
             </remarks>
        </member>
        <member name="T:Npgsql.TypeHandlers.UnknownTypeHandler">
            <summary>
            Handles "conversions" for columns sent by the database with unknown OIDs.
            This differs from TextHandler in that its a text-only handler (we don't want to receive binary
            representations of the types registered here).
            Note that this handler is also used in the very initial query that loads the OID mappings
            (chicken and egg problem).
            Also used for sending parameters with unknown types (OID=0)
            </summary>
        </member>
        <member name="T:Npgsql.TypeHandlers.UnmappedCompositeHandler">
             <summary>
             Type handler for PostgreSQL composite types, mapping them to C# dynamic.
             This is the default handler used for composites.
             </summary>
             <seealso cref="T:Npgsql.TypeHandlers.MappedCompositeHandler`1"/>.
             <remarks>
             http://www.postgresql.org/docs/current/static/rowtypes.html
            
             Encoding:
             A 32-bit integer with the number of columns, then for each column:
             * An OID indicating the type of the column
             * The length of the column(32-bit integer), or -1 if null
             * The column data encoded as binary
             </remarks>
        </member>
        <member name="T:Npgsql.TypeHandlers.UuidHandler">
            <remarks>
            http://www.postgresql.org/docs/current/static/datatype-uuid.html
            </remarks>
        </member>
        <member name="T:Npgsql.TypeHandlers.VoidHandler">
            <remarks>
            http://www.postgresql.org/docs/current/static/datatype-boolean.html
            </remarks>
        </member>
        <member name="T:Npgsql.TypeHandling.DefaultTypeHandlerFactory">
            <summary>
            A type handler factory used to instantiate Npgsql's built-in type handlers.
            </summary>
        </member>
        <member name="T:Npgsql.TypeHandling.INpgsqlSimpleTypeHandler`1">
            <summary>
            Type handlers that wish to support reading other types in additional to the main one can
            implement this interface for all those types.
            </summary>
        </member>
        <member name="M:Npgsql.TypeHandling.INpgsqlSimpleTypeHandler`1.Read(Npgsql.NpgsqlReadBuffer,System.Int32,Npgsql.BackendMessages.FieldDescription)">
            <summary>
            Reads a value of type <typeparamref name="T"/> with the given length from the provided buffer,
            with the assumption that it is entirely present in the provided memory buffer and no I/O will be
            required. 
            </summary>
            <param name="buf">The buffer from which to read.</param>
            <param name="len">The byte length of the value. The buffer might not contain the full length, requiring I/O to be performed.</param>
            <param name="fieldDescription">Additional PostgreSQL information about the type, such as the length in varchar(30).</param>
            <returns>The fully-read value.</returns>
        </member>
        <member name="M:Npgsql.TypeHandling.INpgsqlSimpleTypeHandler`1.ValidateAndGetLength(`0,Npgsql.NpgsqlParameter)">
            <summary>
            Responsible for validating that a value represents a value of the correct and which can be
            written for PostgreSQL - if the value cannot be written for any reason, an exception shold be thrown.
            Also returns the byte length needed to write the value.
            </summary>
            <param name="value">The value to be written to PostgreSQL</param>
            <param name="parameter">
            The <see cref="T:Npgsql.NpgsqlParameter"/> instance where this value resides. Can be used to access additional
            information relevant to the write process (e.g. <see cref="P:Npgsql.NpgsqlParameter.Size"/>).
            </param>
            <returns>The number of bytes required to write the value.</returns>
        </member>
        <member name="M:Npgsql.TypeHandling.INpgsqlSimpleTypeHandler`1.Write(`0,Npgsql.NpgsqlWriteBuffer,Npgsql.NpgsqlParameter)">
            <summary>
            Writes a value to the provided buffer, with the assumption that there is enough space in the buffer
            (no I/O will occur). The Npgsql core will have taken care of that.
            </summary>
            <param name="value">The value to write.</param>
            <param name="buf">The buffer to which to write.</param>
            <param name="parameter">
            The <see cref="T:Npgsql.NpgsqlParameter"/> instance where this value resides. Can be used to access additional
            information relevant to the write process (e.g. <see cref="P:Npgsql.NpgsqlParameter.Size"/>).
            </param>
        </member>
        <member name="T:Npgsql.TypeHandling.INpgsqlTypeHandler`1">
            <summary>
            Type handlers that wish to support reading other types in additional to the main one can
            implement this interface for all those types.
            </summary>
        </member>
        <member name="M:Npgsql.TypeHandling.INpgsqlTypeHandler`1.Read(Npgsql.NpgsqlReadBuffer,System.Int32,System.Boolean,Npgsql.BackendMessages.FieldDescription)">
            <summary>
            Reads a value of type <typeparamref name="T"/> with the given length from the provided buffer,
            using either sync or async I/O.
            </summary>
            <param name="buf">The buffer from which to read.</param>
            <param name="len">The byte length of the value. The buffer might not contain the full length, requiring I/O to be performed.</param>
            <param name="async">If I/O is required to read the full length of the value, whether it should be performed synchronously or asynchronously.</param>
            <param name="fieldDescription">Additional PostgreSQL information about the type, such as the length in varchar(30).</param>
            <returns>The fully-read value.</returns>
        </member>
        <member name="M:Npgsql.TypeHandling.INpgsqlTypeHandler`1.ValidateAndGetLength(`0,Npgsql.NpgsqlLengthCache@,Npgsql.NpgsqlParameter)">
            <summary>
            Responsible for validating that a value represents a value of the correct and which can be
            written for PostgreSQL - if the value cannot be written for any reason, an exception shold be thrown.
            Also returns the byte length needed to write the value.
            </summary>
            <param name="value">The value to be written to PostgreSQL</param>
            <param name="lengthCache">A cache where the length calculated during the validation phase can be stored for use at the writing phase.</param>
            <param name="parameter">
            The <see cref="T:Npgsql.NpgsqlParameter"/> instance where this value resides. Can be used to access additional
            information relevant to the write process (e.g. <see cref="P:Npgsql.NpgsqlParameter.Size"/>).
            </param>
            <returns>The number of bytes required to write the value.</returns>
        </member>
        <member name="M:Npgsql.TypeHandling.INpgsqlTypeHandler`1.Write(`0,Npgsql.NpgsqlWriteBuffer,Npgsql.NpgsqlLengthCache,Npgsql.NpgsqlParameter,System.Boolean)">
            <summary>
            Writes a value to the provided buffer.
            </summary>
            <param name="value">The value to write.</param>
            <param name="buf">The buffer to which to write.</param>
            <param name="lengthCache">A cache where the length calculated during the validation phase can be stored for use at the writing phase.</param>
            <param name="parameter">
            The <see cref="T:Npgsql.NpgsqlParameter"/> instance where this value resides. Can be used to access additional
            information relevant to the write process (e.g. <see cref="P:Npgsql.NpgsqlParameter.Size"/>).
            </param>
            <param name="async">
            If I/O will be necessary (i.e. the buffer is full), determines whether it will be done synchronously or asynchronously.
            </param>
        </member>
        <member name="T:Npgsql.TypeHandling.ITextReaderHandler">
            <summary>
            Implemented by handlers which support <see cref="M:System.Data.Common.DbDataReader.GetTextReader(System.Int32)"/>, returns a standard
            TextReader given a binary Stream.
            </summary>
        </member>
        <member name="T:Npgsql.TypeHandling.NpgsqlSafeReadException">
            <summary>
            Can be thrown by readers to indicate that interpreting the value failed, but the value was read wholly
            and it is safe to continue reading. Any other exception is assumed to leave the buffer in an unknown position,
            losing protocol sync and therefore setting the connector to state Broken.
            Note that an inner exception is mandatory, and will get thrown to the user instead of the NpgsqlSafeReadException.
            </summary>
        </member>
        <member name="M:Npgsql.TypeHandling.NpgsqlSafeReadException.#ctor(System.Exception)">
            <summary>
            Creates an instance of <see cref="T:Npgsql.TypeHandling.NpgsqlSafeReadException"/>.
            </summary>
            <param name="innerException"></param>
        </member>
        <member name="T:Npgsql.TypeHandling.NpgsqlSimpleTypeHandler`1">
            <summary>
            Base class for all simple type handlers, which read and write short, non-arbitrary lengthed
            values to PostgreSQL. Provides a simpler API to implement when compared to <see cref="T:Npgsql.TypeHandling.NpgsqlTypeHandler"/> -
            Npgsql takes care of all I/O before calling into this type, so no I/O needs to be performed by it.
            </summary>
            <typeparam name="TDefault">
            The default CLR type that this handler will read and write. For example, calling <see cref="M:System.Data.Common.DbDataReader.GetValue(System.Int32)"/>
            on a column with this handler will return a value with type <typeparamref name="TDefault"/>.
            Type handlers can support additional types by implementing <see cref="T:Npgsql.TypeHandling.INpgsqlTypeHandler`1"/>.
            </typeparam>
        </member>
        <member name="M:Npgsql.TypeHandling.NpgsqlSimpleTypeHandler`1.#ctor">
            <summary>
            Constructs an <see cref="T:Npgsql.TypeHandling.NpgsqlSimpleTypeHandler`1"/>.
            </summary>
        </member>
        <member name="M:Npgsql.TypeHandling.NpgsqlSimpleTypeHandler`1.Read(Npgsql.NpgsqlReadBuffer,System.Int32,Npgsql.BackendMessages.FieldDescription)">
            <summary>
            Reads a value of type <typeparamref name="TDefault"/> with the given length from the provided buffer,
            with the assumption that it is entirely present in the provided memory buffer and no I/O will be
            required.
            </summary>
            <param name="buf">The buffer from which to read.</param>
            <param name="len">The byte length of the value. The buffer might not contain the full length, requiring I/O to be performed.</param>
            <param name="fieldDescription">Additional PostgreSQL information about the type, such as the length in varchar(30).</param>
            <returns>The fully-read value.</returns>
        </member>
        <member name="M:Npgsql.TypeHandling.NpgsqlSimpleTypeHandler`1.Read(Npgsql.NpgsqlReadBuffer,System.Int32,System.Boolean,Npgsql.BackendMessages.FieldDescription)">
            <summary>
            Reads a value of type <typeparamref name="TDefault"/> with the given length from the provided buffer,
            using either sync or async I/O. This method is sealed for <see cref="T:Npgsql.TypeHandling.NpgsqlSimpleTypeHandler`1"/>,
            override <see cref="M:Npgsql.TypeHandling.NpgsqlSimpleTypeHandler`1.Read(Npgsql.NpgsqlReadBuffer,System.Int32,Npgsql.BackendMessages.FieldDescription)"/>.
            </summary>
            <param name="buf">The buffer from which to read.</param>
            <param name="len">The byte length of the value. The buffer might not contain the full length, requiring I/O to be performed.</param>
            <param name="async">If I/O is required to read the full length of the value, whether it should be performed synchronously or asynchronously.</param>
            <param name="fieldDescription">Additional PostgreSQL information about the type, such as the length in varchar(30).</param>
            <returns>The fully-read value.</returns>
        </member>
        <member name="M:Npgsql.TypeHandling.NpgsqlSimpleTypeHandler`1.Read``1(Npgsql.NpgsqlReadBuffer,System.Int32,System.Boolean,Npgsql.BackendMessages.FieldDescription)">
            <summary>
            Reads a value of type <typeparamref name="TDefault"/> with the given length from the provided buffer,
            using either sync or async I/O. This method is sealed for <see cref="T:Npgsql.TypeHandling.NpgsqlSimpleTypeHandler`1"/>.
            </summary>
            <param name="buf">The buffer from which to read.</param>
            <param name="len">The byte length of the value. The buffer might not contain the full length, requiring I/O to be performed.</param>
            <param name="async">If I/O is required to read the full length of the value, whether it should be performed synchronously or asynchronously.</param>
            <param name="fieldDescription">Additional PostgreSQL information about the type, such as the length in varchar(30).</param>
            <returns>The fully-read value.</returns>
        </member>
        <member name="M:Npgsql.TypeHandling.NpgsqlSimpleTypeHandler`1.Read``1(Npgsql.NpgsqlReadBuffer,System.Int32,Npgsql.BackendMessages.FieldDescription)">
            <summary>
            Reads a value of type <typeparamref name="TDefault"/> with the given length from the provided buffer.
            with the assumption that it is entirely present in the provided memory buffer and no I/O will be
            required. Type handlers typically don't need to override this - override
            <see cref="M:Npgsql.TypeHandling.NpgsqlSimpleTypeHandler`1.Read(Npgsql.NpgsqlReadBuffer,System.Int32,Npgsql.BackendMessages.FieldDescription)"/> - but may do
            so in exceptional cases where reading of arbitrary types is required.
            </summary>
            <param name="buf">The buffer from which to read.</param>
            <param name="len">The byte length of the value. The buffer might not contain the full length, requiring I/O to be performed.</param>
            <param name="fieldDescription">Additional PostgreSQL information about the type, such as the length in varchar(30).</param>
            <returns>The fully-read value.</returns>
        </member>
        <member name="M:Npgsql.TypeHandling.NpgsqlSimpleTypeHandler`1.ValidateAndGetLength(`0,Npgsql.NpgsqlParameter)">
            <summary>
            Responsible for validating that a value represents a value of the correct and which can be
            written for PostgreSQL - if the value cannot be written for any reason, an exception shold be thrown.
            Also returns the byte length needed to write the value.
            </summary>
            <param name="value">The value to be written to PostgreSQL</param>
            <param name="parameter">
            The <see cref="T:Npgsql.NpgsqlParameter"/> instance where this value resides. Can be used to access additional
            information relevant to the write process (e.g. <see cref="P:Npgsql.NpgsqlParameter.Size"/>).
            </param>
            <returns>The number of bytes required to write the value.</returns>
        </member>
        <member name="M:Npgsql.TypeHandling.NpgsqlSimpleTypeHandler`1.Write(`0,Npgsql.NpgsqlWriteBuffer,Npgsql.NpgsqlParameter)">
            <summary>
            Writes a value to the provided buffer, with the assumption that there is enough space in the buffer
            (no I/O will occur). The Npgsql core will have taken care of that.
            </summary>
            <param name="value">The value to write.</param>
            <param name="buf">The buffer to which to write.</param>
            <param name="parameter">
            The <see cref="T:Npgsql.NpgsqlParameter"/> instance where this value resides. Can be used to access additional
            information relevant to the write process (e.g. <see cref="P:Npgsql.NpgsqlParameter.Size"/>).
            </param>
        </member>
        <member name="M:Npgsql.TypeHandling.NpgsqlSimpleTypeHandler`1.ValidateAndGetLength``1(``0,Npgsql.NpgsqlLengthCache@,Npgsql.NpgsqlParameter)">
            <summary>
            This method is sealed, override <see cref="M:Npgsql.TypeHandling.NpgsqlSimpleTypeHandler`1.ValidateAndGetLength(`0,Npgsql.NpgsqlParameter)"/>.
            </summary>
        </member>
        <member name="M:Npgsql.TypeHandling.NpgsqlSimpleTypeHandler`1.WriteWithLengthInternal``1(``0,Npgsql.NpgsqlWriteBuffer,Npgsql.NpgsqlLengthCache,Npgsql.NpgsqlParameter,System.Boolean)">
            <summary>
            In the vast majority of cases writing a parameter to the buffer won't need to perform I/O.
            </summary>
        </member>
        <member name="M:Npgsql.TypeHandling.NpgsqlSimpleTypeHandler`1.Write(`0,Npgsql.NpgsqlWriteBuffer,Npgsql.NpgsqlLengthCache,Npgsql.NpgsqlParameter,System.Boolean)">
            <summary>
            Simple type handlers override <see cref="M:Npgsql.TypeHandling.NpgsqlSimpleTypeHandler`1.Write(`0,Npgsql.NpgsqlWriteBuffer,Npgsql.NpgsqlParameter)"/> instead of this.
            </summary>
        </member>
        <member name="M:Npgsql.TypeHandling.NpgsqlSimpleTypeHandler`1.ValidateAndGetLength(`0,Npgsql.NpgsqlLengthCache@,Npgsql.NpgsqlParameter)">
            <summary>
            Simple type handlers override <see cref="M:Npgsql.TypeHandling.NpgsqlSimpleTypeHandler`1.ValidateAndGetLength(`0,Npgsql.NpgsqlParameter)"/> instead of this.
            </summary>
        </member>
        <member name="M:Npgsql.TypeHandling.NpgsqlSimpleTypeHandler`1.ValidateObjectAndGetLength(System.Object,Npgsql.NpgsqlLengthCache@,Npgsql.NpgsqlParameter)">
            <summary>
            Called to validate and get the length of a value of a non-generic <see cref="T:Npgsql.NpgsqlParameter"/>.
            Type handlers generally don't need to override this.
            </summary>
        </member>
        <member name="M:Npgsql.TypeHandling.NpgsqlSimpleTypeHandler`1.WriteObjectWithLength(System.Object,Npgsql.NpgsqlWriteBuffer,Npgsql.NpgsqlLengthCache,Npgsql.NpgsqlParameter,System.Boolean)">
            <summary>
            Called to write the value of a non-generic <see cref="T:Npgsql.NpgsqlParameter"/>.
            Type handlers generally don't need to override this.
            </summary>
        </member>
        <member name="T:Npgsql.TypeHandling.NpgsqlSimpleTypeHandlerWithPsv`2">
            <summary>
            A simple type handler that supports a provider-specific value in addition to its default value.
            This is necessary mainly in cases where the CLR type cannot represent the full range of the
            PostgreSQL type, and a custom CLR type is needed (e.g. <see cref="T:System.DateTime"/> and
            <see cref="T:NpgsqlTypes.NpgsqlDateTime"/>). The provider-specific type <typeparamref name="TPsv"/> will be returned
            from calls to <see cref="M:System.Data.Common.DbDataReader.GetProviderSpecificValue(System.Int32)"/>.
            </summary>
            <typeparam name="TDefault">
            The default CLR type that this handler will read and write. For example, calling <see cref="M:System.Data.Common.DbDataReader.GetValue(System.Int32)"/>
            on a column with this handler will return a value with type <typeparamref name="TDefault"/>.
            Type handlers can support additional types by implementing <see cref="T:Npgsql.TypeHandling.INpgsqlTypeHandler`1"/>.
            </typeparam>
            <typeparam name="TPsv">The provider-specific CLR type that this handler will read and write.</typeparam>
        </member>
        <member name="M:Npgsql.TypeHandling.NpgsqlSimpleTypeHandlerWithPsv`2.ReadPsv(Npgsql.NpgsqlReadBuffer,System.Int32,Npgsql.BackendMessages.FieldDescription)">
            <summary>
            Reads a value of type <typeparamref name="TPsv"/> with the given length from the provided buffer,
            with the assumption that it is entirely present in the provided memory buffer and no I/O will be
            required. 
            </summary>
            <param name="buf">The buffer from which to read.</param>
            <param name="len">The byte length of the value. The buffer might not contain the full length, requiring I/O to be performed.</param>
            <param name="fieldDescription">Additional PostgreSQL information about the type, such as the length in varchar(30).</param>
            <returns>The fully-read value.</returns>
        </member>
        <member name="M:Npgsql.TypeHandling.NpgsqlSimpleTypeHandlerWithPsv`2.ReadPsvAsObject(Npgsql.NpgsqlReadBuffer,System.Int32,Npgsql.BackendMessages.FieldDescription)">
            <summary>
            Reads a column as the type handler's provider-specific type, assuming that it is already entirely
            in memory (i.e. no I/O is necessary). Called by <see cref="T:Npgsql.NpgsqlDefaultDataReader"/>, which
            buffers entire rows in memory.
            </summary>
        </member>
        <member name="M:Npgsql.TypeHandling.NpgsqlSimpleTypeHandlerWithPsv`2.ReadPsvAsObject(Npgsql.NpgsqlReadBuffer,System.Int32,System.Boolean,Npgsql.BackendMessages.FieldDescription)">
            <summary>
            Reads a column as the type handler's provider-specific type. If it is not already entirely in
            memory, sync or async I/O will be performed as specified by <paramref name="async"/>.
            </summary>
        </member>
        <member name="M:Npgsql.TypeHandling.NpgsqlSimpleTypeHandlerWithPsv`2.ValidateAndGetLength(`1,Npgsql.NpgsqlParameter)">
            <summary>
            Responsible for validating that a value represents a value of the correct and which can be
            written for PostgreSQL - if the value cannot be written for any reason, an exception shold be thrown.
            Also returns the byte length needed to write the value.
            </summary>
            <param name="value">The value to be written to PostgreSQL</param>
            <param name="parameter">
            The <see cref="T:Npgsql.NpgsqlParameter"/> instance where this value resides. Can be used to access additional
            information relevant to the write process (e.g. <see cref="P:Npgsql.NpgsqlParameter.Size"/>).
            </param>
            <returns>The number of bytes required to write the value.</returns>
        </member>
        <member name="M:Npgsql.TypeHandling.NpgsqlSimpleTypeHandlerWithPsv`2.Write(`1,Npgsql.NpgsqlWriteBuffer,Npgsql.NpgsqlParameter)">
            <summary>
            Writes a value to the provided buffer, with the assumption that there is enough space in the buffer
            (no I/O will occur). The Npgsql core will have taken care of that.
            </summary>
            <param name="value">The value to write.</param>
            <param name="buf">The buffer to which to write.</param>
            <param name="parameter">
            The <see cref="T:Npgsql.NpgsqlParameter"/> instance where this value resides. Can be used to access additional
            information relevant to the write process (e.g. <see cref="P:Npgsql.NpgsqlParameter.Size"/>).
            </param>
        </member>
        <member name="M:Npgsql.TypeHandling.NpgsqlSimpleTypeHandlerWithPsv`2.CreateArrayHandler(Npgsql.PostgresTypes.PostgresType)">
            <inheeritdoc />
        </member>
        <member name="T:Npgsql.TypeHandling.NpgsqlTypeHandler">
            <summary>
            Base class for all type handlers, which read and write CLR types into their PostgreSQL
            binary representation.
            Type handler writers shouldn't inherit from this class, inherit <see cref="T:Npgsql.TypeHandling.NpgsqlTypeHandler"/>
            or <see cref="T:Npgsql.TypeHandling.NpgsqlSimpleTypeHandler`1"/> instead.
            </summary>
        </member>
        <member name="P:Npgsql.TypeHandling.NpgsqlTypeHandler.PostgresType">
            <summary>
            The PostgreSQL type handled by this type handler. Injected by <see cref="T:Npgsql.TypeHandling.NpgsqlTypeHandlerFactory"/>.
            </summary>
        </member>
        <member name="M:Npgsql.TypeHandling.NpgsqlTypeHandler.Read``1(Npgsql.NpgsqlReadBuffer,System.Int32,System.Boolean,Npgsql.BackendMessages.FieldDescription)">
            <summary>
            Reads a value of type <typeparamref name="TAny"/> with the given length from the provided buffer,
            using either sync or async I/O.
            </summary>
            <param name="buf">The buffer from which to read.</param>
            <param name="len">The byte length of the value. The buffer might not contain the full length, requiring I/O to be performed.</param>
            <param name="async">If I/O is required to read the full length of the value, whether it should be performed synchronously or asynchronously.</param>
            <param name="fieldDescription">Additional PostgreSQL information about the type, such as the length in varchar(30).</param>
            <returns>The fully-read value.</returns>
        </member>
        <member name="M:Npgsql.TypeHandling.NpgsqlTypeHandler.Read``1(Npgsql.NpgsqlReadBuffer,System.Int32,Npgsql.BackendMessages.FieldDescription)">
            <summary>
            Reads a value of type <typeparamref name="TAny"/> with the given length from the provided buffer,
            with the assumption that it is entirely present in the provided memory buffer and no I/O will be
            required. This can save the overhead of async functions and improves performance.
            </summary>
            <param name="buf">The buffer from which to read.</param>
            <param name="len">The byte length of the value. The buffer might not contain the full length, requiring I/O to be performed.</param>
            <param name="fieldDescription">Additional PostgreSQL information about the type, such as the length in varchar(30).</param>
            <returns>The fully-read value.</returns>
        </member>
        <member name="M:Npgsql.TypeHandling.NpgsqlTypeHandler.ReadAsObject(Npgsql.NpgsqlReadBuffer,System.Int32,Npgsql.BackendMessages.FieldDescription)">
            <summary>
            Reads a column as the type handler's default read type, assuming that it is already entirely
            in memory (i.e. no I/O is necessary). Called by <see cref="T:Npgsql.NpgsqlDefaultDataReader"/>, which
            buffers entire rows in memory.
            </summary>
        </member>
        <member name="M:Npgsql.TypeHandling.NpgsqlTypeHandler.ReadAsObject(Npgsql.NpgsqlReadBuffer,System.Int32,System.Boolean,Npgsql.BackendMessages.FieldDescription)">
            <summary>
            Reads a column as the type handler's default read type. If it is not already entirely in
            memory, sync or async I/O will be performed as specified by <paramref name="async"/>.
            </summary>
        </member>
        <member name="M:Npgsql.TypeHandling.NpgsqlTypeHandler.ReadPsvAsObject(Npgsql.NpgsqlReadBuffer,System.Int32,Npgsql.BackendMessages.FieldDescription)">
            <summary>
            Reads a column as the type handler's provider-specific type, assuming that it is already entirely
            in memory (i.e. no I/O is necessary). Called by <see cref="T:Npgsql.NpgsqlDefaultDataReader"/>, which
            buffers entire rows in memory.
            </summary>
        </member>
        <member name="M:Npgsql.TypeHandling.NpgsqlTypeHandler.ReadPsvAsObject(Npgsql.NpgsqlReadBuffer,System.Int32,System.Boolean,Npgsql.BackendMessages.FieldDescription)">
            <summary>
            Reads a column as the type handler's provider-specific type. If it is not already entirely in
            memory, sync or async I/O will be performed as specified by <paramref name="async"/>.
            </summary>
        </member>
        <member name="M:Npgsql.TypeHandling.NpgsqlTypeHandler.ReadWithLength``1(Npgsql.NpgsqlReadBuffer,System.Boolean,Npgsql.BackendMessages.FieldDescription)">
            <summary>
            Reads a value from the buffer, assuming our read position is at the value's preceding length.
            If the length is -1 (null), this method will return the default value.
            </summary>
        </member>
        <member name="M:Npgsql.TypeHandling.NpgsqlTypeHandler.ValidateAndGetLength``1(``0,Npgsql.NpgsqlLengthCache@,Npgsql.NpgsqlParameter)">
            <summary>
            Called to validate and get the length of a value of a generic <see cref="T:Npgsql.NpgsqlParameter`1"/>.
            </summary>
        </member>
        <member name="M:Npgsql.TypeHandling.NpgsqlTypeHandler.WriteWithLengthInternal``1(``0,Npgsql.NpgsqlWriteBuffer,Npgsql.NpgsqlLengthCache,Npgsql.NpgsqlParameter,System.Boolean)">
            <summary>
            Called to write the value of a generic <see cref="T:Npgsql.NpgsqlParameter`1"/>.
            </summary>
        </member>
        <member name="M:Npgsql.TypeHandling.NpgsqlTypeHandler.ValidateObjectAndGetLength(System.Object,Npgsql.NpgsqlLengthCache@,Npgsql.NpgsqlParameter)">
            <summary>
            Responsible for validating that a value represents a value of the correct and which can be
            written for PostgreSQL - if the value cannot be written for any reason, an exception shold be thrown.
            Also returns the byte length needed to write the value.
            </summary>
            <param name="value">The value to be written to PostgreSQL</param>
            <param name="lengthCache">
            If the byte length calculation is costly (e.g. for UTF-8 strings), its result can be stored in the
            length cache to be reused in the writing process, preventing recalculation.
            </param>
            <param name="parameter">
            The <see cref="T:Npgsql.NpgsqlParameter"/> instance where this value resides. Can be used to access additional
            information relevant to the write process (e.g. <see cref="P:Npgsql.NpgsqlParameter.Size"/>).
            </param>
            <returns>The number of bytes required to write the value.</returns>
        </member>
        <member name="M:Npgsql.TypeHandling.NpgsqlTypeHandler.WriteObjectWithLength(System.Object,Npgsql.NpgsqlWriteBuffer,Npgsql.NpgsqlLengthCache,Npgsql.NpgsqlParameter,System.Boolean)">
            <summary>
            Writes a value to the provided buffer, using either sync or async I/O.
            </summary>
            <param name="value">The value to write.</param>
            <param name="buf">The buffer to which to write.</param>
            <param name="lengthCache"></param>
            <param name="parameter">
            The <see cref="T:Npgsql.NpgsqlParameter"/> instance where this value resides. Can be used to access additional
            information relevant to the write process (e.g. <see cref="P:Npgsql.NpgsqlParameter.Size"/>).
            </param>
            <param name="async">If I/O is required to read the full length of the value, whether it should be performed synchronously or asynchronously.</param>
        </member>
        <member name="M:Npgsql.TypeHandling.NpgsqlTypeHandler.CreateArrayHandler(Npgsql.PostgresTypes.PostgresType)">
            <summary>
            Creates a type handler for arrays of this handler's type.
            </summary>
        </member>
        <member name="M:Npgsql.TypeHandling.NpgsqlTypeHandler.CreateRangeHandler(Npgsql.PostgresTypes.PostgresType)">
            <summary>
            Creates a type handler for ranges of this handler's type.
            </summary>
        </member>
        <member name="M:Npgsql.TypeHandling.NpgsqlTypeHandler.CreateConversionButNoParamException(System.Type)">
            <summary>
            Used to create an exception when the provided type can be converted and written, but an
            instance of <see cref="T:Npgsql.NpgsqlParameter"/> is required for caching of the converted value
            (in <see cref="P:Npgsql.NpgsqlParameter.ConvertedValue"/>.
            </summary>
        </member>
        <member name="T:Npgsql.TypeHandling.NpgsqlTypeHandlerFactory">
            <summary>
            Base class for all type handler factories, which construct type handlers that know how
            to read and write CLR types from/to PostgreSQL types.
            Do not inherit from this class, inherit from <see cref="T:Npgsql.TypeHandling.NpgsqlTypeHandlerFactory`1"/> instead.
            </summary>
        </member>
        <member name="P:Npgsql.TypeHandling.NpgsqlTypeHandlerFactory.DefaultValueType">
            <summary>
            The default CLR type that handlers produced by this factory will read and write.
            </summary>
        </member>
        <member name="T:Npgsql.TypeHandling.NpgsqlTypeHandlerFactory`1">
            <summary>
            Base class for all type handler factories, which construct type handlers that know how
            to read and write CLR types from/to PostgreSQL types. Type handler factories are set up
            via <see cref="T:Npgsql.TypeMapping.NpgsqlTypeMapping"/> in either the global or connection-specific type mapper.
            </summary>
            <seealso cref="T:Npgsql.TypeMapping.NpgsqlTypeMapping"/>
            <seealso cref="P:Npgsql.NpgsqlConnection.GlobalTypeMapper"/>
            <seealso cref="P:Npgsql.NpgsqlConnection.TypeMapper"/>
            <typeparam name="TDefault">The default CLR type that handlers produced by this factory will read and write.</typeparam>
        </member>
        <member name="M:Npgsql.TypeHandling.NpgsqlTypeHandlerFactory`1.Create(Npgsql.NpgsqlConnection)">
            <summary>
            Creates a type handler. The provided connection can be examined to modify type handler
            behavior based on server settings, etc.
            </summary>
        </member>
        <member name="P:Npgsql.TypeHandling.NpgsqlTypeHandlerFactory`1.DefaultValueType">
            <summary>
            The default CLR type that handlers produced by this factory will read and write.
            </summary>
        </member>
        <member name="T:Npgsql.TypeHandling.NpgsqlTypeHandler`1">
            <summary>
            Base class for all type handlers, which read and write CLR types into their PostgreSQL
            binary representation. Unless your type is arbitrary-length, consider inheriting from
            <see cref="T:Npgsql.TypeHandling.NpgsqlSimpleTypeHandler`1"/> instead.
            </summary>
            <typeparam name="TDefault">
            The default CLR type that this handler will read and write. For example, calling <see cref="M:System.Data.Common.DbDataReader.GetValue(System.Int32)"/>
            on a column with this handler will return a value with type <typeparamref name="TDefault"/>.
            Type handlers can support additional types by implementing <see cref="T:Npgsql.TypeHandling.INpgsqlTypeHandler`1"/>.
            </typeparam>
        </member>
        <member name="M:Npgsql.TypeHandling.NpgsqlTypeHandler`1.#ctor">
            <summary>
            Constructs an <see cref="T:Npgsql.TypeHandling.NpgsqlTypeHandler`1"/>.
            </summary>
        </member>
        <member name="M:Npgsql.TypeHandling.NpgsqlTypeHandler`1.Read(Npgsql.NpgsqlReadBuffer,System.Int32,System.Boolean,Npgsql.BackendMessages.FieldDescription)">
            <summary>
            Reads a value of type <typeparamref name="TDefault"/> with the given length from the provided buffer,
            using either sync or async I/O.
            </summary>
            <param name="buf">The buffer from which to read.</param>
            <param name="len">The byte length of the value. The buffer might not contain the full length, requiring I/O to be performed.</param>
            <param name="async">If I/O is required to read the full length of the value, whether it should be performed synchronously or asynchronously.</param>
            <param name="fieldDescription">Additional PostgreSQL information about the type, such as the length in varchar(30).</param>
            <returns>The fully-read value.</returns>
        </member>
        <member name="M:Npgsql.TypeHandling.NpgsqlTypeHandler`1.Read``1(Npgsql.NpgsqlReadBuffer,System.Int32,System.Boolean,Npgsql.BackendMessages.FieldDescription)">
            <summary>
            Reads a value of type <typeparamref name="TDefault"/> with the given length from the provided buffer,
            using either sync or async I/O. Type handlers typically don't need to override this -
            override <see cref="M:Npgsql.TypeHandling.NpgsqlTypeHandler`1.Read(Npgsql.NpgsqlReadBuffer,System.Int32,System.Boolean,Npgsql.BackendMessages.FieldDescription)"/> - but may do
            so in exceptional cases where reading of arbitrary types is required.
            </summary>
            <param name="buf">The buffer from which to read.</param>
            <param name="len">The byte length of the value. The buffer might not contain the full length, requiring I/O to be performed.</param>
            <param name="async">If I/O is required to read the full length of the value, whether it should be performed synchronously or asynchronously.</param>
            <param name="fieldDescription">Additional PostgreSQL information about the type, such as the length in varchar(30).</param>
            <returns>The fully-read value.</returns>
        </member>
        <member name="M:Npgsql.TypeHandling.NpgsqlTypeHandler`1.ValidateAndGetLength(`0,Npgsql.NpgsqlLengthCache@,Npgsql.NpgsqlParameter)">
            <summary>
            Called to validate and get the length of a value of a generic <see cref="T:Npgsql.NpgsqlParameter`1"/>.
            </summary>
        </member>
        <member name="M:Npgsql.TypeHandling.NpgsqlTypeHandler`1.Write(`0,Npgsql.NpgsqlWriteBuffer,Npgsql.NpgsqlLengthCache,Npgsql.NpgsqlParameter,System.Boolean)">
            <summary>
            Called to write the value of a generic <see cref="T:Npgsql.NpgsqlParameter`1"/>.
            </summary>
        </member>
        <member name="M:Npgsql.TypeHandling.NpgsqlTypeHandler`1.ValidateAndGetLength``1(``0,Npgsql.NpgsqlLengthCache@,Npgsql.NpgsqlParameter)">
            <summary>
            Called to validate and get the length of a value of an arbitrary type.
            Checks that the current handler supports that type and throws an exception otherwise.
            </summary>
        </member>
        <member name="M:Npgsql.TypeHandling.NpgsqlTypeHandler`1.WriteWithLengthInternal``1(``0,Npgsql.NpgsqlWriteBuffer,Npgsql.NpgsqlLengthCache,Npgsql.NpgsqlParameter,System.Boolean)">
            <summary>
            In the vast majority of cases writing a parameter to the buffer won't need to perform I/O.
            </summary>
        </member>
        <member name="M:Npgsql.TypeHandling.NpgsqlTypeHandler`1.WriteWithLength``1(``0,Npgsql.NpgsqlWriteBuffer,Npgsql.NpgsqlLengthCache,Npgsql.NpgsqlParameter,System.Boolean)">
            <summary>
            Typically does not need to be overridden by type handlers, but may be needed in some
            cases (e.g. <see cref="T:Npgsql.TypeHandlers.ArrayHandler"/>.
            Note that this method assumes it can write 4 bytes of length (already verified by
            <see cref="M:Npgsql.TypeHandling.NpgsqlTypeHandler`1.WriteWithLengthInternal``1(``0,Npgsql.NpgsqlWriteBuffer,Npgsql.NpgsqlLengthCache,Npgsql.NpgsqlParameter,System.Boolean)"/>).
            </summary>
        </member>
        <member name="M:Npgsql.TypeHandling.NpgsqlTypeHandler`1.ValidateObjectAndGetLength(System.Object,Npgsql.NpgsqlLengthCache@,Npgsql.NpgsqlParameter)">
            <summary>
            Called to validate and get the length of a value of a non-generic <see cref="T:Npgsql.NpgsqlParameter"/>.
            Type handlers generally don't need to override this.
            </summary>
        </member>
        <member name="M:Npgsql.TypeHandling.NpgsqlTypeHandler`1.WriteObjectWithLength(System.Object,Npgsql.NpgsqlWriteBuffer,Npgsql.NpgsqlLengthCache,Npgsql.NpgsqlParameter,System.Boolean)">
            <summary>
            Called to write the value of a non-generic <see cref="T:Npgsql.NpgsqlParameter"/>.
            Type handlers generally don't need to override this.
            </summary>
        </member>
        <member name="M:Npgsql.TypeHandling.NpgsqlTypeHandler`1.CreateArrayHandler(Npgsql.PostgresTypes.PostgresType)">
            <inheritdoc />
        </member>
        <member name="M:Npgsql.TypeHandling.NpgsqlTypeHandler`1.CreateRangeHandler(Npgsql.PostgresTypes.PostgresType)">
            <inheritdoc />
        </member>
        <member name="F:Npgsql.TypeMapping.ConnectorTypeMapper._connector">
            <summary>
            The connector to which this type mapper belongs.
            </summary>
        </member>
        <member name="P:Npgsql.TypeMapping.ConnectorTypeMapper.DatabaseInfo">
            <summary>
            Type information for the database of this mapper. Null for the global mapper.
            </summary>
        </member>
        <member name="F:Npgsql.TypeMapping.ConnectorTypeMapper._byClrType">
            <summary>
            Maps CLR types to their type handlers.
            </summary>
        </member>
        <member name="F:Npgsql.TypeMapping.ConnectorTypeMapper._arrayHandlerByClrType">
            <summary>
            Maps CLR types to their array handlers.
            </summary>
        </member>
        <member name="P:Npgsql.TypeMapping.ConnectorTypeMapper.ChangeCounter">
            <summary>
            Copy of <see cref="P:Npgsql.TypeMapping.GlobalTypeMapper.ChangeCounter"/> at the time when this
            mapper was created, to detect mapping changes. If changes are made to this connection's
            mapper, the change counter is set to -1.
            </summary>
        </member>
        <member name="M:Npgsql.TypeMapping.ConnectorTypeMapper.GetByOID(System.UInt32)">
            <summary>
            Looks up a type handler by its PostgreSQL type's OID.
            </summary>
            <param name="oid">A PostgreSQL type OID</param>
            <returns>A type handler that can be used to encode and decode values.</returns>
        </member>
        <member name="P:Npgsql.TypeMapping.GlobalTypeMapper.ChangeCounter">
            <summary>
            A counter that is incremented whenever a global mapping change occurs.
            Used to invalidate bound type mappers.
            </summary>
        </member>
        <member name="T:Npgsql.TypeMapping.INpgsqlTypeMapper">
            <summary>
            A type mapper, managing how to read and write CLR values to PostgreSQL data types.
            A type mapper exists for each connection, as well as a single global type mapper
            (accessible via <see cref="P:NpgsqlConnection.GlobalTypeMapper"/>).
            </summary>
            <remarks>
            </remarks>
        </member>
        <member name="P:Npgsql.TypeMapping.INpgsqlTypeMapper.DefaultNameTranslator">
            <summary>
            The default name translator to convert CLR type names and member names.
            </summary>
        </member>
        <member name="P:Npgsql.TypeMapping.INpgsqlTypeMapper.Mappings">
            <summary>
            Enumerates all mappings currently set up on this type mapper.
            </summary>
        </member>
        <member name="M:Npgsql.TypeMapping.INpgsqlTypeMapper.AddMapping(Npgsql.TypeMapping.NpgsqlTypeMapping)">
            <summary>
            Adds a new type mapping to this mapper, overwriting any existing mapping in the process.
            </summary>
        </member>
        <member name="M:Npgsql.TypeMapping.INpgsqlTypeMapper.RemoveMapping(System.String)">
            <summary>
            Removes an existing mapping from this mapper. Attempts to read or write this type
            after removal will result in an exception.
            </summary>
            <param name="pgTypeName">A PostgreSQL type name for the type in the database.</param>
        </member>
        <member name="M:Npgsql.TypeMapping.INpgsqlTypeMapper.MapEnum``1(System.String,Npgsql.INpgsqlNameTranslator)">
            <summary>
            Maps a CLR enum to a PostgreSQL enum type.
            </summary>
            <remarks>
            CLR enum labels are mapped by name to PostgreSQL enum labels.
            The translation strategy can be controlled by the <paramref name="nameTranslator"/> parameter,
            which defaults to <see cref="T:Npgsql.NameTranslation.NpgsqlSnakeCaseNameTranslator"/>.
            You can also use the <see cref="T:NpgsqlTypes.PgNameAttribute"/> on your enum fields to manually specify a PostgreSQL enum label.
            If there is a discrepancy between the .NET and database labels while an enum is read or written,
            an exception will be raised.
            </remarks>
            <param name="pgName">
            A PostgreSQL type name for the corresponding enum type in the database.
            If null, the name translator given in <paramref name="nameTranslator"/>will be used.
            </param>
            <param name="nameTranslator">
            A component which will be used to translate CLR names (e.g. SomeClass) into database names (e.g. some_class).
            Defaults to <see cref="T:Npgsql.NameTranslation.NpgsqlSnakeCaseNameTranslator"/>
            </param>
            <typeparam name="TEnum">The .NET enum type to be mapped</typeparam>
        </member>
        <member name="M:Npgsql.TypeMapping.INpgsqlTypeMapper.UnmapEnum``1(System.String,Npgsql.INpgsqlNameTranslator)">
            <summary>
            Removes an existing enum mapping.
            </summary>
            <param name="pgName">
            A PostgreSQL type name for the corresponding enum type in the database.
            If null, the name translator given in <paramref name="nameTranslator"/> will be used.
            </param>
            <param name="nameTranslator">
            A component which will be used to translate CLR names (e.g. SomeClass) into database names (e.g. some_class).
            Defaults to <see cref="T:Npgsql.NameTranslation.NpgsqlSnakeCaseNameTranslator"/>
            </param>
        </member>
        <member name="M:Npgsql.TypeMapping.INpgsqlTypeMapper.MapComposite``1(System.String,Npgsql.INpgsqlNameTranslator)">
            <summary>
            Maps a CLR type to a PostgreSQL composite type.
            </summary>
            <remarks>
            CLR fields and properties by string to PostgreSQL enum labels.
            The translation strategy can be controlled by the <paramref name="nameTranslator"/> parameter,
            which defaults to <see cref="T:Npgsql.NameTranslation.NpgsqlSnakeCaseNameTranslator"/>.
            You can also use the <see cref="T:NpgsqlTypes.PgNameAttribute"/> on your members to manually specify a PostgreSQL enum label.
            If there is a discrepancy between the .NET and database labels while a composite is read or written,
            an exception will be raised.
            </remarks>
            <param name="pgName">
            A PostgreSQL type name for the corresponding enum type in the database.
            If null, the name translator given in <paramref name="nameTranslator"/>will be used.
            </param>
            <param name="nameTranslator">
            A component which will be used to translate CLR names (e.g. SomeClass) into database names (e.g. some_class).
            Defaults to <see cref="T:Npgsql.NameTranslation.NpgsqlSnakeCaseNameTranslator"/>
            </param>
            <typeparam name="T">The .NET type to be mapped</typeparam>
        </member>
        <member name="M:Npgsql.TypeMapping.INpgsqlTypeMapper.UnmapComposite``1(System.String,Npgsql.INpgsqlNameTranslator)">
            <summary>
            Removes an existing enum mapping.
            </summary>
            <param name="pgName">
            A PostgreSQL type name for the corresponding composite type in the database.
            If null, the name translator given in <paramref name="nameTranslator"/>will be used.
            </param>
            <param name="nameTranslator">
            A component which will be used to translate CLR names (e.g. SomeClass) into database names (e.g. some_class).
            Defaults to <see cref="T:Npgsql.NameTranslation.NpgsqlSnakeCaseNameTranslator"/>
            </param>
        </member>
        <member name="M:Npgsql.TypeMapping.INpgsqlTypeMapper.Reset">
            <summary>
            Resets all mapping changes performed on this type mapper and reverts it to its original, starting state.
            </summary>
        </member>
        <member name="T:Npgsql.TypeMapping.NpgsqlTypeMappingBuilder">
            <summary>
            Builds instances of <see cref="T:Npgsql.TypeMapping.NpgsqlTypeMapping"/> for addition into <see cref="T:Npgsql.TypeMapping.INpgsqlTypeMapper"/>.
            </summary>
        </member>
        <member name="P:Npgsql.TypeMapping.NpgsqlTypeMappingBuilder.PgTypeName">
            <summary>
            The name of the PostgreSQL type name, as it appears in the pg_type catalog.
            </summary>
            <remarks>
            This can a a partial name (without the schema), or a fully-qualified name
            (schema.typename) - the latter can be used if you have two types with the same
            name in different schemas.
            </remarks>
        </member>
        <member name="P:Npgsql.TypeMapping.NpgsqlTypeMappingBuilder.NpgsqlDbType">
            <summary>
            The <see cref="P:Npgsql.TypeMapping.NpgsqlTypeMappingBuilder.NpgsqlDbType"/> that corresponds to this type. Setting an
            <see cref="T:Npgsql.NpgsqlParameter"/>'s <see cref="P:Npgsql.NpgsqlParameter.NpgsqlDbType"/> property
            to this value will make Npgsql write its value to PostgreSQL with this mapping.
            </summary>
        </member>
        <member name="P:Npgsql.TypeMapping.NpgsqlTypeMappingBuilder.DbTypes">
            <summary>
            A set of <see cref="T:System.Data.DbType"/>s that correspond to this type. Setting an
            <see cref="T:Npgsql.NpgsqlParameter"/>'s <see cref="P:Npgsql.NpgsqlParameter.DbType"/> property
            to one of these values will make Npgsql write its value to PostgreSQL with this mapping.
            </summary>
        </member>
        <member name="P:Npgsql.TypeMapping.NpgsqlTypeMappingBuilder.ClrTypes">
            <summary>
            A set of CLR types that correspond to this type. Setting an
            <see cref="T:Npgsql.NpgsqlParameter"/>'s <see cref="P:Npgsql.NpgsqlParameter.Value"/> property
            to one of these types will make Npgsql write its value to PostgreSQL with this mapping.
            </summary>
        </member>
        <member name="P:Npgsql.TypeMapping.NpgsqlTypeMappingBuilder.InferredDbType">
            <summary>
            Determines what is returned from <see cref="P:Npgsql.NpgsqlParameter.DbType"/> when this mapping
            is used.
            </summary>
        </member>
        <member name="P:Npgsql.TypeMapping.NpgsqlTypeMappingBuilder.TypeHandlerFactory">
            <summary>
            A factory for a type handler that will be used to read and write values for PostgreSQL type.
            </summary>
        </member>
        <member name="M:Npgsql.TypeMapping.NpgsqlTypeMappingBuilder.Build">
            <summary>
            Builds an <see cref="T:Npgsql.TypeMapping.NpgsqlTypeMapping"/> that can be added to an <see cref="T:Npgsql.TypeMapping.INpgsqlTypeMapper"/>.
            </summary>
            <returns></returns>
        </member>
        <member name="T:Npgsql.TypeMapping.NpgsqlTypeMapping">
            <summary>
            Represents a type mapping for a PostgreSQL data type, which can be added to a type mapper,
            managing when that data type will be read and written and how.
            </summary>
            <seealso cref="P:Npgsql.NpgsqlConnection.GlobalTypeMapper"/>
            <seealso cref="P:Npgsql.NpgsqlConnection.TypeMapper"/>
        </member>
        <member name="P:Npgsql.TypeMapping.NpgsqlTypeMapping.PgTypeName">
            <summary>
            The name of the PostgreSQL type name, as it appears in the pg_type catalog.
            </summary>
            <remarks>
            This can a a partial name (without the schema), or a fully-qualified name
            (schema.typename) - the latter can be used if you have two types with the same
            name in different schemas.
            </remarks>
        </member>
        <member name="P:Npgsql.TypeMapping.NpgsqlTypeMapping.NpgsqlDbType">
            <summary>
            The <see cref="P:Npgsql.TypeMapping.NpgsqlTypeMapping.NpgsqlDbType"/> that corresponds to this type. Setting an
            <see cref="T:Npgsql.NpgsqlParameter"/>'s <see cref="P:Npgsql.NpgsqlParameter.NpgsqlDbType"/> property
            to this value will make Npgsql write its value to PostgreSQL with this mapping.
            </summary>
        </member>
        <member name="P:Npgsql.TypeMapping.NpgsqlTypeMapping.DbTypes">
            <summary>
            A set of <see cref="T:System.Data.DbType"/>s that correspond to this type. Setting an
            <see cref="T:Npgsql.NpgsqlParameter"/>'s <see cref="P:Npgsql.NpgsqlParameter.DbType"/> property
            to one of these values will make Npgsql write its value to PostgreSQL with this mapping.
            </summary>
        </member>
        <member name="P:Npgsql.TypeMapping.NpgsqlTypeMapping.ClrTypes">
            <summary>
            A set of CLR types that correspond to this type. Setting an
            <see cref="T:Npgsql.NpgsqlParameter"/>'s <see cref="P:Npgsql.NpgsqlParameter.Value"/> property
            to one of these types will make Npgsql write its value to PostgreSQL with this mapping.
            </summary>
        </member>
        <member name="P:Npgsql.TypeMapping.NpgsqlTypeMapping.InferredDbType">
            <summary>
            Determines what is returned from <see cref="P:Npgsql.NpgsqlParameter.DbType"/> when this mapping
            is used.
            </summary>
        </member>
        <member name="P:Npgsql.TypeMapping.NpgsqlTypeMapping.TypeHandlerFactory">
            <summary>
            A factory for a type handler that will be used to read and write values for PostgreSQL type.
            </summary>
        </member>
        <member name="P:Npgsql.TypeMapping.NpgsqlTypeMapping.DefaultClrType">
            <summary>
            The default CLR type that handlers produced by this factory will read and write.
            Used by the EF Core provider (and possibly others in the future).
            </summary>
        </member>
        <member name="M:Npgsql.TypeMapping.NpgsqlTypeMapping.ToString">
            <summary>
            Returns a string that represents the current object.
            </summary>
        </member>
        <member name="M:Npgsql.TypeMapping.TypeMappingAttribute.#ctor(System.String,System.Nullable{NpgsqlTypes.NpgsqlDbType},System.Data.DbType[],System.Type[],System.Nullable{System.Data.DbType})">
            <summary>
            Maps an Npgsql type handler to a PostgreSQL type.
            </summary>
            <param name="pgName">A PostgreSQL type name as it appears in the pg_type table.</param>
            <param name="npgsqlDbType">
            A member of <see cref="P:Npgsql.TypeMapping.TypeMappingAttribute.NpgsqlDbType"/> which represents this PostgreSQL type.
            An <see cref="T:Npgsql.NpgsqlParameter"/> with <see cref="P:Npgsql.NpgsqlParameter.NpgsqlDbType"/> set to
            this value will be sent with the type handler mapped by this attribute.
            </param>
            <param name="dbTypes">
            All members of <see cref="T:System.Data.DbType"/> which represent this PostgreSQL type.
            An <see cref="T:Npgsql.NpgsqlParameter"/> with <see cref="P:Npgsql.NpgsqlParameter.DbType"/> set to
            one of these values will be sent with the type handler mapped by this attribute.
            </param>
            <param name="clrTypes">
            Any .NET type which corresponds to this PostgreSQL type.
            An <see cref="T:Npgsql.NpgsqlParameter"/> with <see cref="P:Npgsql.NpgsqlParameter.Value"/> set to
            one of these values will be sent with the type handler mapped by this attribute.
            </param>
            <param name="inferredDbType">
            The "primary" <see cref="T:System.Data.DbType"/> which best corresponds to this PostgreSQL type.
            When <see cref="P:Npgsql.NpgsqlParameter.NpgsqlDbType"/> or <see cref="P:Npgsql.NpgsqlParameter.Value"/>
            set, <see cref="P:Npgsql.NpgsqlParameter.DbType"/> will be set to this value.
            </param>
        </member>
        <member name="M:Npgsql.TypeMapping.TypeMappingAttribute.#ctor(System.String)">
            <summary>
            Read-only parameter
            </summary>
        </member>
        <member name="M:Npgsql.TypeMapping.TypeMappingAttribute.ToString">
            <summary>
            Returns a string that represents the current object.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Npgsql.Util.VersionExtensions.IsGreaterOrEqual(System.Version,System.Int32,System.Int32,System.Int32)">
            <summary>
            Allocation free helper function to find if version is greater than expected
            </summary>
        </member>
        <member name="T:Npgsql.VolatileResourceManager">
             <summary>
            
             </summary>
             <remarks>
             Note that a connection may be closed before its TransactionScope completes. In this case we close the NpgsqlConnection
             as usual but the connector in a special list in the pool; it will be closed only when the scope completes.
             </remarks>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlDate.Epoch">
            <summary>
            Represents the date 1970-01-01
            </summary>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlDate.Era">
            <summary>
            Represents the date 0001-01-01
            </summary>
        </member>
        <member name="T:NpgsqlTypes.NpgsqlDateTime">
            <summary>
            A struct similar to .NET DateTime but capable of storing PostgreSQL's timestamp and timestamptz types.
            DateTime is capable of storing values from year 1 to 9999 at 100-nanosecond precision,
            while PostgreSQL's timestamps store values from 4713BC to 5874897AD with 1-microsecond precision.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlDateTime.ToDateTime">
            <summary>
            Cast of an <see cref="T:NpgsqlTypes.NpgsqlDateTime"/> to a <see cref="T:System.DateTime"/>.
            </summary>
            <returns>An equivalent <see cref="T:System.DateTime"/>.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlDateTime.ToUniversalTime">
            <summary>
            Converts the value of the current <see cref="T:NpgsqlTypes.NpgsqlDateTime"/> object to Coordinated Universal Time (UTC).
            </summary>
            <remarks>
            See the MSDN documentation for DateTime.ToUniversalTime().
            <b>Note:</b> this method <b>only</b> takes into account the time zone's base offset, and does
            <b>not</b> respect daylight savings. See https://github.com/npgsql/npgsql/pull/684 for more
            details.
            </remarks>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlDateTime.ToLocalTime">
            <summary>
            Converts the value of the current <see cref="T:NpgsqlTypes.NpgsqlDateTime"/> object to local time.
            </summary>
            <remarks>
            See the MSDN documentation for DateTime.ToLocalTime().
            <b>Note:</b> this method <b>only</b> takes into account the time zone's base offset, and does
            <b>not</b> respect daylight savings. See https://github.com/npgsql/npgsql/pull/684 for more
            details.
            </remarks>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlDateTime.Add(NpgsqlTypes.NpgsqlTimeSpan)">
            <summary>
            Returns a new <see cref="T:NpgsqlTypes.NpgsqlDateTime"/> that adds the value of the specified TimeSpan to the value of this instance.
            </summary>
            <param name="value">A positive or negative time interval.</param>
            <returns>An object whose value is the sum of the date and time represented by this instance and the time interval represented by value.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlDateTime.Add(System.TimeSpan)">
            <summary>
            Returns a new <see cref="T:NpgsqlTypes.NpgsqlDateTime"/> that adds the value of the specified <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> to the value of this instance.
            </summary>
            <param name="value">A positive or negative time interval.</param>
            <returns>An object whose value is the sum of the date and time represented by this instance and the time interval represented by value.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlDateTime.AddYears(System.Int32)">
            <summary>
            Returns a new <see cref="T:NpgsqlTypes.NpgsqlDateTime"/> that adds the specified number of years to the value of this instance.
            </summary>
            <param name="value">A number of years. The value parameter can be negative or positive.</param>
            <returns>An object whose value is the sum of the date and time represented by this instance and the number of years represented by value.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlDateTime.AddMonths(System.Int32)">
            <summary>
            Returns a new <see cref="T:NpgsqlTypes.NpgsqlDateTime"/> that adds the specified number of months to the value of this instance.
            </summary>
            <param name="value">A number of months. The months parameter can be negative or positive.</param>
            <returns>An object whose value is the sum of the date and time represented by this instance and months.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlDateTime.AddDays(System.Double)">
            <summary>
            Returns a new <see cref="T:NpgsqlTypes.NpgsqlDateTime"/> that adds the specified number of days to the value of this instance.
            </summary>
            <param name="value">A number of whole and fractional days. The value parameter can be negative or positive.</param>
            <returns>An object whose value is the sum of the date and time represented by this instance and the number of days represented by value.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlDateTime.AddHours(System.Double)">
            <summary>
            Returns a new <see cref="T:NpgsqlTypes.NpgsqlDateTime"/> that adds the specified number of hours to the value of this instance.
            </summary>
            <param name="value">A number of whole and fractional hours. The value parameter can be negative or positive.</param>
            <returns>An object whose value is the sum of the date and time represented by this instance and the number of hours represented by value.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlDateTime.AddMinutes(System.Double)">
            <summary>
            Returns a new <see cref="T:NpgsqlTypes.NpgsqlDateTime"/> that adds the specified number of minutes to the value of this instance.
            </summary>
            <param name="value">A number of whole and fractional minutes. The value parameter can be negative or positive.</param>
            <returns>An object whose value is the sum of the date and time represented by this instance and the number of minutes represented by value.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlDateTime.AddSeconds(System.Double)">
            <summary>
            Returns a new <see cref="T:NpgsqlTypes.NpgsqlDateTime"/> that adds the specified number of minutes to the value of this instance.
            </summary>
            <param name="value">A number of whole and fractional minutes. The value parameter can be negative or positive.</param>
            <returns>An object whose value is the sum of the date and time represented by this instance and the number of minutes represented by value.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlDateTime.AddMilliseconds(System.Double)">
            <summary>
            Returns a new <see cref="T:NpgsqlTypes.NpgsqlDateTime"/> that adds the specified number of milliseconds to the value of this instance.
            </summary>
            <param name="value">A number of whole and fractional milliseconds. The value parameter can be negative or positive. Note that this value is rounded to the nearest integer.</param>
            <returns>An object whose value is the sum of the date and time represented by this instance and the number of milliseconds represented by value.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlDateTime.AddTicks(System.Int64)">
            <summary>
            Returns a new <see cref="T:NpgsqlTypes.NpgsqlDateTime"/> that adds the specified number of ticks to the value of this instance.
            </summary>
            <param name="value">A number of 100-nanosecond ticks. The value parameter can be positive or negative.</param>
            <returns>An object whose value is the sum of the date and time represented by this instance and the time represented by value.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlDateTime.op_Implicit(System.DateTime)~NpgsqlTypes.NpgsqlDateTime">
            <summary>
            Implicit cast of a <see cref="T:System.DateTime"/> to an <see cref="T:NpgsqlTypes.NpgsqlDateTime"/>
            </summary>
            <param name="dateTime">A <see cref="T:System.DateTime"/></param>
            <returns>An equivalent <see cref="T:NpgsqlTypes.NpgsqlDateTime"/>.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlDateTime.op_Explicit(NpgsqlTypes.NpgsqlDateTime)~System.DateTime">
            <summary>
            Explicit cast of an <see cref="T:NpgsqlTypes.NpgsqlDateTime"/> to a <see cref="T:System.DateTime"/>.
            </summary>
            <param name="npgsqlDateTime">An <see cref="T:NpgsqlTypes.NpgsqlDateTime"/>.</param>
            <returns>An equivalent <see cref="T:System.DateTime"/>.</returns>
        </member>
        <member name="T:NpgsqlTypes.NpgsqlDbType">
            <summary>
            Represents a PostgreSQL data type that can be written or read to the database.
            Used in places such as <see cref="P:Npgsql.NpgsqlParameter.NpgsqlDbType"/> to unambiguously specify
            how to encode or decode values.
            </summary>
            <remarks>See http://www.postgresql.org/docs/current/static/datatype.html</remarks>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlDbType.Bigint">
            <summary>
            Corresponds to the PostgreSQL 8-byte "bigint" type.
            </summary>
            <remarks>See http://www.postgresql.org/docs/current/static/datatype-numeric.html</remarks>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlDbType.Double">
            <summary>
            Corresponds to the PostgreSQL 8-byte floating-point "double" type.
            </summary>
            <remarks>See http://www.postgresql.org/docs/current/static/datatype-numeric.html</remarks>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlDbType.Integer">
            <summary>
            Corresponds to the PostgreSQL 4-byte "integer" type.
            </summary>
            <remarks>See http://www.postgresql.org/docs/current/static/datatype-numeric.html</remarks>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlDbType.Numeric">
            <summary>
            Corresponds to the PostgreSQL arbitrary-precision "numeric" type.
            </summary>
            <remarks>See http://www.postgresql.org/docs/current/static/datatype-numeric.html</remarks>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlDbType.Real">
            <summary>
            Corresponds to the PostgreSQL floating-point "real" type.
            </summary>
            <remarks>See http://www.postgresql.org/docs/current/static/datatype-numeric.html</remarks>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlDbType.Smallint">
            <summary>
            Corresponds to the PostgreSQL 2-byte "smallint" type.
            </summary>
            <remarks>See http://www.postgresql.org/docs/current/static/datatype-numeric.html</remarks>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlDbType.Money">
            <summary>
            Corresponds to the PostgreSQL "money" type.
            </summary>
            <remarks>See http://www.postgresql.org/docs/current/static/datatype-money.html</remarks>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlDbType.Boolean">
            <summary>
            Corresponds to the PostgreSQL "boolean" type.
            </summary>
            <remarks>See http://www.postgresql.org/docs/current/static/datatype-boolean.html</remarks>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlDbType.Box">
            <summary>
            Corresponds to the PostgreSQL geometric "box" type.
            </summary>
            <remarks>See http://www.postgresql.org/docs/current/static/datatype-geometric.html</remarks>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlDbType.Circle">
            <summary>
            Corresponds to the PostgreSQL geometric "circle" type.
            </summary>
            <remarks>See http://www.postgresql.org/docs/current/static/datatype-geometric.html</remarks>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlDbType.Line">
            <summary>
            Corresponds to the PostgreSQL geometric "line" type.
            </summary>
            <remarks>See http://www.postgresql.org/docs/current/static/datatype-geometric.html</remarks>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlDbType.LSeg">
            <summary>
            Corresponds to the PostgreSQL geometric "lseg" type.
            </summary>
            <remarks>See http://www.postgresql.org/docs/current/static/datatype-geometric.html</remarks>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlDbType.Path">
            <summary>
            Corresponds to the PostgreSQL geometric "path" type.
            </summary>
            <remarks>See http://www.postgresql.org/docs/current/static/datatype-geometric.html</remarks>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlDbType.Point">
            <summary>
            Corresponds to the PostgreSQL geometric "point" type.
            </summary>
            <remarks>See http://www.postgresql.org/docs/current/static/datatype-geometric.html</remarks>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlDbType.Polygon">
            <summary>
            Corresponds to the PostgreSQL geometric "polygon" type.
            </summary>
            <remarks>See http://www.postgresql.org/docs/current/static/datatype-geometric.html</remarks>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlDbType.Char">
            <summary>
            Corresponds to the PostgreSQL "char(n)" type.
            </summary>
            <remarks>See http://www.postgresql.org/docs/current/static/datatype-character.html</remarks>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlDbType.Text">
            <summary>
            Corresponds to the PostgreSQL "text" type.
            </summary>
            <remarks>See http://www.postgresql.org/docs/current/static/datatype-character.html</remarks>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlDbType.Varchar">
            <summary>
            Corresponds to the PostgreSQL "varchar" type.
            </summary>
            <remarks>See http://www.postgresql.org/docs/current/static/datatype-character.html</remarks>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlDbType.Name">
            <summary>
            Corresponds to the PostgreSQL internal "name" type.
            </summary>
            <remarks>See http://www.postgresql.org/docs/current/static/datatype-character.html</remarks>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlDbType.Citext">
            <summary>
            Corresponds to the PostgreSQL "citext" type for the citext module.
            </summary>
            <remarks>See http://www.postgresql.org/docs/current/static/citext.html</remarks>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlDbType.InternalChar">
             <summary>
             Corresponds to the PostgreSQL "char" type.
             </summary>
             <remarks>
             This is an internal field and should normally not be used for regular applications.
            
             See http://www.postgresql.org/docs/current/static/datatype-text.html
             </remarks>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlDbType.Bytea">
            <summary>
            Corresponds to the PostgreSQL "bytea" type, holding a raw byte string.
            </summary>
            <remarks>See http://www.postgresql.org/docs/current/static/datatype-binary.html</remarks>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlDbType.Date">
            <summary>
            Corresponds to the PostgreSQL "date" type.
            </summary>
            <remarks>See http://www.postgresql.org/docs/current/static/datatype-datetime.html</remarks>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlDbType.Time">
            <summary>
            Corresponds to the PostgreSQL "time" type.
            </summary>
            <remarks>See http://www.postgresql.org/docs/current/static/datatype-datetime.html</remarks>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlDbType.Timestamp">
            <summary>
            Corresponds to the PostgreSQL "timestamp" type.
            </summary>
            <remarks>See http://www.postgresql.org/docs/current/static/datatype-datetime.html</remarks>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlDbType.TimestampTZ">
            <summary>
            Corresponds to the PostgreSQL "timestamp with time zone" type.
            </summary>
            <remarks>See http://www.postgresql.org/docs/current/static/datatype-datetime.html</remarks>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlDbType.TimestampTz">
            <summary>
            Corresponds to the PostgreSQL "timestamp with time zone" type.
            </summary>
            <remarks>See http://www.postgresql.org/docs/current/static/datatype-datetime.html</remarks>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlDbType.Interval">
            <summary>
            Corresponds to the PostgreSQL "interval" type.
            </summary>
            <remarks>See http://www.postgresql.org/docs/current/static/datatype-datetime.html</remarks>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlDbType.TimeTZ">
            <summary>
            Corresponds to the PostgreSQL "time with time zone" type.
            </summary>
            <remarks>See http://www.postgresql.org/docs/current/static/datatype-datetime.html</remarks>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlDbType.TimeTz">
            <summary>
            Corresponds to the PostgreSQL "time with time zone" type.
            </summary>
            <remarks>See http://www.postgresql.org/docs/current/static/datatype-datetime.html</remarks>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlDbType.Abstime">
            <summary>
            Corresponds to the obsolete PostgreSQL "abstime" type.
            </summary>
            <remarks>See http://www.postgresql.org/docs/current/static/datatype-datetime.html</remarks>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlDbType.Inet">
            <summary>
            Corresponds to the PostgreSQL "inet" type.
            </summary>
            <remarks>See http://www.postgresql.org/docs/current/static/datatype-net-types.html</remarks>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlDbType.Cidr">
            <summary>
            Corresponds to the PostgreSQL "cidr" type, a field storing an IPv4 or IPv6 network.
            </summary>
            <remarks>See http://www.postgresql.org/docs/current/static/datatype-net-types.html</remarks>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlDbType.MacAddr">
            <summary>
            Corresponds to the PostgreSQL "macaddr" type, a field storing a 6-byte physical address.
            </summary>
            <remarks>See http://www.postgresql.org/docs/current/static/datatype-net-types.html</remarks>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlDbType.MacAddr8">
            <summary>
            Corresponds to the PostgreSQL "macaddr8" type, a field storing a 6-byte or 8-byte physical address.
            </summary>
            <remarks>See http://www.postgresql.org/docs/current/static/datatype-net-types.html</remarks>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlDbType.Bit">
            <summary>
            Corresponds to the PostgreSQL "bit" type.
            </summary>
            <remarks>See http://www.postgresql.org/docs/current/static/datatype-bit.html</remarks>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlDbType.Varbit">
            <summary>
            Corresponds to the PostgreSQL "varbit" type, a field storing a variable-length string of bits.
            </summary>
            <remarks>See http://www.postgresql.org/docs/current/static/datatype-boolean.html</remarks>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlDbType.TsVector">
            <summary>
            Corresponds to the PostgreSQL "tsvector" type.
            </summary>
            <remarks>See http://www.postgresql.org/docs/current/static/datatype-textsearch.html</remarks>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlDbType.TsQuery">
            <summary>
            Corresponds to the PostgreSQL "tsquery" type.
            </summary>
            <remarks>See http://www.postgresql.org/docs/current/static/datatype-textsearch.html</remarks>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlDbType.Regconfig">
            <summary>
            Corresponds to the PostgreSQL "tsquery" type.
            </summary>
            <remarks>See http://www.postgresql.org/docs/current/static/datatype-textsearch.html</remarks>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlDbType.Uuid">
            <summary>
            Corresponds to the PostgreSQL "uuid" type.
            </summary>
            <remarks>See http://www.postgresql.org/docs/current/static/datatype-uuid.html</remarks>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlDbType.Xml">
            <summary>
            Corresponds to the PostgreSQL "xml" type.
            </summary>
            <remarks>See http://www.postgresql.org/docs/current/static/datatype-xml.html</remarks>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlDbType.Json">
            <summary>
            Corresponds to the PostgreSQL "json" type, a field storing JSON in text format.
            </summary>
            <remarks>See http://www.postgresql.org/docs/current/static/datatype-json.html</remarks>
            <seealso cref="F:NpgsqlTypes.NpgsqlDbType.Jsonb"/>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlDbType.Jsonb">
            <summary>
            Corresponds to the PostgreSQL "jsonb" type, a field storing JSON in an optimized binary
            format.
            </summary>
            <remarks>
            Supported since PostgreSQL 9.4.
            See http://www.postgresql.org/docs/current/static/datatype-json.html
            </remarks>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlDbType.Hstore">
            <summary>
            Corresponds to the PostgreSQL "hstore" type, a dictionary of string key-value pairs.
            </summary>
            <remarks>See http://www.postgresql.org/docs/current/static/hstore.html</remarks>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlDbType.Array">
            <summary>
            Corresponds to the PostgreSQL "array" type, a variable-length multidimensional array of
            another type. This value must be combined with another value from <see cref="T:NpgsqlTypes.NpgsqlDbType"/>
            via a bit OR (e.g. NpgsqlDbType.Array | NpgsqlDbType.Integer)
            </summary>
            <remarks>See http://www.postgresql.org/docs/current/static/arrays.html</remarks>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlDbType.Range">
            <summary>
            Corresponds to the PostgreSQL "range" type, continuous range of values of specific type.
            This value must be combined with another value from <see cref="T:NpgsqlTypes.NpgsqlDbType"/>
            via a bit OR (e.g. NpgsqlDbType.Range | NpgsqlDbType.Integer)
            </summary>
            <remarks>
            Supported since PostgreSQL 9.2.
            See http://www.postgresql.org/docs/9.2/static/rangetypes.html
            </remarks>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlDbType.Refcursor">
            <summary>
            Corresponds to the PostgreSQL "refcursor" type.
            </summary>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlDbType.Oidvector">
            <summary>
            Corresponds to the PostgreSQL internal "oidvector" type.
            </summary>
            <remarks>See http://www.postgresql.org/docs/current/static/datatype-oid.html</remarks>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlDbType.Int2Vector">
            <summary>
            Corresponds to the PostgreSQL internal "int2vector" type.
            </summary>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlDbType.Oid">
            <summary>
            Corresponds to the PostgreSQL "oid" type.
            </summary>
            <remarks>See http://www.postgresql.org/docs/current/static/datatype-oid.html</remarks>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlDbType.Xid">
            <summary>
            Corresponds to the PostgreSQL "xid" type, an internal transaction identifier.
            </summary>
            <remarks>See http://www.postgresql.org/docs/current/static/datatype-oid.html</remarks>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlDbType.Cid">
            <summary>
            Corresponds to the PostgreSQL "cid" type, an internal command identifier.
            </summary>
            <remarks>See http://www.postgresql.org/docs/current/static/datatype-oid.html</remarks>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlDbType.Regtype">
            <summary>
            Corresponds to the PostgreSQL "regtype" type, a numeric (OID) ID of a type in the pg_type table.
            </summary>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlDbType.Tid">
            <summary>
            Corresponds to the PostgreSQL "tid" type, a tuple id identifying the physical location of a row within its table.
            </summary>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlDbType.Unknown">
            <summary>
            A special value that can be used to send parameter values to the database without
            specifying their type, allowing the database to cast them to another value based on context.
            The value will be converted to a string and send as text.
            </summary>
            <remarks>
            This value shouldn't ordinarily be used, and makes sense only when sending a data type
            unsupported by Npgsql.
            </remarks>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlDbType.Geometry">
            <summary>
            The geometry type for PostgreSQL spatial extension PostGIS.
            </summary>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlDbType.Geography">
            <summary>
            The geography (geodetic) type for PostgreSQL spatial extension PostGIS.
            </summary>
        </member>
        <member name="T:NpgsqlTypes.BuiltInPostgresType">
            <summary>
            Represents a built-in PostgreSQL type as it appears in pg_type, including its name and OID.
            Extension types with variable OIDs are not represented.
            </summary>
        </member>
        <member name="T:NpgsqlTypes.NpgsqlRange`1">
            <summary>
            Represents a PostgreSQL range type.
            </summary>
            <typeparam name="T">The element type of the values in the range.</typeparam>
            <remarks>
            See: https://www.postgresql.org/docs/current/static/rangetypes.html
            </remarks>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlRange`1.EmptyLiteral">
            <summary>
            Defined by PostgreSQL to represent an empty range.
            </summary>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlRange`1.LowerInfinityLiteral">
            <summary>
            Defined by PostgreSQL to represent an infinite lower bound.
            Some element types may have specific handling for this value distinct from a missing or null value.
            </summary>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlRange`1.UpperInfinityLiteral">
            <summary>
            Defined by PostgreSQL to represent an infinite upper bound.
            Some element types may have specific handling for this value distinct from a missing or null value.
            </summary>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlRange`1.NullLiteral">
            <summary>
            Defined by PostgreSQL to represent an null bound.
            Some element types may have specific handling for this value distinct from an infinite or missing value.
            </summary>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlRange`1.LowerInclusiveBound">
            <summary>
            Defined by PostgreSQL to represent a lower inclusive bound.
            </summary>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlRange`1.LowerExclusiveBound">
            <summary>
            Defined by PostgreSQL to represent a lower exclusive bound.
            </summary>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlRange`1.UpperInclusiveBound">
            <summary>
            Defined by PostgreSQL to represent an upper inclusive bound.
            </summary>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlRange`1.UpperExclusiveBound">
            <summary>
            Defined by PostgreSQL to represent an upper exclusive bound.
            </summary>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlRange`1.BoundSeparator">
            <summary>
            Defined by PostgreSQL to separate the values for the upper and lower bounds.
            </summary>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlRange`1.BoundConverter">
            <summary>
            The <see cref="T:System.ComponentModel.TypeConverter"/> used by <see cref="M:NpgsqlTypes.NpgsqlRange`1.Parse(System.String)"/> to convert <see cref="T:System.String"/> bounds into <typeparamref name="T"/>.
            </summary>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlRange`1.HasEquatableBounds">
            <summary>
            True if <typeparamref name="T"/> implements <see cref="T:System.IEquatable`1"/>; otherwise, false.
            </summary>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlRange`1.Empty">
            <summary>
            Represents the empty range. This field is read-only.
            </summary>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlRange`1.LowerBound">
            <summary>
            The lower bound of the range. Only valid when <see cref="P:NpgsqlTypes.NpgsqlRange`1.LowerBoundInfinite"/> is false.
            </summary>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlRange`1.UpperBound">
            <summary>
            The upper bound of the range. Only valid when <see cref="P:NpgsqlTypes.NpgsqlRange`1.UpperBoundInfinite"/> is false.
            </summary>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlRange`1.Flags">
            <summary>
            The characteristics of the boundaries.
            </summary>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlRange`1.LowerBoundIsInclusive">
            <summary>
            True if the lower bound is part of the range (i.e. inclusive); otherwise, false.
            </summary>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlRange`1.UpperBoundIsInclusive">
            <summary>
            True if the upper bound is part of the range (i.e. inclusive); otherwise, false.
            </summary>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlRange`1.LowerBoundInfinite">
            <summary>
            True if the lower bound is indefinite (i.e. infinite or unbounded); otherwise, false.
            </summary>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlRange`1.UpperBoundInfinite">
            <summary>
            True if the upper bound is indefinite (i.e. infinite or unbounded); otherwise, false.
            </summary>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlRange`1.IsEmpty">
            <summary>
            True if the range is empty; otherwise, false.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlRange`1.#ctor(`0,`0)">
            <summary>
            Constructs an <see cref="T:NpgsqlTypes.NpgsqlRange`1"/> with inclusive and definite bounds.
            </summary>
            <param name="lowerBound">The lower bound of the range.</param>
            <param name="upperBound">The upper bound of the range.</param>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlRange`1.#ctor(`0,System.Boolean,`0,System.Boolean)">
            <summary>
            Constructs an <see cref="T:NpgsqlTypes.NpgsqlRange`1"/> with definite bounds.
            </summary>
            <param name="lowerBound">The lower bound of the range.</param>
            <param name="lowerBoundIsInclusive">True if the lower bound is is part of the range (i.e. inclusive); otherwise, false.</param>
            <param name="upperBound">The upper bound of the range.</param>
            <param name="upperBoundIsInclusive">True if the upper bound is part of the range (i.e. inclusive); otherwise, false.</param>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlRange`1.#ctor(`0,System.Boolean,System.Boolean,`0,System.Boolean,System.Boolean)">
            <summary>
            Constructs an <see cref="T:NpgsqlTypes.NpgsqlRange`1"/>.
            </summary>
            <param name="lowerBound">The lower bound of the range.</param>
            <param name="lowerBoundIsInclusive">True if the lower bound is is part of the range (i.e. inclusive); otherwise, false.</param>
            <param name="lowerBoundInfinite">True if the lower bound is indefinite (i.e. infinite or unbounded); otherwise, false.</param>
            <param name="upperBound">The upper bound of the range.</param>
            <param name="upperBoundIsInclusive">True if the upper bound is part of the range (i.e. inclusive); otherwise, false.</param>
            <param name="upperBoundInfinite">True if the upper bound is indefinite (i.e. infinite or unbounded); otherwise, false.</param>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlRange`1.#ctor(`0,`0,NpgsqlTypes.RangeFlags)">
            <summary>
            Constructs an <see cref="T:NpgsqlTypes.NpgsqlRange`1"/>.
            </summary>
            <param name="lowerBound">The lower bound of the range.</param>
            <param name="upperBound">The upper bound of the range.</param>
            <param name="flags">The characteristics of the range boundaries.</param>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlRange`1.IsEmptyRange(`0,`0,NpgsqlTypes.RangeFlags)">
            <summary>
            Attempts to determine if the range is malformed or implicitly empty.
            </summary>
            <param name="lowerBound">The lower bound of the range.</param>
            <param name="upperBound">The upper bound of the range.</param>
            <param name="flags">The characteristics of the range boundaries.</param>
            <returns>
            True if the range is implicitly empty; otherwise, false.
            </returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlRange`1.EvaluateBoundaryFlags(System.Boolean,System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            Evaluates the boundary flags.
            </summary>
            <param name="lowerBoundIsInclusive">True if the lower bound is is part of the range (i.e. inclusive); otherwise, false.</param>
            <param name="lowerBoundInfinite">True if the lower bound is indefinite (i.e. infinite or unbounded); otherwise, false.</param>
            <param name="upperBoundIsInclusive">True if the upper bound is part of the range (i.e. inclusive); otherwise, false.</param>
            <param name="upperBoundInfinite">True if the upper bound is indefinite (i.e. infinite or unbounded); otherwise, false.</param>
            <returns>
            The boundary characteristics.
            </returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlRange`1.op_Equality(NpgsqlTypes.NpgsqlRange{`0},NpgsqlTypes.NpgsqlRange{`0})">
            <summary>
            Indicates whether the <see cref="T:NpgsqlTypes.NpgsqlRange`1"/> on the left is equal to the <see cref="T:NpgsqlTypes.NpgsqlRange`1"/> on the right.
            </summary>
            <param name="x">The <see cref="T:NpgsqlTypes.NpgsqlRange`1"/> on the left.</param>
            <param name="y">The <see cref="T:NpgsqlTypes.NpgsqlRange`1"/> on the right.</param>
            <returns>
            True if the <see cref="T:NpgsqlTypes.NpgsqlRange`1"/> on the left is equal to the <see cref="T:NpgsqlTypes.NpgsqlRange`1"/> on the right; otherwise, false.
            </returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlRange`1.op_Inequality(NpgsqlTypes.NpgsqlRange{`0},NpgsqlTypes.NpgsqlRange{`0})">
            <summary>
            Indicates whether the <see cref="T:NpgsqlTypes.NpgsqlRange`1"/> on the left is not equal to the <see cref="T:NpgsqlTypes.NpgsqlRange`1"/> on the right.
            </summary>
            <param name="x">The <see cref="T:NpgsqlTypes.NpgsqlRange`1"/> on the left.</param>
            <param name="y">The <see cref="T:NpgsqlTypes.NpgsqlRange`1"/> on the right.</param>
            <returns>
            True if the <see cref="T:NpgsqlTypes.NpgsqlRange`1"/> on the left is not equal to the <see cref="T:NpgsqlTypes.NpgsqlRange`1"/> on the right; otherwise, false.
            </returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlRange`1.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:NpgsqlTypes.NpgsqlRange`1.Equals(NpgsqlTypes.NpgsqlRange{`0})">
            <inheritdoc />
        </member>
        <member name="M:NpgsqlTypes.NpgsqlRange`1.GetHashCode">
            <inheritdoc />
        </member>
        <member name="M:NpgsqlTypes.NpgsqlRange`1.ToString">
            <inheritdoc />
        </member>
        <member name="M:NpgsqlTypes.NpgsqlRange`1.Parse(System.String)">
            <summary>
            Parses the well-known text representation of a PostgreSQL range type into a <see cref="T:NpgsqlTypes.NpgsqlRange`1"/>.
            </summary>
            <param name="value">A PosgreSQL range type in a well-known text format.</param>
            <returns>
            The <see cref="T:NpgsqlTypes.NpgsqlRange`1"/> represented by the <paramref name="value"/>.
            </returns>
            <exception cref="T:System.FormatException">
            Malformed range literal.
            </exception>
            <exception cref="T:System.FormatException">
            Malformed range literal. Missing left parenthesis or bracket.
            </exception>
            <exception cref="T:System.FormatException">
            Malformed range literal. Missing right parenthesis or bracket.
            </exception>
            <exception cref="T:System.FormatException">
            Malformed range literal. Missing comma after lower bound.
            </exception>
            <remarks>
            See: https://www.postgresql.org/docs/current/static/rangetypes.html
            </remarks>
        </member>
        <member name="T:NpgsqlTypes.NpgsqlRange`1.RangeTypeConverter">
            <inheritdoc />
            <summary>
            Represents a type converter for <see cref="T:NpgsqlTypes.NpgsqlRange`1" />.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlRange`1.RangeTypeConverter.Register">
            <summary>
            Adds a <see cref="T:System.ComponentModel.TypeConverterAttribute"/> to the closed form <see cref="T:NpgsqlTypes.NpgsqlRange`1"/>.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlRange`1.RangeTypeConverter.CanConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Type)">
            <inheritdoc />
        </member>
        <member name="M:NpgsqlTypes.NpgsqlRange`1.RangeTypeConverter.CanConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Type)">
            <inheritdoc />
        </member>
        <member name="M:NpgsqlTypes.NpgsqlRange`1.RangeTypeConverter.ConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object)">
            <inheritdoc />
        </member>
        <member name="M:NpgsqlTypes.NpgsqlRange`1.RangeTypeConverter.ConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object,System.Type)">
            <inheritdoc />
        </member>
        <member name="T:NpgsqlTypes.RangeFlags">
            <summary>
            Represents characteristics of range type boundaries.
            </summary>
            <remarks>
            See: https://www.postgresql.org/docs/current/static/rangetypes.html
            </remarks>
        </member>
        <member name="F:NpgsqlTypes.RangeFlags.None">
            <summary>
            The default flag. The range is not empty and has boundaries that are definite and exclusive.
            </summary>
        </member>
        <member name="F:NpgsqlTypes.RangeFlags.Empty">
            <summary>
            The range is empty. E.g. '(0,0)', 'empty'.
            </summary>
        </member>
        <member name="F:NpgsqlTypes.RangeFlags.LowerBoundInclusive">
            <summary>
            The lower bound is inclusive. E.g. '[0,5]', '[0,5)', '[0,)'.
            </summary>
        </member>
        <member name="F:NpgsqlTypes.RangeFlags.UpperBoundInclusive">
            <summary>
            The upper bound is inclusive. E.g. '[0,5]', '(0,5]', '(,5]'.
            </summary>
        </member>
        <member name="F:NpgsqlTypes.RangeFlags.LowerBoundInfinite">
            <summary>
            The lower bound is infinite or indefinite. E.g. '(null,5]', '(-infinity,5]', '(,5]'.
            </summary>
        </member>
        <member name="F:NpgsqlTypes.RangeFlags.UpperBoundInfinite">
            <summary>
            The upper bound is infinite or indefinite. E.g. '[0,null)', '[0,infinity)', '[0,)'.
            </summary>
        </member>
        <member name="F:NpgsqlTypes.RangeFlags.Inclusive">
            <summary>
            Both the lower and upper bounds are inclusive.
            </summary>
        </member>
        <member name="F:NpgsqlTypes.RangeFlags.Infinite">
            <summary>
            Both the lower and upper bounds are indefinite.
            </summary>
        </member>
        <member name="F:NpgsqlTypes.RangeFlags.LowerInclusiveInfinite">
            <summary>
            The lower bound is both inclusive and indefinite. This represents an error condition.
            </summary>
        </member>
        <member name="F:NpgsqlTypes.RangeFlags.UpperInclusiveInfinite">
            <summary>
            The upper bound is both inclusive and indefinite. This represents an error condition.
            </summary>
        </member>
        <member name="T:NpgsqlTypes.NpgsqlTimeSpan">
            <summary>
            Represents the PostgreSQL interval datatype.
            </summary>
            <remarks>PostgreSQL differs from .NET in how it's interval type doesn't assume 24 hours in a day
            (to deal with 23- and 25-hour days caused by daylight savings adjustments) and has a concept
            of months that doesn't exist in .NET's <see cref="T:System.TimeSpan"/> class. (Neither datatype
            has any concessions for leap-seconds).
            <para>For most uses just casting to and from TimeSpan will work correctly &#x2014; in particular,
            the results of subtracting one <see cref="T:System.DateTime"/> or the PostgreSQL date, time and
            timestamp types from another should be the same whether you do so in .NET or PostgreSQL &#x2014;
            but if the handling of days and months in PostgreSQL is important to your application then you
            should use this class instead of <see cref="T:System.TimeSpan"/>.</para>
            <para>If you don't know whether these differences are important to your application, they
            probably arent! Just use <see cref="T:System.TimeSpan"/> and do not use this class directly &#x263a;</para>
            <para>To avoid forcing unnecessary provider-specific concerns on users who need not be concerned
            with them a call to <see cref="M:System.Data.IDataRecord.GetValue(System.Int32)"/> on a field containing an
            <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> value will return a <see cref="T:System.TimeSpan"/> rather than an
            <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/>. If you need the extra functionality of <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/>
            then use <see cref="M:Npgsql.NpgsqlDataReader.GetInterval(System.Int32)"/>.</para>
            </remarks>
            <seealso cref="P:NpgsqlTypes.NpgsqlTimeSpan.Ticks"/>
            <seealso cref="M:NpgsqlTypes.NpgsqlTimeSpan.JustifyDays"/>
            <seealso cref="M:NpgsqlTypes.NpgsqlTimeSpan.JustifyMonths"/>
            <seealso cref="M:NpgsqlTypes.NpgsqlTimeSpan.Canonicalize"/>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlTimeSpan.TicksPerMicrosecond">
            <summary>
            Represents the number of ticks (100ns periods) in one microsecond. This field is constant.
            </summary>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlTimeSpan.TicksPerMillsecond">
            <summary>
            Represents the number of ticks (100ns periods) in one millisecond. This field is constant.
            </summary>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlTimeSpan.TicksPerSecond">
            <summary>
            Represents the number of ticks (100ns periods) in one second. This field is constant.
            </summary>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlTimeSpan.TicksPerMinute">
            <summary>
            Represents the number of ticks (100ns periods) in one minute. This field is constant.
            </summary>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlTimeSpan.TicksPerHour">
            <summary>
            Represents the number of ticks (100ns periods) in one hour. This field is constant.
            </summary>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlTimeSpan.TicksPerDay">
            <summary>
            Represents the number of ticks (100ns periods) in one day. This field is constant.
            </summary>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlTimeSpan.HoursPerDay">
            <summary>
            Represents the number of hours in one day (assuming no daylight savings adjustments). This field is constant.
            </summary>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlTimeSpan.DaysPerMonth">
            <summary>
            Represents the number of days assumed in one month if month justification or unjustifcation is performed.
            This is set to 30 for consistency with PostgreSQL. Note that this is means that month adjustments cause
            a year to be taken as 30 &#xd7; 12 = 360 rather than 356/366 days.
            </summary>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlTimeSpan.TicksPerMonth">
            <summary>
            Represents the number of ticks (100ns periods) in one day, assuming 30 days per month. <seealso cref="F:NpgsqlTypes.NpgsqlTimeSpan.DaysPerMonth"/>
            </summary>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlTimeSpan.MonthsPerYear">
            <summary>
            Represents the number of months in a year. This field is constant.
            </summary>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlTimeSpan.MaxValue">
            <summary>
            Represents the maximum <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/>. This field is read-only.
            </summary>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlTimeSpan.MinValue">
            <summary>
            Represents the minimum <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/>. This field is read-only.
            </summary>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlTimeSpan.Zero">
            <summary>
            Represents the zero <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/>. This field is read-only.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTimeSpan.#ctor(System.Int64)">
            <summary>
            Initializes a new <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> to the specified number of ticks.
            </summary>
            <param name="ticks">A time period expressed in 100ns units.</param>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTimeSpan.#ctor(System.TimeSpan)">
            <summary>
            Initializes a new <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> to hold the same time as a <see cref="T:System.TimeSpan"/>
            </summary>
            <param name="timespan">A time period expressed in a <see cref="T:System.TimeSpan"/></param>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTimeSpan.#ctor(System.Int32,System.Int32,System.Int64)">
            <summary>
            Initializes a new <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> to the specified number of months, days
            &amp; ticks.
            </summary>
            <param name="months">Number of months.</param>
            <param name="days">Number of days.</param>
            <param name="ticks">Number of 100ns units.</param>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTimeSpan.#ctor(System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            Initializes a new <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> to the specified number of
            days, hours, minutes &amp; seconds.
            </summary>
            <param name="days">Number of days.</param>
            <param name="hours">Number of hours.</param>
            <param name="minutes">Number of minutes.</param>
            <param name="seconds">Number of seconds.</param>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTimeSpan.#ctor(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            Initializes a new <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> to the specified number of
            days, hours, minutes, seconds &amp; milliseconds.
            </summary>
            <param name="days">Number of days.</param>
            <param name="hours">Number of hours.</param>
            <param name="minutes">Number of minutes.</param>
            <param name="seconds">Number of seconds.</param>
            <param name="milliseconds">Number of milliseconds.</param>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTimeSpan.#ctor(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            Initializes a new <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> to the specified number of
            months, days, hours, minutes, seconds &amp; milliseconds.
            </summary>
            <param name="months">Number of months.</param>
            <param name="days">Number of days.</param>
            <param name="hours">Number of hours.</param>
            <param name="minutes">Number of minutes.</param>
            <param name="seconds">Number of seconds.</param>
            <param name="milliseconds">Number of milliseconds.</param>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTimeSpan.#ctor(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            Initializes a new <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> to the specified number of
            years, months, days, hours, minutes, seconds &amp; milliseconds.
            <para>Years are calculated exactly equivalent to 12 months.</para>
            </summary>
            <param name="years">Number of years.</param>
            <param name="months">Number of months.</param>
            <param name="days">Number of days.</param>
            <param name="hours">Number of hours.</param>
            <param name="minutes">Number of minutes.</param>
            <param name="seconds">Number of seconds.</param>
            <param name="milliseconds">Number of milliseconds.</param>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlTimeSpan.Ticks">
            <summary>
            The total number of ticks(100ns units) contained. This is the resolution of the
            <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/>  type. This ignores the number of days and
            months held. If you want them included use <see cref="M:NpgsqlTypes.NpgsqlTimeSpan.UnjustifyInterval"/> first.
            <remarks>The resolution of the PostgreSQL
            interval type is by default 1&#xb5;s = 1,000 ns. It may be smaller as follows:
            <list type="number">
            <item>
            <term>interval(0)</term>
            <description>resolution of 1s (1 second)</description>
            </item>
            <item>
            <term>interval(1)</term>
            <description>resolution of 100ms = 0.1s (100 milliseconds)</description>
            </item>
            <item>
            <term>interval(2)</term>
            <description>resolution of 10ms = 0.01s (10 milliseconds)</description>
            </item>
            <item>
            <term>interval(3)</term>
            <description>resolution of 1ms = 0.001s (1 millisecond)</description>
            </item>
            <item>
            <term>interval(4)</term>
            <description>resolution of 100&#xb5;s = 0.0001s (100 microseconds)</description>
            </item>
            <item>
            <term>interval(5)</term>
            <description>resolution of 10&#xb5;s = 0.00001s (10 microseconds)</description>
            </item>
            <item>
            <term>interval(6) or interval</term>
            <description>resolution of 1&#xb5;s = 0.000001s (1 microsecond)</description>
            </item>
            </list>
            <para>As such, if the 100-nanosecond resolution is significant to an application, a PostgreSQL interval will
            not suffice for those purposes.</para>
            <para>In more frequent cases though, the resolution of the interval suffices.
            <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> will always suffice to handle the resolution of any interval value, and upon
            writing to the database, will be rounded to the resolution used.</para>
            </remarks>
            <returns>The number of ticks in the instance.</returns>
            </summary>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlTimeSpan.Microseconds">
            <summary>
            Gets the number of whole microseconds held in the instance.
            <returns>An  in the range [-999999, 999999].</returns>
            </summary>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlTimeSpan.Milliseconds">
            <summary>
            Gets the number of whole milliseconds held in the instance.
            <returns>An  in the range [-999, 999].</returns>
            </summary>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlTimeSpan.Seconds">
            <summary>
            Gets the number of whole seconds held in the instance.
            <returns>An  in the range [-59, 59].</returns>
            </summary>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlTimeSpan.Minutes">
            <summary>
            Gets the number of whole minutes held in the instance.
            <returns>An  in the range [-59, 59].</returns>
            </summary>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlTimeSpan.Hours">
            <summary>
            Gets the number of whole hours held in the instance.
            <remarks>Note that this can be less than -23 or greater than 23 unless <see cref="M:NpgsqlTypes.NpgsqlTimeSpan.JustifyDays"/>
            has been used to produce this instance.</remarks>
            </summary>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlTimeSpan.Days">
            <summary>
            Gets the number of days held in the instance.
            <remarks>Note that this does not pay attention to a time component with -24 or less hours or
            24 or more hours, unless <see cref="M:NpgsqlTypes.NpgsqlTimeSpan.JustifyDays"/> has been called to produce this instance.</remarks>
            </summary>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlTimeSpan.Months">
            <summary>
            Gets the number of months held in the instance.
            <remarks>Note that this does not pay attention to a day component with -30 or less days or
            30 or more days, unless <see cref="M:NpgsqlTypes.NpgsqlTimeSpan.JustifyMonths"/> has been called to produce this instance.</remarks>
            </summary>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlTimeSpan.Time">
            <summary>
            Returns a <see cref="T:System.TimeSpan"/> representing the time component of the instance.
            <remarks>Note that this may have a value beyond the range &#xb1;23:59:59.9999999 unless
            <see cref="M:NpgsqlTypes.NpgsqlTimeSpan.JustifyDays"/> has been called to produce this instance.</remarks>
            </summary>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlTimeSpan.TotalTicks">
            <summary>
            The total number of ticks (100ns units) in the instance, assuming 24 hours in each day and
            30 days in a month.
            </summary>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlTimeSpan.TotalMicroseconds">
            <summary>
            The total number of microseconds in the instance, assuming 24 hours in each day and
            30 days in a month.
            </summary>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlTimeSpan.TotalMilliseconds">
            <summary>
            The total number of milliseconds in the instance, assuming 24 hours in each day and
            30 days in a month.
            </summary>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlTimeSpan.TotalSeconds">
            <summary>
            The total number of seconds in the instance, assuming 24 hours in each day and
            30 days in a month.
            </summary>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlTimeSpan.TotalMinutes">
            <summary>
            The total number of minutes in the instance, assuming 24 hours in each day and
            30 days in a month.
            </summary>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlTimeSpan.TotalHours">
            <summary>
            The total number of hours in the instance, assuming 24 hours in each day and
            30 days in a month.
            </summary>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlTimeSpan.TotalDays">
            <summary>
            The total number of days in the instance, assuming 24 hours in each day and
            30 days in a month.
            </summary>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlTimeSpan.TotalMonths">
            <summary>
            The total number of months in the instance, assuming 24 hours in each day and
            30 days in a month.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTimeSpan.FromTicks(System.Int64)">
            <summary>
            Creates an <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> from a number of ticks.
            </summary>
            <param name="ticks">The number of ticks (100ns units) in the interval.</param>
            <returns>A <see cref="M:NpgsqlTypes.NpgsqlTimeSpan.Canonicalize"/>d <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> with the given number of ticks.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTimeSpan.FromMicroseconds(System.Double)">
            <summary>
            Creates an <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> from a number of microseconds.
            </summary>
            <param name="micro">The number of microseconds in the interval.</param>
            <returns>A <see cref="M:NpgsqlTypes.NpgsqlTimeSpan.Canonicalize"/>d <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> with the given number of microseconds.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTimeSpan.FromMilliseconds(System.Double)">
            <summary>
            Creates an <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> from a number of milliseconds.
            </summary>
            <param name="milli">The number of milliseconds in the interval.</param>
            <returns>A <see cref="M:NpgsqlTypes.NpgsqlTimeSpan.Canonicalize"/>d <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> with the given number of milliseconds.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTimeSpan.FromSeconds(System.Double)">
            <summary>
            Creates an <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> from a number of seconds.
            </summary>
            <param name="seconds">The number of seconds in the interval.</param>
            <returns>A <see cref="M:NpgsqlTypes.NpgsqlTimeSpan.Canonicalize"/>d <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> with the given number of seconds.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTimeSpan.FromMinutes(System.Double)">
            <summary>
            Creates an <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> from a number of minutes.
            </summary>
            <param name="minutes">The number of minutes in the interval.</param>
            <returns>A <see cref="M:NpgsqlTypes.NpgsqlTimeSpan.Canonicalize"/>d <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> with the given number of minutes.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTimeSpan.FromHours(System.Double)">
            <summary>
            Creates an <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> from a number of hours.
            </summary>
            <param name="hours">The number of hours in the interval.</param>
            <returns>A <see cref="M:NpgsqlTypes.NpgsqlTimeSpan.Canonicalize"/>d <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> with the given number of hours.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTimeSpan.FromDays(System.Double)">
            <summary>
            Creates an <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> from a number of days.
            </summary>
            <param name="days">The number of days in the interval.</param>
            <returns>A <see cref="M:NpgsqlTypes.NpgsqlTimeSpan.Canonicalize"/>d <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> with the given number of days.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTimeSpan.FromMonths(System.Double)">
            <summary>
            Creates an <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> from a number of months.
            </summary>
            <param name="months">The number of months in the interval.</param>
            <returns>A <see cref="M:NpgsqlTypes.NpgsqlTimeSpan.Canonicalize"/>d <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> with the given number of months.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTimeSpan.Add(NpgsqlTypes.NpgsqlTimeSpan)">
            <summary>
            Adds another interval to this instance and returns the result.
            </summary>
            <param name="interval">An <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> to add to this instance.</param>
            <returns>An <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"></see> whose values are the sums of the two instances.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTimeSpan.Subtract(NpgsqlTypes.NpgsqlTimeSpan)">
            <summary>
            Subtracts another interval from this instance and returns the result.
            </summary>
            <param name="interval">An <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> to subtract from this instance.</param>
            <returns>An <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"></see> whose values are the differences of the two instances.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTimeSpan.Negate">
            <summary>
            Returns an <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> whose value is the negated value of this instance.
            </summary>
            <returns>An <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> whose value is the negated value of this instance.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTimeSpan.Duration">
            <summary>
            This absolute value of this instance. In the case of some, but not all, components being negative,
            the rules used for justification are used to determine if the instance is positive or negative.
            </summary>
            <returns>An <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> whose value is the absolute value of this instance.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTimeSpan.JustifyDays">
            <summary>
            Equivalent to PostgreSQL's justify_days function.
            </summary>
            <returns>An <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> based on this one, but with any hours outside of the range [-23, 23]
            converted into days.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTimeSpan.UnjustifyDays">
            <summary>
            Opposite to PostgreSQL's justify_days function.
            </summary>
            <returns>An <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> based on this one, but with any days converted to multiples of &#xB1;24hours.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTimeSpan.JustifyMonths">
            <summary>
            Equivalent to PostgreSQL's justify_months function.
            </summary>
            <returns>An <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> based on this one, but with any days outside of the range [-30, 30]
            converted into months.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTimeSpan.UnjustifyMonths">
            <summary>
            Opposite to PostgreSQL's justify_months function.
            </summary>
            <returns>An <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> based on this one, but with any months converted to multiples of &#xB1;30days.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTimeSpan.JustifyInterval">
            <summary>
            Equivalent to PostgreSQL's justify_interval function.
            </summary>
            <returns>An <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> based on this one,
            but with any months converted to multiples of &#xB1;30days
            and then with any days converted to multiples of &#xB1;24hours</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTimeSpan.UnjustifyInterval">
            <summary>
            Opposite to PostgreSQL's justify_interval function.
            </summary>
            <returns>An <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> based on this one, but with any months converted to multiples of &#xB1;30days and then any days converted to multiples of &#xB1;24hours;</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTimeSpan.Canonicalize">
            <summary>
            Produces a canonical NpgslInterval with 0 months and hours in the range of [-23, 23].
            <remarks>
            <para>
            While the fact that for many purposes, two different <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> instances could be considered
            equivalent (e.g. one with 2days, 3hours and one with 1day 27hours) there are different possible canonical forms.
            </para><para>
            E.g. we could move all excess hours into days and all excess days into months and have the most readable form,
            or we could move everything into the ticks and have the form that allows for the easiest arithmetic) the form
            chosen has two important properties that make it the best choice.
            </para><para>First, it is closest two how
            <see cref="T:System.TimeSpan"/> objects are most often represented. Second, it is compatible with results of many
            PostgreSQL functions, particularly with age() and the results of subtracting one date, time or timestamp from
            another.
            </para>
            <para>Note that the results of casting a <see cref="T:System.TimeSpan"/> to <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> is
            canonicalised.</para>
            </remarks>
            </summary>
            <returns>An <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> based on this one, but with months converted to multiples of &#xB1;30days and with any hours outside of the range [-23, 23]
            converted into days.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTimeSpan.op_Implicit(System.TimeSpan)~NpgsqlTypes.NpgsqlTimeSpan">
            <summary>
            Implicit cast of a <see cref="T:System.TimeSpan"/> to an <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/>
            </summary>
            <param name="timespan">A <see cref="T:System.TimeSpan"/></param>
            <returns>An eqivalent, canonical, <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/>.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTimeSpan.ToNpgsqlTimeSpan(System.TimeSpan)">
            <summary>
            Casts a <see cref="T:System.TimeSpan"/> to an <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/>.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTimeSpan.op_Explicit(NpgsqlTypes.NpgsqlTimeSpan)~System.TimeSpan">
            <summary>
            Explicit cast of an <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> to a <see cref="T:System.TimeSpan"/>.
            </summary>
            <param name="interval">A <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/>.</param>
            <returns>An equivalent <see cref="T:System.TimeSpan"/>.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTimeSpan.ToTimeSpan(NpgsqlTypes.NpgsqlTimeSpan)">
            <summary>
            Casts an <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> to a <see cref="T:System.TimeSpan"/>.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTimeSpan.Equals(NpgsqlTypes.NpgsqlTimeSpan)">
            <summary>
            Returns true if another <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> is exactly the same as this instance.
            </summary>
            <param name="other">An <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> for comparison.</param>
            <returns>true if the two <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> instances are exactly the same,
            false otherwise.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTimeSpan.Equals(System.Object)">
            <summary>
            Returns true if another object is an <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/>, that is exactly the same as
            this instance
            </summary>
            <param name="obj">An <see cref="T:System.Object"/> for comparison.</param>
            <returns>true if the argument is an <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> and is exactly the same
            as this one, false otherwise.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTimeSpan.Compare(NpgsqlTypes.NpgsqlTimeSpan,NpgsqlTypes.NpgsqlTimeSpan)">
            <summary>
            Compares two <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> instances.
            </summary>
            <param name="x">The first <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/>.</param>
            <param name="y">The second <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/>.</param>
            <returns>0 if the two are equal or equivalent. A value greater than zero if x is greater than y,
            a value less than zero if x is less than y.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTimeSpan.GetHashCode">
            <summary>
            A hash code suitable for uses with hashing algorithms.
            </summary>
            <returns>An signed integer.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTimeSpan.CompareTo(NpgsqlTypes.NpgsqlTimeSpan)">
            <summary>
            Compares this instance with another/
            </summary>
            <param name="other">An <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> to compare this with.</param>
            <returns>0 if the instances are equal or equivalent. A value less than zero if
            this instance is less than the argument. A value greater than zero if this instance
            is greater than the instance.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTimeSpan.CompareTo(System.Object)">
            <summary>
            Compares this instance with another/
            </summary>
            <param name="other">An object to compare this with.</param>
            <returns>0 if the argument is an <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> and the instances are equal or equivalent.
            A value less than zero if the argument is an <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> and
            this instance is less than the argument.
            A value greater than zero if the argument is an <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> and this instance
            is greater than the instance.</returns>
            A value greater than zero if the argument is null.
            <exception cref="T:System.ArgumentException">The argument is not an <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/>.</exception>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTimeSpan.Parse(System.String)">
            <summary>
            Parses a <see cref="T:System.String"/> and returns a <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> instance.
            Designed to use the formats generally returned by PostgreSQL.
            </summary>
            <param name="str">The <see cref="T:System.String"/> to parse.</param>
            <returns>An <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> represented by the argument.</returns>
            <exception cref="T:System.ArgumentNullException">The string was null.</exception>
            <exception cref="T:System.OverflowException">A value obtained from parsing the string exceeded the values allowed for the relevant component.</exception>
            <exception cref="T:System.FormatException">The string was not in a format that could be parsed to produce an <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/>.</exception>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTimeSpan.TryParse(System.String,NpgsqlTypes.NpgsqlTimeSpan@)">
            <summary>
            Attempt to parse a <see cref="T:System.String"/> to produce an <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/>.
            </summary>
            <param name="str">The <see cref="T:System.String"/> to parse.</param>
            <param name="result">(out) The <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> produced, or <see cref="F:NpgsqlTypes.NpgsqlTimeSpan.Zero"/> if the parsing failed.</param>
            <returns>true if the parsing succeeded, false otherwise.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTimeSpan.ToString">
            <summary>
            Create a <see cref="T:System.String"/> representation of the <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> instance.
            The format returned is of the form:
            [M mon[s]] [d day[s]] [HH:mm:ss[.f[f[f[f[f[f[f[f[f]]]]]]]]]]
            A zero <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> is represented as 00:00:00
            <remarks>
            Ticks are 100ns, Postgress resolution is only to 1&#xb5;s at most. Hence we lose 1 or more decimal
            precision in storing values in the database. Despite this, this method will output that extra
            digit of precision. It's forward-compatible with any future increases in resolution up to 100ns,
            and also makes this ToString() more applicable to any other use-case.
            </remarks>
            </summary>
            <returns>The <see cref="T:System.String"/> representation.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTimeSpan.op_Addition(NpgsqlTypes.NpgsqlTimeSpan,NpgsqlTypes.NpgsqlTimeSpan)">
            <summary>
            Adds two <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> together.
            </summary>
            <param name="x">The first <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> to add.</param>
            <param name="y">The second <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> to add.</param>
            <returns>An <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> whose values are the sum of the arguments.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTimeSpan.op_Subtraction(NpgsqlTypes.NpgsqlTimeSpan,NpgsqlTypes.NpgsqlTimeSpan)">
            <summary>
            Subtracts one <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> from another.
            </summary>
            <param name="x">The <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> to subtract the other from.</param>
            <param name="y">The <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> to subtract from the other.</param>
            <returns>An <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> whose values are the difference of the arguments</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTimeSpan.op_Equality(NpgsqlTypes.NpgsqlTimeSpan,NpgsqlTypes.NpgsqlTimeSpan)">
            <summary>
            Returns true if two <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> are exactly the same.
            </summary>
            <param name="x">The first <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> to compare.</param>
            <param name="y">The second <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> to compare.</param>
            <returns>true if the two arguments are exactly the same, false otherwise.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTimeSpan.op_Inequality(NpgsqlTypes.NpgsqlTimeSpan,NpgsqlTypes.NpgsqlTimeSpan)">
            <summary>
            Returns false if two <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> are exactly the same.
            </summary>
            <param name="x">The first <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> to compare.</param>
            <param name="y">The second <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> to compare.</param>
            <returns>false if the two arguments are exactly the same, true otherwise.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTimeSpan.op_LessThan(NpgsqlTypes.NpgsqlTimeSpan,NpgsqlTypes.NpgsqlTimeSpan)">
            <summary>
            Compares two <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> instances to see if the first is less than the second
            </summary>
            <param name="x">The first <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> to compare.</param>
            <param name="y">The second <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> to compare.</param>
            <returns>true if the first <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> is less than second, false otherwise.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTimeSpan.op_LessThanOrEqual(NpgsqlTypes.NpgsqlTimeSpan,NpgsqlTypes.NpgsqlTimeSpan)">
            <summary>
            Compares two <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> instances to see if the first is less than or equivalent to the second
            </summary>
            <param name="x">The first <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> to compare.</param>
            <param name="y">The second <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> to compare.</param>
            <returns>true if the first <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> is less than or equivalent to second, false otherwise.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTimeSpan.op_GreaterThan(NpgsqlTypes.NpgsqlTimeSpan,NpgsqlTypes.NpgsqlTimeSpan)">
            <summary>
            Compares two <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> instances to see if the first is greater than the second
            </summary>
            <param name="x">The first <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> to compare.</param>
            <param name="y">The second <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> to compare.</param>
            <returns>true if the first <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> is greater than second, false otherwise.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTimeSpan.op_GreaterThanOrEqual(NpgsqlTypes.NpgsqlTimeSpan,NpgsqlTypes.NpgsqlTimeSpan)">
            <summary>
            Compares two <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> instances to see if the first is greater than or equivalent the second
            </summary>
            <param name="x">The first <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> to compare.</param>
            <param name="y">The second <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> to compare.</param>
            <returns>true if the first <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> is greater than or equivalent to the second, false otherwise.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTimeSpan.op_UnaryPlus(NpgsqlTypes.NpgsqlTimeSpan)">
            <summary>
            Returns the instance.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTimeSpan.Plus(NpgsqlTypes.NpgsqlTimeSpan)">
            <summary>
            Returns the instance.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTimeSpan.op_UnaryNegation(NpgsqlTypes.NpgsqlTimeSpan)">
            <summary>
            Negates an <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/> instance.
            </summary>
            <param name="x">An <see cref="T:NpgsqlTypes.NpgsqlTimeSpan"/>.</param>
            <returns>The negation of the argument.</returns>
        </member>
        <member name="T:NpgsqlTypes.NpgsqlTsQuery">
            <summary>
            Represents a PostgreSQL tsquery. This is the base class for the
            lexeme, not, or, and, and "followed by" nodes.
            </summary>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlTsQuery.Kind">
            <summary>
            Node kind
            </summary>
        </member>
        <member name="T:NpgsqlTypes.NpgsqlTsQuery.NodeKind">
            <summary>
            NodeKind
            </summary>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlTsQuery.NodeKind.Empty">
            <summary>
            Represents the empty tsquery. Should only be used at top level.
            </summary>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlTsQuery.NodeKind.Lexeme">
            <summary>
            Lexeme
            </summary>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlTsQuery.NodeKind.Not">
            <summary>
            Not operator
            </summary>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlTsQuery.NodeKind.And">
            <summary>
            And operator
            </summary>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlTsQuery.NodeKind.Or">
            <summary>
            Or operator
            </summary>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlTsQuery.NodeKind.Phrase">
            <summary>
            "Followed by" operator
            </summary>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTsQuery.ToString">
            <summary>
            Writes the tsquery in PostgreSQL's text format.
            </summary>
            <returns></returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTsQuery.Parse(System.String)">
            <summary>
            Parses a tsquery in PostgreSQL's text format.
            </summary>
            <param name="value"></param>
            <returns></returns>
        </member>
        <member name="T:NpgsqlTypes.NpgsqlTsQueryLexeme">
            <summary>
            TsQuery Lexeme node.
            </summary>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlTsQueryLexeme.Text">
            <summary>
            Lexeme text.
            </summary>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlTsQueryLexeme.Weights">
            <summary>
            Weights is a bitmask of the Weight enum.
            </summary>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlTsQueryLexeme.IsPrefixSearch">
            <summary>
            Prefix search.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTsQueryLexeme.#ctor(System.String)">
            <summary>
            Creates a tsquery lexeme with only lexeme text.
            </summary>
            <param name="text">Lexeme text.</param>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTsQueryLexeme.#ctor(System.String,NpgsqlTypes.NpgsqlTsQueryLexeme.Weight)">
            <summary>
            Creates a tsquery lexeme with lexeme text and weights.
            </summary>
            <param name="text">Lexeme text.</param>
            <param name="weights">Bitmask of enum Weight.</param>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTsQueryLexeme.#ctor(System.String,NpgsqlTypes.NpgsqlTsQueryLexeme.Weight,System.Boolean)">
            <summary>
            Creates a tsquery lexeme with lexeme text, weights and prefix search flag.
            </summary>
            <param name="text">Lexeme text.</param>
            <param name="weights">Bitmask of enum Weight.</param>
            <param name="isPrefixSearch">Is prefix search?</param>
        </member>
        <member name="T:NpgsqlTypes.NpgsqlTsQueryLexeme.Weight">
            <summary>
            Weight enum, can be OR'ed together.
            </summary>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlTsQueryLexeme.Weight.None">
            <summary>
            None
            </summary>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlTsQueryLexeme.Weight.D">
            <summary>
            D
            </summary>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlTsQueryLexeme.Weight.C">
            <summary>
            C
            </summary>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlTsQueryLexeme.Weight.B">
            <summary>
            B
            </summary>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlTsQueryLexeme.Weight.A">
            <summary>
            A
            </summary>
        </member>
        <member name="T:NpgsqlTypes.NpgsqlTsQueryNot">
            <summary>
            TsQuery Not node.
            </summary>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlTsQueryNot.Child">
            <summary>
            Child node
            </summary>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTsQueryNot.#ctor(NpgsqlTypes.NpgsqlTsQuery)">
            <summary>
            Creates a not operator, with a given child node.
            </summary>
            <param name="child"></param>
        </member>
        <member name="T:NpgsqlTypes.NpgsqlTsQueryBinOp">
            <summary>
            Base class for TsQuery binary operators (&amp; and |).
            </summary>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlTsQueryBinOp.Left">
            <summary>
            Left child
            </summary>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlTsQueryBinOp.Right">
            <summary>
            Right child
            </summary>
        </member>
        <member name="T:NpgsqlTypes.NpgsqlTsQueryAnd">
            <summary>
            TsQuery And node.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTsQueryAnd.#ctor(NpgsqlTypes.NpgsqlTsQuery,NpgsqlTypes.NpgsqlTsQuery)">
            <summary>
            Creates an and operator, with two given child nodes.
            </summary>
            <param name="left"></param>
            <param name="right"></param>
        </member>
        <member name="T:NpgsqlTypes.NpgsqlTsQueryOr">
            <summary>
            TsQuery Or Node.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTsQueryOr.#ctor(NpgsqlTypes.NpgsqlTsQuery,NpgsqlTypes.NpgsqlTsQuery)">
            <summary>
            Creates an or operator, with two given child nodes.
            </summary>
            <param name="left"></param>
            <param name="right"></param>
        </member>
        <member name="T:NpgsqlTypes.NpgsqlTsQueryFollowedBy">
            <summary>
            TsQuery "Followed by" Node.
            </summary>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlTsQueryFollowedBy.Distance">
            <summary>
            The distance between the 2 nodes, in lexemes.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTsQueryFollowedBy.#ctor(NpgsqlTypes.NpgsqlTsQuery,System.Int32,NpgsqlTypes.NpgsqlTsQuery)">
            <summary>
            Creates a "followed by" operator, specifying 2 child nodes and the 
            distance between them in lexemes.
            </summary>
            <param name="left"></param>
            <param name="distance"></param>
            <param name="right"></param>
        </member>
        <member name="T:NpgsqlTypes.NpgsqlTsQueryEmpty">
            <summary>
            Represents an empty tsquery. Shold only be used as top node.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTsQueryEmpty.#ctor">
            <summary>
            Creates a tsquery that represents an empty query. Should not be used as child node.
            </summary>
        </member>
        <member name="T:NpgsqlTypes.NpgsqlTsVector">
            <summary>
            Represents a PostgreSQL tsvector.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTsVector.#ctor(System.Collections.Generic.List{NpgsqlTypes.NpgsqlTsVector.Lexeme})">
            <summary>
            Constructs an NpgsqlTsVector from a list of lexemes. This also sorts and remove duplicates.
            </summary>
            <param name="lexemes"></param>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTsVector.Parse(System.String)">
            <summary>
            Parses a tsvector in PostgreSQL's text format.
            </summary>
            <param name="value"></param>
            <returns></returns>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlTsVector.Item(System.Int32)">
            <summary>
            Returns the lexeme at a specific index
            </summary>
            <param name="index"></param>
            <returns></returns>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlTsVector.Count">
            <summary>
            Gets the number of lexemes.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTsVector.GetEnumerator">
            <summary>
            Returns an enumerator.
            </summary>
            <returns></returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTsVector.System#Collections#IEnumerable#GetEnumerator">
            <summary>
            Returns an enumerator.
            </summary>
            <returns></returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTsVector.ToString">
            <summary>
            Gets a string representation in PostgreSQL's format.
            </summary>
            <returns></returns>
        </member>
        <member name="T:NpgsqlTypes.NpgsqlTsVector.Lexeme">
            <summary>
            Represents a lexeme. A lexeme consists of a text string and optional word entry positions.
            </summary>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlTsVector.Lexeme.Text">
            <summary>
            Gets or sets the text.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTsVector.Lexeme.#ctor(System.String)">
            <summary>
            Creates a lexeme with no word entry positions.
            </summary>
            <param name="text"></param>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTsVector.Lexeme.#ctor(System.String,System.Collections.Generic.List{NpgsqlTypes.NpgsqlTsVector.Lexeme.WordEntryPos})">
            <summary>
            Creates a lexeme with word entry positions.
            </summary>
            <param name="text"></param>
            <param name="wordEntryPositions"></param>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlTsVector.Lexeme.Item(System.Int32)">
            <summary>
            Gets a word entry position.
            </summary>
            <param name="index"></param>
            <returns></returns>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlTsVector.Lexeme.Count">
            <summary>
            Gets the number of word entry positions.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTsVector.Lexeme.ToString">
            <summary>
            Creates a string representation in PostgreSQL's format.
            </summary>
            <returns></returns>
        </member>
        <member name="T:NpgsqlTypes.NpgsqlTsVector.Lexeme.WordEntryPos">
            <summary>
            Represents a word entry position and an optional weight.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTsVector.Lexeme.WordEntryPos.#ctor(System.Int32,NpgsqlTypes.NpgsqlTsVector.Lexeme.Weight)">
            <summary>
            Creates a WordEntryPos with a given position and weight.
            </summary>
            <param name="pos">Position values can range from 1 to 16383; larger numbers are silently set to 16383.</param>
            <param name="weight">A weight labeled between A and D.</param>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlTsVector.Lexeme.WordEntryPos.Weight">
            <summary>
            The weight is labeled from A to D. D is the default, and not printed.
            </summary>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlTsVector.Lexeme.WordEntryPos.Pos">
            <summary>
            The position is a 14-bit unsigned integer indicating the position in the text this lexeme occurs. Cannot be 0.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTsVector.Lexeme.WordEntryPos.ToString">
            <summary>
            Prints this lexeme in PostgreSQL's format, i.e. position is followed by weight (weight is only printed if A, B or C).
            </summary>
            <returns></returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTsVector.Lexeme.WordEntryPos.Equals(NpgsqlTypes.NpgsqlTsVector.Lexeme.WordEntryPos)">
            <summary>
            Determines whether the specified object is equal to the current object.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTsVector.Lexeme.WordEntryPos.Equals(System.Object)">
            <summary>
            Determines whether the specified object is equal to the current object.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTsVector.Lexeme.WordEntryPos.GetHashCode">
            <summary>
            Gets a hash code for the current object.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTsVector.Lexeme.WordEntryPos.op_Equality(NpgsqlTypes.NpgsqlTsVector.Lexeme.WordEntryPos,NpgsqlTypes.NpgsqlTsVector.Lexeme.WordEntryPos)">
            <summary>
            Determines whether the specified object is equal to the current object.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTsVector.Lexeme.WordEntryPos.op_Inequality(NpgsqlTypes.NpgsqlTsVector.Lexeme.WordEntryPos,NpgsqlTypes.NpgsqlTsVector.Lexeme.WordEntryPos)">
            <summary>
            Determines whether the specified object is unequal to the current object.
            </summary>
        </member>
        <member name="T:NpgsqlTypes.NpgsqlTsVector.Lexeme.Weight">
            <summary>
            The weight is labeled from A to D. D is the default, and not printed.
            </summary>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlTsVector.Lexeme.Weight.D">
            <summary>
            D, the default
            </summary>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlTsVector.Lexeme.Weight.C">
            <summary>
            C
            </summary>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlTsVector.Lexeme.Weight.B">
            <summary>
            B
            </summary>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlTsVector.Lexeme.Weight.A">
            <summary>
            A
            </summary>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTsVector.Lexeme.Equals(NpgsqlTypes.NpgsqlTsVector.Lexeme)">
            <summary>
            Determines whether the specified object is equal to the current object.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTsVector.Lexeme.Equals(System.Object)">
            <summary>
            Determines whether the specified object is equal to the current object.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTsVector.Lexeme.GetHashCode">
            <summary>
            Gets a hash code for the current object.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTsVector.Lexeme.op_Equality(NpgsqlTypes.NpgsqlTsVector.Lexeme,NpgsqlTypes.NpgsqlTsVector.Lexeme)">
            <summary>
            Determines whether the specified object is equal to the current object.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTsVector.Lexeme.op_Inequality(NpgsqlTypes.NpgsqlTsVector.Lexeme,NpgsqlTypes.NpgsqlTsVector.Lexeme)">
            <summary>
            Determines whether the specified object is unequal to the current object.
            </summary>
        </member>
        <member name="T:NpgsqlTypes.NpgsqlPoint">
            <summary>
            Represents a PostgreSQL point type.
            </summary>
            <remarks>
            See http://www.postgresql.org/docs/current/static/datatype-geometric.html
            </remarks>
        </member>
        <member name="T:NpgsqlTypes.NpgsqlLine">
            <summary>
            Represents a PostgreSQL line type.
            </summary>
            <remarks>
            See http://www.postgresql.org/docs/current/static/datatype-geometric.html
            </remarks>
        </member>
        <member name="T:NpgsqlTypes.NpgsqlLSeg">
            <summary>
            Represents a PostgreSQL Line Segment type.
            </summary>
        </member>
        <member name="T:NpgsqlTypes.NpgsqlBox">
            <summary>
            Represents a PostgreSQL box type.
            </summary>
            <remarks>
            See http://www.postgresql.org/docs/current/static/datatype-geometric.html
            </remarks>
        </member>
        <member name="T:NpgsqlTypes.NpgsqlPath">
            <summary>
            Represents a PostgreSQL Path type.
            </summary>
        </member>
        <member name="T:NpgsqlTypes.NpgsqlPolygon">
            <summary>
            Represents a PostgreSQL Polygon type.
            </summary>
        </member>
        <member name="T:NpgsqlTypes.NpgsqlCircle">
            <summary>
            Represents a PostgreSQL Circle type.
            </summary>
        </member>
        <member name="T:NpgsqlTypes.NpgsqlInet">
            <summary>
            Represents a PostgreSQL inet type, which is a combination of an IPAddress and a
            subnet mask.
            </summary>
            <remarks>
            http://www.postgresql.org/docs/current/static/datatype-net-types.html
            </remarks>
        </member>
        <member name="T:NpgsqlTypes.NpgsqlTid">
            <summary>
            Represents a PostgreSQL tid value
            </summary>
            <remarks>
            http://www.postgresql.org/docs/current/static/datatype-oid.html
            </remarks>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlTid.BlockNumber">
            <summary>
            Block number
            </summary>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlTid.OffsetNumber">
            <summary>
            Tuple index within block
            </summary>
        </member>
        <member name="T:NpgsqlTypes.PgNameAttribute">
            <summary>
            Indicates that this property or field correspond to a PostgreSQL field with the specified name
            </summary>
        </member>
        <member name="P:NpgsqlTypes.PgNameAttribute.PgName">
            <summary>
            The name of PostgreSQL field that corresponds to this CLR property or field
            </summary>
        </member>
        <member name="M:NpgsqlTypes.PgNameAttribute.#ctor(System.String)">
            <summary>
            Indicates that this property or field correspond to a PostgreSQL field with the specified name
            </summary>
            <param name="pgName">The name of PostgreSQL field that corresponds to this CLR property or field</param>
        </member>
        <member name="T:JetBrains.Annotations.CanBeNullAttribute">
            <summary>
            Indicates that the value of the marked element could be <c>null</c> sometimes,
            so the check for <c>null</c> is necessary before its usage.
            </summary>
            <example><code>
            [CanBeNull] public object Test() { return null; }
            public void UseTest() {
              var p = Test();
              var s = p.ToString(); // Warning: Possible 'System.NullReferenceException'
            }
            </code></example>
        </member>
        <member name="T:JetBrains.Annotations.NotNullAttribute">
            <summary>
            Indicates that the value of the marked element could never be <c>null</c>.
            </summary>
            <example><code>
            [NotNull] public object Foo() {
              return null; // Warning: Possible 'null' assignment
            }
            </code></example>
        </member>
        <member name="T:JetBrains.Annotations.ItemNotNullAttribute">
            <summary>
            Can be appplied to symbols of types derived from IEnumerable as well as to symbols of Task
            and Lazy classes to indicate that the value of a collection item, of the Task.Result property
            or of the Lazy.Value property can never be null.
            </summary>
        </member>
        <member name="T:JetBrains.Annotations.ItemCanBeNullAttribute">
            <summary>
            Can be appplied to symbols of types derived from IEnumerable as well as to symbols of Task
            and Lazy classes to indicate that the value of a collection item, of the Task.Result property
            or of the Lazy.Value property can be null.
            </summary>
        </member>
        <member name="T:JetBrains.Annotations.UsedImplicitlyAttribute">
            <summary>
            Indicates that the marked symbol is used implicitly (e.g. via reflection, in external library),
            so this symbol will not be marked as unused (as well as by other usage inspections).
            </summary>
        </member>
        <member name="T:JetBrains.Annotations.MeansImplicitUseAttribute">
            <summary>
            Should be used on attributes and causes ReSharper to not mark symbols marked with such attributes
            as unused (as well as by other usage inspections)
            </summary>
        </member>
        <member name="F:JetBrains.Annotations.ImplicitUseKindFlags.Access">
            <summary>Only entity marked with attribute considered used.</summary>
        </member>
        <member name="F:JetBrains.Annotations.ImplicitUseKindFlags.Assign">
            <summary>Indicates implicit assignment to a member.</summary>
        </member>
        <member name="F:JetBrains.Annotations.ImplicitUseKindFlags.InstantiatedWithFixedConstructorSignature">
            <summary>
            Indicates implicit instantiation of a type with fixed constructor signature.
            That means any unused constructor parameters won't be reported as such.
            </summary>
        </member>
        <member name="F:JetBrains.Annotations.ImplicitUseKindFlags.InstantiatedNoFixedConstructorSignature">
            <summary>Indicates implicit instantiation of a type.</summary>
        </member>
        <member name="T:JetBrains.Annotations.ImplicitUseTargetFlags">
            <summary>
            Specify what is considered used implicitly when marked
            with <see cref="T:JetBrains.Annotations.MeansImplicitUseAttribute"/> or <see cref="T:JetBrains.Annotations.UsedImplicitlyAttribute"/>.
            </summary>
        </member>
        <member name="F:JetBrains.Annotations.ImplicitUseTargetFlags.Members">
            <summary>Members of entity marked with attribute are considered used.</summary>
        </member>
        <member name="F:JetBrains.Annotations.ImplicitUseTargetFlags.WithMembers">
            <summary>Entity marked with attribute and all its members considered used.</summary>
        </member>
        <member name="T:JetBrains.Annotations.PublicAPIAttribute">
            <summary>
            This attribute is intended to mark publicly available API
            which should not be removed and so is treated as used.
            </summary>
        </member>
        <member name="T:JetBrains.Annotations.ContractAnnotationAttribute">
            <summary>
            Describes dependency between method input and output.
            </summary>
            <syntax>
            <p>Function Definition Table syntax:</p>
            <list>
            <item>FDT      ::= FDTRow [;FDTRow]*</item>
            <item>FDTRow   ::= Input =&gt; Output | Output &lt;= Input</item>
            <item>Input    ::= ParameterName: Value [, Input]*</item>
            <item>Output   ::= [ParameterName: Value]* {halt|stop|void|nothing|Value}</item>
            <item>Value    ::= true | false | null | notnull | canbenull</item>
            </list>
            If method has single input parameter, it's name could be omitted.<br/>
            Using <c>halt</c> (or <c>void</c>/<c>nothing</c>, which is the same)
            for method output means that the methos doesn't return normally.<br/>
            <c>canbenull</c> annotation is only applicable for output parameters.<br/>
            You can use multiple <c>[ContractAnnotation]</c> for each FDT row,
            or use single attribute with rows separated by semicolon.<br/>
            </syntax>
            <examples><list>
            <item><code>
            [ContractAnnotation("=> halt")]
            public void TerminationMethod()
            </code></item>
            <item><code>
            [ContractAnnotation("halt &lt;= condition: false")]
            public void Assert(bool condition, string text) // regular assertion method
            </code></item>
            <item><code>
            [ContractAnnotation("s:null => true")]
            public bool IsNullOrEmpty(string s) // string.IsNullOrEmpty()
            </code></item>
            <item><code>
            // A method that returns null if the parameter is null,
            // and not null if the parameter is not null
            [ContractAnnotation("null => null; notnull => notnull")]
            public object Transform(object data)
            </code></item>
            <item><code>
            [ContractAnnotation("s:null=>false; =>true,result:notnull; =>false, result:null")]
            public bool TryParse(string s, out Person result)
            </code></item>
            </list></examples>
        </member>
        <member name="T:JetBrains.Annotations.InvokerParameterNameAttribute">
            <summary>
            Indicates that the function argument should be string literal and match one
            of the parameters of the caller function. For example, ReSharper annotates
            the parameter of <see cref="T:System.ArgumentNullException"/>.
            </summary>
            <example><code>
            public void Foo(string param) {
              if (param == null)
                throw new ArgumentNullException("par"); // Warning: Cannot resolve symbol
            }
            </code></example>
        </member>
        <member name="T:JetBrains.Annotations.NoEnumerationAttribute">
            <summary>
            Indicates that IEnumerable, passed as parameter, is not enumerated.
            </summary>
        </member>
    </members>
</doc>
