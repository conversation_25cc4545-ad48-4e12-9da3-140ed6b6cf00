﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Entidades.Gasolina
{
    public class EstadoGasolina
    {
        private int _iNumEmp;
        private string _scorreoEmpleado;

        public EstadoGasolina() //Constructor
        {
           _iNumEmp = 0;
           _scorreoEmpleado = string.Empty;
        }

        //Propiedades
        public int iNumEmp
        {
            get
            {
                return _iNumEmp;
            }
            set
            {
                _iNumEmp = value;
            }
        }
        public string sCorreoEmpleado
        {
            get
            {
                return _scorreoEmpleado;
            }
            set
            {
                _scorreoEmpleado = value;
            }
        }
    }
}
