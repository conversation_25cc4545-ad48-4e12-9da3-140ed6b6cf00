﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Configuration;
using System.IO;
using Datos.PDF;
using Datos.Errores;
using System.Windows.Forms;
using Datos.Nomina;
using Datos.Control;
using Entidades.Control;

namespace Negocios.Generales
{
    public class GeneralesBO
    {
        public NominaControl obtenerFechaNominaControl()
        {
            NominaControlDAL NominaControl = new NominaControlDAL(); 
            try
            {
                return NominaControl.obtenerFechaNomina();
            }
            catch (Exception ex)
            {
                GrabarErrorDAL Error = new GrabarErrorDAL();
                Error.grabarError("Negocios.Generales", "GeneralesBO.cs", "obtenerFechaNominaControl", "Error al consultar la fecha Nomina", 32, ex.Message.ToString());
                throw new Exception(ex.Message.ToString());
            }
        }

        public FondoControl obtenerFechaControlFondo(int iMovimiento)
        {
            FondoControlDAL FondoControl = new FondoControlDAL();
            try
            {
                return FondoControl.obtenerFechaFondo(iMovimiento);
            }
            catch (Exception ex)
            {
                GrabarErrorDAL Error = new GrabarErrorDAL();
                Error.grabarError("Negocios.Generales", "GeneralesBO.cs", "obtenerFechaControlFondo", "Error al obntener la fecha control del fondo", 33, ex.Message.ToString());
                throw new Exception(ex.Message.ToString());
            }
        }

        public void decodificarPDF(int iNumeroEmpleado, DateTime dFechaNomina)
        {
            PDFDAL PDF = new PDFDAL();
            string sNombrePDF = iNumeroEmpleado + "_" + dFechaNomina.ToString("yyyy-MM-dd");
            try
            {
                PDF.DecodificarPDF(iNumeroEmpleado, sNombrePDF);
            }
            catch (Exception ex)
            {
                GrabarErrorDAL Error = new GrabarErrorDAL();
                Error.grabarError("Negocios.Generales", "GeneralesBO.cs", "decodificarPDF", "Error al decodificar PDF", 34, ex.Message.ToString());
                throw new Exception(ex.Message.ToString());
            }
        }

        public void depurarArchivo(int iNumeroEmpleado, DateTime dFechaNomina)
        {
            string sCarpetaPDF = @"C:\PDF\";
            string sNombreArchivo = iNumeroEmpleado.ToString() + "_" + dFechaNomina.ToString("yyyy-MM-dd");
            string rutaCompleta = Path.Combine(sCarpetaPDF, sNombreArchivo + ".pdf");
            
            using (var fs = new FileStream(rutaCompleta, FileMode.Open))
            {
                if (Directory.Exists(sCarpetaPDF))
                {
                    try
                    {
                        fs.Close();
                        fs.Dispose();
                        File.Delete(rutaCompleta);
                    }
                    catch (Exception ex)
                    {
                        GrabarErrorDAL Error = new GrabarErrorDAL();
                        Error.grabarError("Negocios.Generales", "GeneralesBO.cs", "depurarArchivo", "Error al depurar el archivo", 35, ex.Message.ToString());
                        throw new Exception(ex.Message.ToString());
                    }
                    // Se cierran los archivos para liberar memoria.
                    finally
                    {
                        fs.Close();
                        fs.Dispose();
                    }
                }

            }
               
        }

        public void depurarCarpeta()
        {
            string sCarpetaPDF = @"C:\PDF\";

            if (Directory.Exists(sCarpetaPDF))
            {
                try
                {
                    Directory.Delete(sCarpetaPDF);
                }
                catch (Exception ex)
                {
                    GrabarErrorDAL Error = new GrabarErrorDAL();
                    Error.grabarError("Negocios.Generales", "GeneralesBO.cs", "depurarCarpeta", "Error eliminar carpeta", 36, ex.Message.ToString());
                    throw new Exception(ex.Message.ToString());
                }
            }
        }

        public byte[] convertirPDFA64(int iNumeroEmpleado, DateTime dFechaNomina)
        {
            string sBase64 = string.Empty;
            string sRutaArchivo = @"C:\PDF\";
            string sNombreArchivo = iNumeroEmpleado.ToString() + "_" + dFechaNomina.ToString("yyyy-MM-dd");
            string rutaCompleta = Path.Combine(sRutaArchivo, sNombreArchivo + ".pdf");
            // Sanitizamos la ruta
            string sRutaSana = SanitizarRutaArchivo(rutaCompleta);
     
            using (var fs = new FileStream(sRutaSana, FileMode.Open))
            using (var br = new BinaryReader(fs))
            {
                byte[] bytes = new byte[(int)fs.Length];
                try
                {
                    br.Read(bytes, 0, bytes.Length);
                    // base64 es la cadena en donde se guarda el arreglo de bytes ya convertido
                    sBase64 = Convert.ToBase64String(bytes);
                    return bytes;
                }
                catch
                {
                    MessageBox.Show("Ocurrio un error al cargar el archivo.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Asterisk, MessageBoxDefaultButton.Button1);
                    GrabarErrorDAL Error = new GrabarErrorDAL();
                    Error.grabarError("Negocios.Generales", "GeneralesBO.cs", "convertirPDFA64", "Error al convertir a base 64", 37, "sRutaArchivo C:/PDF");
                    return null;
                }
                // Se cierran los archivos para liberar memoria.
                finally
                {
                    fs.Close();
                    fs.Dispose();
                    br.Close();
                    br.Dispose();
                    bytes = null;
                }
            }
        }

        /// <summary>
        /// Función para sanitizar la ruta del archivo 
        /// </summary>
        /// <param name="rutaArchivo"></param>
        /// <returns></returns>
        public string SanitizarRutaArchivo(string rutaArchivo)
        {
            return rutaArchivo;
        }
    }
}
