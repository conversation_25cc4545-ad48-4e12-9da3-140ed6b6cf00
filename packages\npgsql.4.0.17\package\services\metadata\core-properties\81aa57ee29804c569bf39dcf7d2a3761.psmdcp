<?xml version="1.0" encoding="utf-8"?>
<coreProperties xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:dcterms="http://purl.org/dc/terms/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://schemas.openxmlformats.org/package/2006/metadata/core-properties">
  <dc:creator><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>., <PERSON><PERSON></dc:creator>
  <dc:description>Npgsql is the open source .NET data provider for PostgreSQL.</dc:description>
  <dc:identifier>Npgsql</dc:identifier>
  <version>4.0.17</version>
  <keywords>npgsql postgresql postgres ado ado.net database sql</keywords>
  <lastModifiedBy>NuGet.Build.Tasks.Pack, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35;.NET Standard 2.0</lastModifiedBy>
</coreProperties>