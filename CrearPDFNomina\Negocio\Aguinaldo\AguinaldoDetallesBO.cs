﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Entidades.Nomina;
using Datos.Nomina;
using Datos.Errores;

namespace Negocios.Aguinaldo
{
    public class AguinaldoDetallesBO
    {
        public List<NominaDetalleEmpleado> obtenerAguinaldoDetalleEmpleado(int iNumeroEmpleado, DateTime dFechaNomina, int iEmpresa)
        {
            NominaDetalleDAL NominaDetalle = new NominaDetalleDAL();
            try
            {
                return NominaDetalle.consultarNominaDetalle(iNumeroEmpleado, dFechaNomina, iEmpresa);
            }
            catch (Exception ex)
            {
                GrabarErrorDAL Error = new GrabarErrorDAL();
                Error.grabarError("Negocios.Aguinaldo", "AguinaldoDetallesBO.cs", "obtenerAguinaldoDetalleEmpleado", "Error al obtener informacion detallada del empleado", 19, ex.Message.ToString());
                throw new Exception(ex.Message.ToString());
            }
        }
    }
}
