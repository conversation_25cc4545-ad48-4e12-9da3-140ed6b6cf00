﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Entidades.Fondo
{
    public class EstadoFondo
    {
        private int _iNumEmp;
        private string _sApellildoPaterno;
        private string _sApellildoMaterno;
        private string _sNombre;
        private string _sRfc;
        private string _sCurp;
        private DateTime _dFechaAlta;
        private int _iSueldoMensual;
        private int _iCentro;
        private string _sDescripcionCentro;
        private int _iNumeroSeguro;
        private decimal _iImporteSeguro;
        private string _sCorreoEmpleado;

        public EstadoFondo() //Constructor
        {
           _iNumEmp = 0;
           _sApellildoPaterno = string.Empty;
           _sApellildoMaterno = string.Empty;
           _sNombre = string.Empty;
           _sRfc = string.Empty;
           _sCurp = string.Empty;
           _dFechaAlta = Convert.ToDateTime("1900-01-01");
           _iSueldoMensual = 0;
           _iCentro = 0;
           _sDescripcionCentro = string.Empty;
           _iNumeroSeguro = 0;
           _iImporteSeguro = 0;
           _sCorreoEmpleado = string.Empty;
        }

        //Propiedades
        public int iNumEmp
        {
            get
            {
                return _iNumEmp;
            }
            set
            {
                _iNumEmp = value;
            }
        }
        public string sApellidoPaterno
        {
            get
            {
                return _sApellildoPaterno;
            }
            set
            {
                _sApellildoPaterno = value;
            }
        }
        public string sApellidoMaterno
        {
            get
            {
                return _sApellildoMaterno;
            }
            set
            {
                _sApellildoMaterno = value;
            }
        }
        public string sNombre
        {
            get
            {
                return _sNombre;
            }
            set
            {
                _sNombre = value;
            }
        }
        public string sRFC
        {
            get
            {
                return _sRfc;
            }
            set
            {
                _sRfc = value;
            }
        }
        public string sCurp
        {
            get
            {
                return _sCurp;
            }
            set
            {
                _sCurp = value;
            }
        }
        public DateTime dFechaAlta
        {
            get
            {
                return _dFechaAlta;
            }
            set
            {
                _dFechaAlta = value;
            }
        }
        public int iSueldoMensual
        {
            get
            {
                return _iSueldoMensual;
            }
            set
            {
                _iSueldoMensual = value;
            }
        }
        public int iCentro
        {
            get
            {
                return _iCentro;
            }
            set
            {
                _iCentro = value;
            }
        }
        public string sDescripcionCentro
        {
            get
            {
                return _sDescripcionCentro;
            }
            set
            {
                _sDescripcionCentro = value;
            }
        }
        public int iNumeroSeguro
        {
            get
            {
                return _iNumeroSeguro;
            }
            set
            {
                _iNumeroSeguro = value;
            }
        }
        public decimal iImporteSeguro
        {
            get
            {
                return _iImporteSeguro;
            }
            set
            {
                _iImporteSeguro = value;
            }
        }
        public string sCorreoEmpleado
        {
            get
            {
                return _sCorreoEmpleado;
            }
            set
            {
                _sCorreoEmpleado = value;
            }
        }
    }
}
