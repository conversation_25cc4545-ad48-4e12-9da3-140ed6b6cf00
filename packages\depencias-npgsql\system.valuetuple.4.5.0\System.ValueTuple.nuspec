﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2013/05/nuspec.xsd">
  <metadata minClientVersion="2.12">
    <id>System.ValueTuple</id>
    <version>4.5.0</version>
    <title>System.ValueTuple</title>
    <authors>Microsoft</authors>
    <owners>microsoft,dotnetframework</owners>
    <requireLicenseAcceptance>false</requireLicenseAcceptance>
    <licenseUrl>https://github.com/dotnet/corefx/blob/master/LICENSE.TXT</licenseUrl>
    <projectUrl>https://dot.net/</projectUrl>
    <iconUrl>http://go.microsoft.com/fwlink/?LinkID=288859</iconUrl>
    <description>Provides the System.ValueTuple structs, which implement the underlying types for tuples in C# and Visual Basic.

Commonly Used Types:
System.ValueTuple
System.ValueTuple&lt;T1&gt;
System.ValueTuple&lt;T1, T2&gt;
System.ValueTuple&lt;T1, T2, T3&gt;
System.ValueTuple&lt;T1, T2, T3, T4&gt;
System.ValueTuple&lt;T1, T2, T3, T4, T5&gt;
System.ValueTuple&lt;T1, T2, T3, T4, T5, T6&gt;
System.ValueTuple&lt;T1, T2, T3, T4, T5, T6, T7&gt;
System.ValueTuple&lt;T1, T2, T3, T4, T5, T6, T7, TRest&gt;
 
30ab651fcb4354552bd4891619a0bdd81e0ebdbf 
When using NuGet 3.x this package requires at least version 3.4.</description>
    <releaseNotes>https://go.microsoft.com/fwlink/?LinkID=799421</releaseNotes>
    <copyright>© Microsoft Corporation.  All rights reserved.</copyright>
    <serviceable>true</serviceable>
    <dependencies>
      <group targetFramework="MonoAndroid1.0" />
      <group targetFramework="MonoTouch1.0" />
      <group targetFramework=".NETFramework4.5" />
      <group targetFramework=".NETFramework4.6.1" />
      <group targetFramework=".NETFramework4.7" />
      <group targetFramework=".NETCoreApp2.0" />
      <group targetFramework=".NETStandard1.0">
        <dependency id="NETStandard.Library" version="1.6.1" />
      </group>
      <group targetFramework=".NETStandard2.0" />
      <group targetFramework=".NETPortable4.5-Profile259" />
      <group targetFramework="UAP10.0.16299" />
      <group targetFramework="Windows8.0" />
      <group targetFramework="WindowsPhone8.0" />
      <group targetFramework="WindowsPhoneApp8.1" />
      <group targetFramework="Xamarin.iOS1.0" />
      <group targetFramework="Xamarin.Mac2.0" />
      <group targetFramework="Xamarin.TVOS1.0" />
      <group targetFramework="Xamarin.WatchOS1.0" />
    </dependencies>
    <frameworkAssemblies>
      <frameworkAssembly assemblyName="mscorlib" targetFramework=".NETFramework4.6.1" />
      <frameworkAssembly assemblyName="mscorlib" targetFramework=".NETFramework4.7" />
      <frameworkAssembly assemblyName="System" targetFramework=".NETFramework4.6.1" />
    </frameworkAssemblies>
  </metadata>
</package>