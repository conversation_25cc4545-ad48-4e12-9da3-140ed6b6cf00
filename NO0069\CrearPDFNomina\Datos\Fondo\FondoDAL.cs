﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.IO;
using Npgsql;
using NpgsqlTypes;
using Entidades.Fondo;
using Datos.Errores;


namespace Datos.Fondo
{
    public class FondoDAL : Conexion.ConexionDAL
    {
        public List<EstadoFondo> consultaGeneralesFondo(DateTime dFechaGeneralesFondo)
        {
            NpgsqlCommand cmd = new NpgsqlCommand();
            NpgsqlDataReader lector;
            cmd.Connection = nConexion;
            cmd.CommandTimeout = 4000;

            List<EstadoFondo> listaEmpleadosFondo = new List<EstadoFondo>();

            try
            {
                // sFecha = dFechaNomina.ToString("yyyy-MM-dd"); 
                cmd.CommandText = "SELECT num_empleado,des_apellidopaterno,des_apellidomaterno,des_nombre,clv_rfc,clv_curp,fec_alta,imp_sueldomensual,num_centro,des_centro,num_seguros,imp_seguros,des_correo FROM fun_obtiene_estado_fondo_empleados(0, @dFechaNomina, 2);";
                //cmd.CommandText = "SELECT numemp, apellidopaterno, apellidomaterno, nombre, rfc, curp, fechaalta, sueldomensual, centro, descripcioncentro,numeroseguros,importeseguro FROM saprecibosnominasgrupo WHERE fechanomina = '@dFechaNomina'";
                cmd.Parameters.AddWithValue("@dFechaNomina", NpgsqlDbType.Date, dFechaGeneralesFondo);

                conectarBaseDatos(3); //Administracion
                lector = cmd.ExecuteReader();

                while (lector.Read())
                {
                    EstadoFondo Fondo = new EstadoFondo();
                    Fondo.iNumEmp = int.Parse(lector["num_empleado"].ToString());
                    Fondo.sApellidoPaterno = lector["des_apellidopaterno"].ToString();
                    Fondo.sApellidoMaterno = lector["des_apellidomaterno"].ToString();
                    Fondo.sNombre = lector["des_nombre"].ToString();
                    Fondo.sRFC = lector["clv_rfc"].ToString();
                    Fondo.sCurp = lector["clv_curp"].ToString();
                    Fondo.dFechaAlta = Convert.ToDateTime(lector["fec_alta"].ToString());
                    Fondo.iSueldoMensual = int.Parse(lector["imp_sueldomensual"].ToString());
                    Fondo.iCentro = int.Parse(lector["num_centro"].ToString());
                    Fondo.sDescripcionCentro = lector["des_centro"].ToString();
                    Fondo.iNumeroSeguro = int.Parse(lector["num_seguros"].ToString());
                    Fondo.iImporteSeguro = Convert.ToDecimal(lector["imp_seguros"]);
                    // Fondo.dTotalAPagar = Convert.ToDecimal(lector["mtotalapagar"]);
                    Fondo.sCorreoEmpleado = lector["des_correo"].ToString();

                    listaEmpleadosFondo.Add(Fondo);
                }
            }
            catch (Exception ex)
            {
                GrabarErrorDAL Error = new GrabarErrorDAL();
                Error.grabarError("Datos.Fondo", "FondoDAL.cs", "consultaGeneralesFondo", "Error al obtener fun_obtiene_estado_fondo_empleados", 3, ex.Message.ToString());
                throw new Exception(ex.Message.ToString());
                
            }
            finally
            {
                cerrarConexion();
            }

            return listaEmpleadosFondo;
        }
    }
}
