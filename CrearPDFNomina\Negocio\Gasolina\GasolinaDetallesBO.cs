﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Datos.Gasolina;
using Datos.Errores;
using Entidades.Gasolina;

namespace Negocios.Gasolina
{
    public class GasolinaDetallesBO
    {
        public List<EstadoDetalleGasolina> obtenerEdoDetalleGasolina(int iNumeroEmpleado, DateTime dFechaCorte)
        {
            GasolinaDetallesDAL GasolinaDetalles = new GasolinaDetallesDAL();
            try
            {
                return GasolinaDetalles.consultarEdoGasolinaDetalle(iNumeroEmpleado, dFechaCorte);
            }
            catch (Exception ex)
            {
                GrabarErrorDAL Error = new GrabarErrorDAL();
                Error.grabarError("Negocios.Gasolina", "GasolinaDetallesBO.cs", "obtenerEdoFondoDetalleEmpleados", "Error al obtener el detalle de gasolina del empleado", 31, ex.Message.ToString());
                throw new Exception(ex.Message.ToString());
            }
        }
    }
}
