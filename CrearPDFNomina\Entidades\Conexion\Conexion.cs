﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Entidades.Conexion
{
    public class Conexion
    {
        private static string _sServidorContabilidad;
        private static string _sBDContabilidad;
        private static string _sUsuarioContabilidad;
        private static string _sClaveContabilidad;
        private static string _sServidorPersonal;
        private static string _sBDPersonal;
        private static string _sUsuarioPersonal;
        private static string _sClavePersonal;
        private static string _sServidorAdministracion;
        private static string _sBDAdministracion;
        private static string _sUsuarioAdministracion;
        private static string _sClaveAdministracion;
        private static string _sServidorWebService;
        private static string _sDescripcionWebService;
        

        public Conexion()
        {
            _sServidorContabilidad = string.Empty;
            _sBDContabilidad = string.Empty;
            _sUsuarioContabilidad = string.Empty;
            _sClaveContabilidad = string.Empty;
            _sServidorPersonal = string.Empty;
            _sBDPersonal = string.Empty;
            _sUsuarioPersonal = string.Empty;
            _sClavePersonal = string.Empty;
            _sServidorAdministracion = string.Empty;
            _sBDAdministracion = string.Empty;
            _sUsuarioAdministracion = string.Empty;
            _sClaveAdministracion = string.Empty;
            _sServidorWebService = string.Empty;
            _sDescripcionWebService = string.Empty;
        }
        //Contabilidad
        public static string sServidorContabilidad
        {
            get
            {
                return _sServidorContabilidad;
            }
            set
            {
                _sServidorContabilidad = value;
            }
        }

        public static string sBDContabilidad
        {
            get
            {
                return _sBDContabilidad;
            }
            set
            {
                _sBDContabilidad = value;
            }
        }

        public static string sUsuarioContabilidad
        {
            get
            {
                return _sUsuarioContabilidad;
            }
            set
            {
                _sUsuarioContabilidad = value;
            }
        }

         public static string sClaveContabilidad
        {
            get
            {
                return _sClaveContabilidad;
            }
            set
            {
                _sClaveContabilidad = value;
            }
        }

        //Personal
         public static string sServidorPersonal
         {
             get
             {
                 return _sServidorPersonal;
             }
             set
             {
                 _sServidorPersonal = value;
             }
         }

         public static string sBDPersonal
         {
             get
             {
                 return _sBDPersonal;
             }
             set
             {
                 _sBDPersonal = value;
             }
         }
        
         public static string sUsuarioPersonal
         {
             get
             {
                 return _sUsuarioPersonal;
             }
             set
             {
                 _sUsuarioPersonal = value;
             }
         }

         public static string sClavePersonal
         {
             get
             {
                 return _sClavePersonal;
             }
             set
             {
                 _sClavePersonal = value;
             }
         }

        //Administracion
         public static string sServidorAdministracion
         {
             get
             {
                 return _sServidorAdministracion;
             }
             set
             {
                 _sServidorAdministracion = value;
             }
         }

         public static string sBDAdministracion
         {
             get
             {
                 return _sBDAdministracion;
             }
             set
             {
                 _sBDAdministracion = value;
             }
         }

         public static string sUsuarioAdministracion
         {
             get
             {
                 return _sUsuarioAdministracion;
             }
             set
             {
                 _sUsuarioAdministracion = value;
             }
         }

         public static string sClavedministracion
         {
             get
             {
                 return _sClaveAdministracion;
             }
             set
             {
                 _sClaveAdministracion = value;
             }
         }

        //WebService
        public static string sServidorWebService
        {
            get
            {
                return _sServidorWebService;
            }
            set
            {
                _sServidorWebService = value;
            }
        }

        public static string sDescripcionWebService
         {
             get
             {
                 return _sDescripcionWebService;
             }
             set
             {
                 _sDescripcionWebService = value;
             }
         }
    }
}
