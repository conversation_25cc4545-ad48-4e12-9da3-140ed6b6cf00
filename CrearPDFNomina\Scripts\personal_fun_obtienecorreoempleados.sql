DROP FUNCTION fun_obtienecorreoempleados();
DROP TYPE type_obtienecorreoempleados;

CREATE TYPE type_obtienecorreoempleados AS
(
	inumeroempleado INTEGER, 
	cemailempleado VARCHAR(100)
);

CREATE OR REPLACE FUNCTION fun_obtienecorreoempleados()
  RETURNS SETOF type_obtienecorreoempleados AS
$BODY$
 DECLARE
	--Autor: 96862629 - Cabanillas Urias Jaime Gael
	--Base de datos: Personal POSTGRESQL
	--Fecha creacion: 23/12/2015
	--Servidor Prueba: ************
	--Servidor Produccion: **********
	--Descripcion: Se encarga de obtener los correos de los empleados para el envio de talones.
	
	rCorreo type_obtienecorreoempleados;
 BEGIN
	FOR rCorreo IN SELECT a.numemp, a.email 
		       FROM sapdatosgeneraleshojas a
		       JOIN sapcatalogoempleados b ON a.numemp = b.numemp AND b.cancelado != '1'
		       WHERE a.email != ''
		       AND a.email LIKE '%@%'
		       AND UPPER(a.email) LIKE '%.COM%'
		       ORDER BY a.numemp
	LOOP
			RETURN NEXT rCorreo;
	END LOOP;
 END;
 $BODY$
  LANGUAGE plpgsql VOLATILE SECURITY DEFINER;

GRANT EXECUTE ON FUNCTION fun_obtienecorreoempleados() TO syspersonal;