<?xml version="1.0" encoding="utf-8"?>
<coreProperties xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:dcterms="http://purl.org/dc/terms/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://schemas.openxmlformats.org/package/2006/metadata/core-properties">
  <dc:creator>Microsoft</dc:creator>
  <dc:description>Provides types for efficient representation and pooling of managed, stack, and native memory segments and sequences of such segments, along with primitives to parse and format UTF-8 encoded text stored in those memory segments.

Commonly Used Types:
System.Span
System.ReadOnlySpan
System.Memory
System.ReadOnlyMemory
System.Buffers.MemoryPool
System.Buffers.ReadOnlySequence
System.Buffers.Text.Utf8Parser
System.Buffers.Text.Utf8Formatter
 
c6cf790234e063b855fcdb50f3fb1b3cfac73275 
When using NuGet 3.x this package requires at least version 3.4.</dc:description>
  <dc:identifier>System.Memory</dc:identifier>
  <version>4.5.3</version>
  <keywords></keywords>
  <lastModifiedBy>NuGet.Packaging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35;Microsoft Windows NT 10.0.14393.0;.NET Framework 4.5</lastModifiedBy>
</coreProperties>