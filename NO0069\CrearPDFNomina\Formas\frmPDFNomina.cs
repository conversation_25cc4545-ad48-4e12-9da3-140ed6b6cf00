﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using Negocios.Correo;
using Negocios.Nomina;
using Negocios.Fondo;
using Negocios.Gasolina;
using Negocios.PDF;
using Negocios.Conexion;
using Entidades.CorreoEmpleado;
using Entidades.Nomina;
using Entidades.Control;
using Negocios.Generales;
using Negocios.Utilidades;
using Negocios.Aguinaldo;

namespace NO0069.Formas
{
    public partial class frmPDFNomina : Form
    {
        public frmPDFNomina()
        {
            InitializeComponent();
        }

        public int iniciarTarea(int TipoCorreo)
        {
           int iCorrecto = 0;
           bool bContinuar = false;
           ConfigurarBO Configurar = new ConfigurarBO();

           if (Configurar.configurarConexion())
           {
                if (depurarTalonNomina(TipoCorreo)) //Se depura la informacion de la tabla mov_talonesempleadosmail
                {
                    if (TipoCorreo == 1) //Nomina
                    {
                       NominaControl NominaControl = new NominaControl();
                       NominaBO Nomina = new NominaBO();

                       NominaControl = consultarFechaNominaControl(); //Se obtienen las fechas de la tabla nomcontrol

                       //Se valida fecha nomina para determinar si se ejecuta proceso de creacion de PDF's 
                       if (NominaControl.dFechaNomina != Convert.ToDateTime("1900-01-01"))
                       {
                           //ReplicarCorreos();
                           bContinuar = construirPDFNomina(NominaControl.dFechaNomina, TipoCorreo);
                       }

                       //Utilidades
                       if (NominaControl.iGeneraUtilidades == 1)
                       {
                           if (NominaControl.dFechaUtilidades != Convert.ToDateTime("1900-01-01"))
                           {
                               bContinuar = construirPDFUtilidades(NominaControl.dFechaUtilidades, TipoCorreo); 
                           }
                       }

                       //Aguinaldo
                       if (NominaControl.iGeneraAguinaldo == 1)
                       {
                           if (NominaControl.dFechaAguinaldo != Convert.ToDateTime("1900-01-01"))
                           {
                               bContinuar = construirPDFAguinaldo(NominaControl.dFechaAguinaldo, TipoCorreo); 
                           }
                       }
                   }
                   else 
                   {
                       FondoControl FondoControl = new FondoControl();
                       NominaBO Nomina = new NominaBO();

                       FondoControl = consultarFechaFondoControl(TipoCorreo);

                       if (FondoControl.dFechaCorteFondo != Convert.ToDateTime("1900-01-01"))
                       {
                           if (TipoCorreo == 2 || TipoCorreo == 3)   //Edo Cta Fdo y Gasolina
                           {
                               //Generar el PDF
                               bContinuar = construirPDFFondo(FondoControl.dFechaCorteFondo, TipoCorreo);
                           }
                       }
                   }
               }
            }
            if(bContinuar == false)
            {
                iCorrecto = 1; //Fallo generación
            }

            return iCorrecto;
        }

        private bool depurarTalonNomina(int iTipo)
        {
            bool bContinuar = false; 
            NominaBO Nomina = new NominaBO();

            try
            {
                bContinuar = Nomina.depurarTalonNomina(iTipo);
            }
            catch (Exception ex)
            {
                MessageBox.Show("Error al depurar informacion del talon de la nomina: " + ex.Message.ToString() + "", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }

            return bContinuar;
        }

        private bool configurarConexion()
        {
            bool bContinuar = false;
            
            ConfigurarBO Configurar = new ConfigurarBO();
            try 
            {
                bContinuar = Configurar.configurarConexion();
            }
            catch(Exception ex)
            {
                MessageBox.Show("Error al configurar conexion "+ex.Message.ToString()+"", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }

            return bContinuar;
        }

        private void frmPDFNomina_Load(object sender, EventArgs e)
        {
        }

        private NominaControl consultarFechaNominaControl()
        {
            NominaControl NominaControl = new NominaControl();
            GeneralesBO Generales = new GeneralesBO();

            try
            {
                NominaControl = Generales.obtenerFechaNominaControl();
            }
            catch (Exception ex)
            {
                MessageBox.Show("Error al consultar la fecha de Nomina control" + ex.Message.ToString() + "", "PDF Nomina", MessageBoxButtons.OK, MessageBoxIcon.Error); 
                throw new Exception(ex.Message.ToString());
            }

            return NominaControl;
        }

        private FondoControl consultarFechaFondoControl(int iMovimiento)
        {
            FondoControl FondoControl = new FondoControl();
            GeneralesBO Generales = new GeneralesBO();

            try
            {
                FondoControl = Generales.obtenerFechaControlFondo(iMovimiento);
            }
            catch (Exception ex)
            {
                MessageBox.Show("Error al consultar la fecha de control de fondo" + ex.Message.ToString() + "", "PDF Fondo de Retiro", MessageBoxButtons.OK, MessageBoxIcon.Error);
                throw new Exception(ex.Message.ToString());
            }

            return FondoControl;
        }

        private void ReplicarCorreos()
        {
            bool bReplico = false;

            CatalogoCorreoBO ReplicarCorreos = new CatalogoCorreoBO();

            try
            {
                bReplico = ReplicarCorreos.procesoReplicaCorreos();

                if (!bReplico)
                {
                    MessageBox.Show("No existen registros a replicar", "PDF Nomina", MessageBoxButtons.OK ,MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("Error al replicar correos de empleados", "PDF Nomina", MessageBoxButtons.OK, MessageBoxIcon.Error); 
                throw new Exception(ex.Message.ToString()); 
            }
        }

        private bool construirPDFUtilidades(DateTime dFechaUtilidades, int iTipo)
        {
            UtilidadesBO Utilidades = new UtilidadesBO();
            bool bGenero = false;

            try
            {
                bGenero = Utilidades.construirPDF(dFechaUtilidades, iTipo);
            }
            catch (Exception ex)
            {
                MessageBox.Show("Error al construir PDF utilidades" + ex.Message.ToString() + "", "PDF Utilidades", MessageBoxButtons.OK, MessageBoxIcon.Error); 
                throw new Exception(ex.Message.ToString());
            }

            return bGenero;
        }

        private bool construirPDFAguinaldo(DateTime dFechaAguinaldo, int iTipo)
        {
            AguinaldoBO Aguinaldo = new AguinaldoBO();
            bool bGenero = false;

            try
            {
                bGenero = Aguinaldo.construirPDF(dFechaAguinaldo, iTipo);
            }
            catch (Exception ex)
            {
                MessageBox.Show("Error al construir PDF Aguinaldo" + ex.Message.ToString() + "", "PDF Aguinaldo", MessageBoxButtons.OK, MessageBoxIcon.Error); 
                throw new Exception(ex.Message.ToString());
            }

            return bGenero;
        }

        private bool construirPDFNomina(DateTime dFechaNomina, int iTipo)
        {
            NominaBO Nomina = new NominaBO();
            bool bGenero = false; 

            try
            {
                bGenero = Nomina.construirPDF(dFechaNomina, iTipo);

                if (!bGenero)
                {
                    MessageBox.Show("No existen registros para la generacion de la nomina", "PDF Nomina");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("Error al construir PDF Nomina" + ex.Message.ToString() + "", "PDF Nomina", MessageBoxButtons.OK, MessageBoxIcon.Error); 
                //throw new Exception(ex.Message.ToString()); 
            }

            return bGenero;
        }
        private bool construirPDFFondo(DateTime dFechaFondo, int iTipo)
        {
            FondoBO Fondo = new FondoBO();
            GasolinaBO Gasolina = new GasolinaBO();
            bool bGenero = false;

            try
            {
                if (iTipo == 2)
                {
                    bGenero = Fondo.construirPDF(dFechaFondo, iTipo);
                }
                else if (iTipo == 3)
                {
                    bGenero = Gasolina.construirPDF(dFechaFondo, iTipo);
                }

                if (!bGenero)
                {
                    MessageBox.Show("No existen registros para la generacion de la nomina", "PDF Nomina");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("Error al construir PDF Fondo o Gasolina" + ex.Message.ToString() + "", "PDF Fondo", MessageBoxButtons.OK, MessageBoxIcon.Error); 
                throw new Exception(ex.Message.ToString());
            }

            return bGenero;
        }
    }
}
