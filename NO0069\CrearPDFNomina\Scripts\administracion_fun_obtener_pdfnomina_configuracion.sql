-- Function: fun_obtener_pdfnomina_configuracion(integer)

-- DROP FUNCTION fun_obtener_pdfnomina_configuracion(integer);
CREATE OR REPLACE FUNCTION fun_obtener_pdfnomina_configuracion(integer)
  RETURNS TABLE( num_empresa integer, des_nombre_archivo text, num_ancho integer, num_alto integer, des_autor text) AS
$BODY$
  DECLARE
	iduEmpresa ALIAS FOR $1;
BEGIN
		RETURN QUERY
		SELECT  
			COALESCE(cpncdf.idu_empresa, cpnc.idu_empresa),
			COALESCE(cpncdf.des_ruta_logo, cpnc.des_ruta_logo),
			COALESCE(cpncdf.num_ancho, cpnc.num_ancho), 
			COALESCE(cpncdf.num_alto, cpnc.num_alto), 
			COALESCE(cpncdf.des_autor, cpnc.des_autor)
		FROM cat_pdfnomina_configuraciones cpnc
		left join cat_pdfnomina_configuraciones cpncdf ON cpncdf.idu_empresa = iduEmpresa AND cpnc.opc_estatus = b'1'
		WHERE cpnc.idu_empresa = 1;
END;
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100
  ROWS 1000;
ALTER FUNCTION fun_obtener_pdfnomina_configuracion(integer)
  OWNER TO syspruebasadmon;