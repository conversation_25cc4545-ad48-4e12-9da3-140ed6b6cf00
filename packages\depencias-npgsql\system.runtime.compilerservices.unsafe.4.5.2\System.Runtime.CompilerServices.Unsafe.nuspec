﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2013/05/nuspec.xsd">
  <metadata minClientVersion="2.12">
    <id>System.Runtime.CompilerServices.Unsafe</id>
    <version>4.5.2</version>
    <title>System.Runtime.CompilerServices.Unsafe</title>
    <authors>Microsoft</authors>
    <owners>microsoft,dotnetframework</owners>
    <requireLicenseAcceptance>false</requireLicenseAcceptance>
    <licenseUrl>https://github.com/dotnet/corefx/blob/master/LICENSE.TXT</licenseUrl>
    <projectUrl>https://dot.net/</projectUrl>
    <iconUrl>http://go.microsoft.com/fwlink/?LinkID=288859</iconUrl>
    <description>Provides the System.Runtime.CompilerServices.Unsafe class, which provides generic, low-level functionality for manipulating pointers.

Commonly Used Types:
System.Runtime.CompilerServices.Unsafe
 
02b11eeee1fbc5f3ef43a1452fe07efd25fa1715 
When using NuGet 3.x this package requires at least version 3.4.</description>
    <releaseNotes>https://go.microsoft.com/fwlink/?LinkID=799421</releaseNotes>
    <copyright>© Microsoft Corporation. All rights reserved.</copyright>
    <serviceable>true</serviceable>
    <dependencies>
      <group targetFramework=".NETFramework4.5" />
      <group targetFramework=".NETCoreApp2.0" />
      <group targetFramework=".NETStandard1.0">
        <dependency id="System.Runtime" version="4.3.0" />
      </group>
      <group targetFramework=".NETStandard2.0" />
      <group targetFramework=".NETPortable4.5-Profile259" />
      <group targetFramework="Windows8.0" />
      <group targetFramework="WindowsPhone8.0" />
      <group targetFramework="WindowsPhoneApp8.1" />
    </dependencies>
  </metadata>
</package>