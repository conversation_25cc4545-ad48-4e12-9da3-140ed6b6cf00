﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2013/05/nuspec.xsd">
  <metadata minClientVersion="2.8.6">
    <id>System.Numerics.Vectors</id>
    <version>4.4.0</version>
    <title>System.Numerics.Vectors</title>
    <authors>Microsoft</authors>
    <owners>microsoft,dotnetframework</owners>
    <requireLicenseAcceptance>false</requireLicenseAcceptance>
    <licenseUrl>https://github.com/dotnet/corefx/blob/master/LICENSE.TXT</licenseUrl>
    <projectUrl>https://dot.net/</projectUrl>
    <iconUrl>http://go.microsoft.com/fwlink/?LinkID=288859</iconUrl>
    <description>Provides hardware-accelerated numeric types, suitable for high-performance processing and graphics applications.

Commonly Used Types:
System.Numerics.Matrix3x2
System.Numerics.Matrix4x4
System.Numerics.Plane
System.Numerics.Quaternion
System.Numerics.Vector2
System.Numerics.Vector3
System.Numerics.Vector4
System.Numerics.Vector
System.Numerics.Vector&lt;T&gt;
 
8321c729934c0f8be754953439b88e6e1c120c24</description>
    <releaseNotes>https://go.microsoft.com/fwlink/?LinkID=799421</releaseNotes>
    <copyright>© Microsoft Corporation.  All rights reserved.</copyright>
    <serviceable>true</serviceable>
    <dependencies>
      <group targetFramework="MonoAndroid1.0" />
      <group targetFramework="MonoTouch1.0" />
      <group targetFramework=".NETFramework4.6" />
      <group targetFramework=".NETCoreApp2.0" />
      <group targetFramework=".NETStandard1.0">
        <dependency id="NETStandard.Library" version="1.6.1" />
      </group>
      <group targetFramework=".NETStandard2.0" />
      <group targetFramework=".NETPortable4.5-Profile259" />
      <group targetFramework="Xamarin.iOS1.0" />
      <group targetFramework="Xamarin.Mac2.0" />
      <group targetFramework="Xamarin.TVOS1.0" />
      <group targetFramework="Xamarin.WatchOS1.0" />
    </dependencies>
    <frameworkAssemblies>
      <frameworkAssembly assemblyName="mscorlib" targetFramework=".NETFramework4.6" />
      <frameworkAssembly assemblyName="System.Numerics" targetFramework=".NETFramework4.6" />
    </frameworkAssemblies>
  </metadata>
</package>