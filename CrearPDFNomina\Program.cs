﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;

namespace NO0069
{
    static class Program
    {
        /// <summary>
        /// Punto de entrada principal para la aplicación.
        /// </summary>
        [STAThread]
        static int Main(string[] TipoCorreo)
        {
            //Parametro de entrada para el tipo de funcion a ejecutar
            //1.- Nomina
            //2.- Edo Cta Fdo
            //3.- Edo Cta Gasolina
            int _TipoCorreo = Convert.ToInt32(TipoCorreo[0]); //NOTA: AQUI SE DESBORDARA SI SE EJECUTA EL EXE SIN ARGUMENTOS YA QUE NO PUEDE CONVERTIR "" A ENTERO.
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            Formas.frmPDFNomina frm = new Formas.frmPDFNomina();
            int iContinua = frm.iniciarTarea(_TipoCorreo);
            //Application.Run(new Formas.frmPDFNomina(iTipoConexion));

            return iContinua;
        }
    }
}
