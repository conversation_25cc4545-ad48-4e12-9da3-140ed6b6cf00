﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Entidades.PDFNomina
{
    public class PDFNominaConfiguracion
    {
        private int _iNumEmpresa;
        private string _sNombreArchivo;
        private int _iAncho;
        private int _iAlto;
        private string _sAutor;
			

        public PDFNominaConfiguracion()
        {
            _iNumEmpresa = 0;
            _sNombreArchivo = "";
            _iAncho = 0;
            _iAlto = 0;
            _sAutor = "";
        }

        public int iNumEmpresa
        {
            get
            {
                return _iNumEmpresa;
            }
            set
            {
                _iNumEmpresa = value;
            }
        }

        public string sNombreArchivo
        {
            get
            {
                return _sNombreArchivo;
            }
            set
            {
                _sNombreArchivo = value;
            }
        }
        public int iAncho
        {
            get
            {
                return _iAncho;
            }
            set
            {
                _iAncho = value;
            }
        }
        public int iAlto
        {
            get
            {
                return _iAlto;
            }
            set
            {
                _iAlto = value;
            }
        }
        public string sAutor
        {
            get
            {
                return _sAutor;
            }
            set
            {
                _sAutor = value;
            }
        }
    }
}
