﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Entidades.CorreoEmpleado;
using Datos.Correo;
using Datos.Generales;
using Datos.Errores;

namespace Negocios.Correo
{
    public class CatalogoCorreoBO
    {
        public bool consultarFlagReplica()
        {
            bool bContinuar = false;
            string sValor = string.Empty;
            GeneralesDAL general = new GeneralesDAL();

            sValor = "0"; //general.consultarValorLlave("FlagReplicaCorreos"); Se deja de utilizar archivo app.config

            if (sValor == "0")
            {
                bContinuar = false;
            }
            else if (sValor == "1")
            {
                bContinuar = true;
            }

            return bContinuar;
        }

        public bool procesoReplicaCorreos()
        {
            int iContador = 0;
            bool bReplico = true;

            try
            {
                if (consultarFlagReplica())
                {
                    List<CatalogoCorreo> listaCorreos = consultarCorreosAReplicar();

                    for (int i = 0; i < listaCorreos.LongCount(); i++)
                    {
                        iContador++;
                        guardarCorreoObtenido(listaCorreos[i].iNumEmpleado, listaCorreos[i].sCorreoEmpleado, listaCorreos[i].dFechaCaptura);
                    }

                    if (iContador == 0)
                    {
                        bReplico = false;
                    }
                }
            }
            catch (Exception ex)
            {
                GrabarErrorDAL Error = new GrabarErrorDAL();
                Error.grabarError("Negocios.Correo", "CatalogoCorreoBO.cs", "procesoReplicaCorreos", "Error al procesar replica de correos", 19, ex.Message.ToString());
                throw new Exception(ex.Message.ToString());
            }

            return bReplico; 
        }

        private List<CatalogoCorreo> consultarCorreosAReplicar()
        {
            CatalogoCorreoDAL Correo = new CatalogoCorreoDAL();

            try
            {
                return Correo.consultarCorreos();
            }
            catch (Exception ex)
            {
                GrabarErrorDAL Error = new GrabarErrorDAL();
                Error.grabarError("Negocios.Correo", "CatalogoCorreoBO.cs", "consultarCorreosAReplicar", "Error al consultar los correos a replicar", 20, ex.Message.ToString());
                throw new Exception(ex.Message.ToString());
            }
        }

        public int guardarCorreoObtenido(int iNumeroEmpleado, string sCorreo, DateTime dFechaCaptura)
        {
            CatalogoCorreoDAL Correo = new CatalogoCorreoDAL();

            try
            {
                return Correo.replicarCorreosEmpleados(iNumeroEmpleado, sCorreo, dFechaCaptura);
            }
            catch (Exception ex)
            {
                GrabarErrorDAL Error = new GrabarErrorDAL();
                Error.grabarError("Negocios.Correo", "CatalogoCorreoBO.cs", "guardarCorreoObtenido", "Error al guardar en replica correos", 21, ex.Message.ToString());
                throw new Exception(ex.Message.ToString());
            }
        }
    }
}
