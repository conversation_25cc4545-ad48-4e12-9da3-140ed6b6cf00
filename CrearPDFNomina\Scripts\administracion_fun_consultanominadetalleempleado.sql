-- Function: fun_consultanominadetalleempleado(integer, date, integer)

-- DROP FUNCTION fun_consultanominadetalleempleado(integer, date, integer);

CREATE OR REPLACE FUNCTION fun_consultanominadetalleempleado(integer, date, integer)
  RETURNS SETOF type_consultanominadetalleempleado AS
$BODY$
DECLARE
	--Autor: 96862629 - Cabanillas Urias Jaime <PERSON> 
	--BD: Administracion PostgreSQL
	--FECHA: 08/08/2016
	--SERVIDOR PRUEBAS: ************
	--SERVIDOR PRODUCCION: **********
	--Descripcion: Se genera funcion para obtener el detalle de la nomina del empleado.
	
	iNumEmp 		ALIAS FOR $1; 
	dFechaNomina	ALIAS FOR $2;
	iEmpresa		ALIAS FOR $3;
	rEmpleado    	type_consultanominadetalleempleado;
BEGIN 
	FOR rEmpleado IN SELECT tipomovimiento, descripcionmovimiento, percepciondeduccion, TO_CHAR(importe/100.00,'LFM9,999,999.00') AS importe
			 FROM sapdetallerecibosnominagrupo
			 WHERE numemp = iNumEmp 
			 AND fechanomina = dFechaNomina 
			 AND empresa = iEmpresa
			 --ORDER BY orden
			 ORDER BY bloque asc, orden asc
	LOOP
		RETURN NEXT rEmpleado;
	END LOOP;
END;
$BODY$
  LANGUAGE plpgsql VOLATILE SECURITY DEFINER;
  
GRANT EXECUTE ON FUNCTION fun_consultanominadetalleempleado(integer, date, integer) TO sysgenexus;