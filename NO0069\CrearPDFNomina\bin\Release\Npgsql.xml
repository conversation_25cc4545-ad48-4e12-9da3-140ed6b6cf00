<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Npgsql</name>
    </assembly>
    <members>
        <member name="T:NpgsqlTypes.ArrayNativeToBackendTypeConverter">
            <summary>
            Handles serialisation of .NET array or IEnumeration to pg format.
            Arrays of arrays, enumerations of enumerations, arrays of enumerations etc.
            are treated as multi-dimensional arrays (in much the same manner as an array of arrays
            is used to emulate multi-dimensional arrays in languages that lack native support for them).
            If such an enumeration of enumerations is "jagged" (as opposed to rectangular, cuboid,
            hypercuboid, hyperhypercuboid, etc) then this class will "correctly" serialise it, but pg
            will raise an error as it doesn't allow jagged arrays.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.ArrayNativeToBackendTypeConverter.#ctor(NpgsqlTypes.NpgsqlNativeTypeInfo)">
            <summary>
            Create an ArrayNativeToBackendTypeConverter with the element converter passed
            </summary>
            <param name="elementConverter">The <see cref="T:NpgsqlTypes.NpgsqlNativeTypeInfo"/> that would be used to serialise the element type.</param>
        </member>
        <member name="M:NpgsqlTypes.ArrayNativeToBackendTypeConverter.ArrayToArrayText(NpgsqlTypes.NpgsqlNativeTypeInfo,System.Object,System.Boolean,NpgsqlTypes.NativeToBackendTypeConverterOptions,System.Boolean)">
            <summary>
            Serialise the enumeration or array.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.ArrayNativeToBackendTypeConverter.ArrayToArrayBinary(NpgsqlTypes.NpgsqlNativeTypeInfo,System.Object,NpgsqlTypes.NativeToBackendTypeConverterOptions)">
            <summary>
            Convert a System.Array to PG binary format.
            Write the array header and prepare to write array data to the stream.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.ArrayNativeToBackendTypeConverter.WriteBinaryArrayData(NpgsqlTypes.NpgsqlNativeTypeInfo,System.Array,NpgsqlTypes.NativeToBackendTypeConverterOptions,System.IO.MemoryStream,System.Int32,System.Int32[])">
            <summary>
            Append all array data to the binary stream.
            </summary>
        </member>
        <member name="T:NpgsqlTypes.ArrayBackendToNativeTypeConverter">
            <summary>
            Handles parsing of pg arrays into .NET arrays.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.ArrayBackendToNativeTypeConverter.TokenEnumeration(System.String)">
            <summary>
            Takes a string representation of a pg 1-dimensional array
            (or a 1-dimensional row within an n-dimensional array)
            and allows enumeration of the string represenations of each items.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.ArrayBackendToNativeTypeConverter.ArrayChunkEnumeration(System.String)">
            <summary>
            Takes a string representation of a pg n-dimensional array
            and allows enumeration of the string represenations of the next
            lower level of rows (which in turn can be taken as (n-1)-dimensional arrays.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.ArrayBackendToNativeTypeConverter.RecursiveArrayListEnumeration(System.Collections.ArrayList)">
            <summary>
            Takes an ArrayList which may be an ArrayList of ArrayLists, an ArrayList of ArrayLists of ArrayLists
            and so on and enumerates the items that aren't ArrayLists (the leaf nodes if we think of the ArrayList
            passed as a tree). Simply uses the ArrayLists' own IEnumerators to get that of the next,
            pushing them onto a stack until we hit something that isn't an ArrayList.
            <param name="list"><see cref="T:System.Collections.ArrayList">ArrayList</see> to enumerate</param>
            <returns><see cref="T:System.Collections.IEnumerable">IEnumerable</see></returns>
            </summary>
        </member>
        <member name="M:NpgsqlTypes.ArrayBackendToNativeTypeConverter.#ctor(NpgsqlTypes.NpgsqlBackendTypeInfo)">
            <summary>
            Create a new ArrayBackendToNativeTypeConverter
            </summary>
            <param name="elementConverter"><see cref="T:NpgsqlTypes.NpgsqlBackendTypeInfo"/> for the element type.</param>
        </member>
        <member name="M:NpgsqlTypes.ArrayBackendToNativeTypeConverter.ArrayTextToArray(NpgsqlTypes.NpgsqlBackendTypeInfo,System.Byte[],System.Int16,System.Int32)">
            <summary>
            Creates an array from pg text representation.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.ArrayBackendToNativeTypeConverter.ToArrayList(NpgsqlTypes.NpgsqlBackendTypeInfo,System.String,System.Int16,System.Int32)">
            <summary>
            Creates an array list from pg represenation of an array.
            Multidimensional arrays are treated as ArrayLists of ArrayLists
            </summary>
        </member>
        <member name="M:NpgsqlTypes.ArrayBackendToNativeTypeConverter.ToArray(System.Collections.ArrayList,System.Type)">
            <summary>
            Creates an n-dimensional array from an ArrayList of ArrayLists or
            a 1-dimensional array from something else.
            </summary>
            <param name="list"><see cref="T:System.Collections.ArrayList"/> to convert</param>
            <param name="elementType">Type of the elements in the list</param>
            <returns><see cref="T:System.Array"/> produced.</returns>
        </member>
        <member name="M:NpgsqlTypes.ArrayBackendToNativeTypeConverter.ArrayBinaryToArray(NpgsqlTypes.NpgsqlBackendTypeInfo,System.Byte[],System.Int32,System.Int32)">
            <summary>
            Creates an n-dimensional System.Array from PG binary representation.
            This function reads the array header and sets up an n-dimensional System.Array object to hold its data.
            PopulateArrayFromBinaryArray() is then called to carry out array population.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.ArrayBackendToNativeTypeConverter.PopulateArrayFromBinaryArray(NpgsqlTypes.NpgsqlBackendTypeInfo,System.Byte[],System.Int32,System.Int32,System.Int32@,System.Int32[],System.Int32[],System.Int32,System.Array,System.Int32[])">
            <summary>
            Recursively populates an array from PB binary data representation.
            </summary>
        </member>
        <member name="T:NpgsqlTypes.ArrayBackendToNativeTypeConverter.IntSetIterator">
             <summary>
             Takes an array of ints and treats them like the limits of a set of counters.
             Retains a matching set of ints that is set to all zeros on the first ++
             On a ++ it increments the "right-most" int. If that int reaches it's
             limit it is set to zero and the one before it is incremented, and so on.
            
             Making this a more general purpose class is pretty straight-forward, but we'll just put what we need here.
             </summary>
        </member>
        <member name="T:NpgsqlTypes.BitString">
            <summary>
            <para>Implements a bit string; a collection of zero or more bits which can each be 1 or 0.</para>
            <para>BitString's behave as a list of bools, though like most strings and unlike most collections the position
            tends to be of as much significance as the value.</para>
            <para>BitStrings are often used as masks, and are commonly cast to and from other values.</para>
            </summary>
        </member>
        <member name="F:NpgsqlTypes.BitString.Empty">
            <summary>
            Represents the empty string.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.BitString.#ctor(System.Collections.Generic.IEnumerable{System.Boolean})">
            <summary>
            Create a BitString from an enumeration of boolean values. The BitString will contain
            those booleans in the order they came in.
            </summary>
            <param name="bits">The boolean values.</param>
        </member>
        <member name="M:NpgsqlTypes.BitString.#ctor(System.Boolean,System.Int32)">
            <summary>
            Creates a BitString filled with a given number of true or false values.
            </summary>
            <param name="value">The value to fill the string with.</param>
            <param name="count">The number of bits to fill.</param>
        </member>
        <member name="M:NpgsqlTypes.BitString.#ctor(System.String)">
            <summary>
            Creats a bitstring from a <see cref="T:System.String">string</see>.
            <param name="str">The <see cref="T:System.String">string to copy from</see>.</param>
            <seealso cref="M:NpgsqlTypes.BitString.Parse(System.String)"/>
            </summary>
        </member>
        <member name="M:NpgsqlTypes.BitString.#ctor(System.Boolean)">
            <summary>
            Creates a single-bit element from a boolean value.
            </summary>
            <param name="boolean">The <see cref="T:System.Boolean">bool</see> value which determines whether
            the bit is 1 or 0.</param>
        </member>
        <member name="M:NpgsqlTypes.BitString.#ctor(System.UInt32)">
            <summary>
            Creates a bitstring from an unsigned integer value. The string will be the shortest required to
            contain the integer (e.g. 1 bit for 0 or 1, 2 for 2 or 3, 3 for 4-7, and so on).
            </summary>
            <param name="integer">The <see cref="T:System.UInt32">integer</see>.</param>
            <remarks>This method is not CLS Compliant, and may not be available to some languages.</remarks>
        </member>
        <member name="M:NpgsqlTypes.BitString.#ctor(System.Int32)">
            <summary>
            Creates a bitstring from an integer value. The string will be the shortest required to
            contain the integer (e.g. 1 bit for 0 or 1, 2 for 2 or 3, 3 for 4-7, and so on).
            </summary>
            <param name="integer">The <see cref="T:System.Int32">integer</see>.</param>
        </member>
        <member name="M:NpgsqlTypes.BitString.IndexOf(System.Boolean)">
            <summary>
            Finds the first instance of a given value
            </summary>
            <param name="item">The value - whether true or false - to search for.</param>
            <returns>The index of the value found, or -1 if none are present.</returns>
        </member>
        <member name="M:NpgsqlTypes.BitString.Contains(System.Boolean)">
            <summary>
            True if there is at least one bit with the value looked for.
            </summary>
            <param name="item">The value - true or false - to detect.</param>
            <returns>True if at least one bit was the same as item, false otherwise.</returns>
        </member>
        <member name="M:NpgsqlTypes.BitString.CopyTo(System.Boolean[],System.Int32)">
            <summary>
            Copies the bitstring to an array of bools.
            </summary>
            <param name="array">The <see cref="T:System.Boolean">boolean</see> array to copy to.</param>
            <param name="arrayIndex">The index in the array to start copying from.</param>
        </member>
        <member name="M:NpgsqlTypes.BitString.GetEnumerator">
            <summary>
            Returns an enumerator that enumerates through the string.
            </summary>
            <returns>The enumerator.</returns>
        </member>
        <member name="M:NpgsqlTypes.BitString.Concat(NpgsqlTypes.BitString)">
            <summary>
            Creats a bitstring by concatenating another onto this one.
            </summary>
            <param name="append">The string to append to this one.</param>
            <returns>The combined strings.</returns>
        </member>
        <member name="M:NpgsqlTypes.BitString.Substring(System.Int32,System.Int32)">
            <summary>
            Returns a substring of this string.
            </summary>
            <param name="start">The position to start from, must be between 0 and the length of the string.</param>
            <param name="length">The length of the string to return, must be greater than zero, and may not be
            so large that the start + length exceeds the bounds of this instance.</param>
            <returns>The Bitstring identified</returns>
        </member>
        <member name="M:NpgsqlTypes.BitString.Substring(System.Int32)">
            <summary>
            Returns a substring of this string.
            </summary>
            <param name="start">The position to start from, must be between 0 and the length of the string,
            the rest of the string is returned.</param>
            <returns>The Bitstring identified</returns>
        </member>
        <member name="M:NpgsqlTypes.BitString.And(NpgsqlTypes.BitString)">
            <summary>
            A logical and between this string and another. The two strings must be the same length.
            </summary>
            <param name="operand">Another BitString to AND with this one.</param>
            <returns>A bitstring with 1 where both BitStrings had 1 and 0 otherwise.</returns>
        </member>
        <member name="M:NpgsqlTypes.BitString.Or(NpgsqlTypes.BitString)">
            <summary>
            A logical or between this string and another. The two strings must be the same length.
            </summary>
            <param name="operand">Another BitString to OR with this one.</param>
            <returns>A bitstring with 1 where either BitString had 1 and 0 otherwise.</returns>
        </member>
        <member name="M:NpgsqlTypes.BitString.Xor(NpgsqlTypes.BitString)">
            <summary>
            A logical xor between this string and another. The two strings must be the same length.
            </summary>
            <param name="operand">Another BitString to XOR with this one.</param>
            <returns>A bitstring with 1 where one BitStrings and the other had 0,
            and 0 where they both had 1 or both had 0.</returns>
        </member>
        <member name="M:NpgsqlTypes.BitString.Not">
            <summary>
            A bitstring that is the logical inverse of this one.
            </summary>
            <returns>A bitstring of the same length as this with 1 where this has 0 and vice-versa.</returns>
        </member>
        <member name="M:NpgsqlTypes.BitString.LShift(System.Int32)">
            <summary>
            Shifts the string operand bits to the left, filling with zeros to produce a
            string of the same length.
            </summary>
            <param name="operand">The number of bits to shift to the left.</param>
            <returns>A left-shifted bitstring.</returns>
            <remarks><para>The behaviour of LShift is closer to what one would expect from dealing
            with PostgreSQL bit-strings than in using the same operations on integers in .NET</para>
            <para>In particular, negative operands result in a right-shift, and operands greater than
            the length of the string will shift it entirely, resulting in a zero-filled string.</para>
            </remarks>
        </member>
        <member name="M:NpgsqlTypes.BitString.RShift(System.Int32)">
            <summary>
            Shifts the string operand bits to the right, filling with zeros to produce a
            string of the same length.
            </summary>
            <param name="operand">The number of bits to shift to the right.</param>
            <returns>A right-shifted bitstring.</returns>
            <remarks><para>The behaviour of RShift is closer to what one would expect from dealing
            with PostgreSQL bit-strings than in using the same operations on integers in .NET</para>
            <para>In particular, negative operands result in a left-shift, and operands greater than
            the length of the string will shift it entirely, resulting in a zero-filled string. It also performs
            a logical shift, rather than an arithmetic shift, so it always sets the vacated bit positions to zero
            (like PostgreSQL and like .NET for unsigned integers but not for signed integers).</para>
            </remarks>
        </member>
        <member name="M:NpgsqlTypes.BitString.Equals(NpgsqlTypes.BitString)">
            <summary>
            Returns true if the this string is identical to the argument passed.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.BitString.CompareTo(NpgsqlTypes.BitString)">
            <summary>
            Compares two strings. Strings are compared as strings, so while 0 being less than 1 will
            mean a comparison between two strings of the same size is the same as treating them as numbers,
            in the case of two strings of differing lengths the comparison starts at the right-most (most significant)
            bit, and if all bits of the shorter string are exhausted without finding a comparison, then the larger
            string is deemed to be greater than the shorter (0010 is greater than 0001 but less than 00100).
            </summary>
            <param name="other">Another string to compare with this one.</param>
            <returns>A value if the two strings are identical, an integer less
            than zero if this is less than the argument, and an integer greater
            than zero otherwise.</returns>
        </member>
        <member name="M:NpgsqlTypes.BitString.CompareTo(System.Object)">
            <summary>
            Compares the string with another object.
            </summary>
            <param name="obj">The object to compare with.</param>
            <returns>If the object is null then this string is considered greater. If the object is another BitString
            then they are compared as in <see cref="M:NpgsqlTypes.BitString.CompareTo(NpgsqlTypes.BitString)">the explicit comparison for BitStrings</see>
            in any other case a <see cref="T:System.ArgumentException"/> is thrown.</returns>
        </member>
        <member name="M:NpgsqlTypes.BitString.Equals(System.Object)">
            <summary>
            Compares this BitString with an object for equality.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.BitString.GetHashCode">
            <summary>
            Returns a code for use in hashing operations.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.BitString.ToString(System.String)">
            <summary>
            Returns a string representation of the BitString.
            </summary>
            <param name="format">
            A string which can contain a letter and optionally a number which sets a minimum size for the string
            returned. In each case using the lower-case form of the letter will result in a lower-case string
            being returned.
            <list type="table">
            <item>
            <term>B</term>
            <description>A string of 1s and 0s.</description>
            </item>
            <item>
            <term>X</term>
            <description>An hexadecimal string (will result in an error unless the string's length is divisible by 4).</description>
            </item>
            <item>
            <term>G</term>
            <description>A string of 1s and 0s in single-quotes preceded by 'B' (Postgres bit string literal syntax).</description>
            </item>
            <term>Y</term>
            <description>An hexadecimal string in single-quotes preceded by 'X' (Postgres bit literal syntax, will result in an error unless the string's length is divisible by 4.</description>
            </list>
            <term>C</term>
            <description>The format produced by format-string "Y" if legal, otherwise that produced by format-string "G".</description>
            <term>E</term>
            <description>The most compact safe representation for Postgres. If single bit will be either a 0 or a 1. Otherwise if it
            can be that produce by format string "Y" it will, otherwise if there are less than 9bits in length it will be that
            produced by format-string "G". For longer strings that cannot be represented in hexadecimal it will be a string
            representing the first part of the string in format "Y" followed by the PostgreSQL concatenation operator, followed
            by the final bits in the format "G". E.g. "X'13DCE'||B'110'"</description>
            If format is empty or null, it is treated as if "B" had been passed (the default repreesentation, and that
            generally used by PostgreSQL for display).
            </param>
            <returns>The formatted string.</returns>
        </member>
        <member name="M:NpgsqlTypes.BitString.ToString">
            <summary>
            Returns a string representation for the Bitstring
            </summary>
            <returns>A string containing '0' and '1' characters.</returns>
        </member>
        <member name="M:NpgsqlTypes.BitString.ToString(System.String,System.IFormatProvider)">
            <summary>
            Returns the same string as <see cref="M:NpgsqlTypes.BitString.ToString(System.String)"/>. formatProvider is ignored.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.BitString.Parse(System.String)">
            <summary>
            Parses a string to produce a BitString. Most formats that can be produced by
            <see cref="M:NpgsqlTypes.BitString.ToString(System.String)"/> can be accepted, but hexadecimal
            can be interpreted with the preceding X' to mark the following characters as
            being hexadecimal rather than binary.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.BitString.op_BitwiseAnd(NpgsqlTypes.BitString,NpgsqlTypes.BitString)">
            <summary>
            Performs a logical AND on the two operands.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.BitString.op_BitwiseOr(NpgsqlTypes.BitString,NpgsqlTypes.BitString)">
            <summary>
            Performs a logcial OR on the two operands.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.BitString.op_ExclusiveOr(NpgsqlTypes.BitString,NpgsqlTypes.BitString)">
            <summary>
            Perofrms a logical EXCLUSIVE-OR on the two operands
            </summary>
        </member>
        <member name="M:NpgsqlTypes.BitString.op_OnesComplement(NpgsqlTypes.BitString)">
            <summary>
            Performs a logical NOT on the operand.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.BitString.op_Addition(NpgsqlTypes.BitString,NpgsqlTypes.BitString)">
            <summary>
            Concatenates the operands.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.BitString.op_LeftShift(NpgsqlTypes.BitString,System.Int32)">
            <summary>
            Left-shifts the string BitString.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.BitString.op_RightShift(NpgsqlTypes.BitString,System.Int32)">
            <summary>
            Right-shifts the string BitString.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.BitString.op_Equality(NpgsqlTypes.BitString,NpgsqlTypes.BitString)">
            <summary>
            Compares the two operands.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.BitString.op_Inequality(NpgsqlTypes.BitString,NpgsqlTypes.BitString)">
            <summary>
            Compares the two operands.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.BitString.op_LessThan(NpgsqlTypes.BitString,NpgsqlTypes.BitString)">
            <summary>
            Compares the two operands.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.BitString.op_GreaterThan(NpgsqlTypes.BitString,NpgsqlTypes.BitString)">
            <summary>
            Compares the two operands.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.BitString.op_LessThanOrEqual(NpgsqlTypes.BitString,NpgsqlTypes.BitString)">
            <summary>
            Compares the two operands.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.BitString.op_GreaterThanOrEqual(NpgsqlTypes.BitString,NpgsqlTypes.BitString)">
            <summary>
            Compares the two operands.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.BitString.ToString(System.Text.Encoding)">
            <summary>
            Interprets the bitstring as a series of bits in an encoded character string,
            encoded according to the Encoding passed, and returns that string.
            The bitstring must contain a whole number of octets(bytes) and also be
            valid according to the Encoding passed.
            </summary>
            <param name="encoding">The <see cref="T:System.Text.Encoding"/> to use in producing the string.</param>
            <returns>The string that was encoded in the BitString.</returns>
        </member>
        <member name="M:NpgsqlTypes.BitString.ToByteEnumerable">
            <summary>
            Interprets the bitstring as a series of octets (bytes) and returns those octets. Fails
            if the Bitstring does not contain a whole number of octets (its length is not evenly
            divisible by 8).
            </summary>
        </member>
        <member name="M:NpgsqlTypes.BitString.ToSByteEnumerable">
            <summary>
            Interprets the bitstring as a series of signed octets (bytes) and returns those octets. Fails
            if the Bitstring does not contain a whole number of octets (its length is not evenly
            divisible by 8).
            <remarks>This method is not CLS-Compliant and may not be available to languages that cannot
            handle signed bytes.</remarks>
            </summary>
        </member>
        <member name="M:NpgsqlTypes.BitString.ToUInt16Enumerable">
            <summary>
            Interprets the bitstring as a series of unsigned 16-bit integers and returns those integers.
            Fails if the Bitstring's length is not evenly divisible by 16.
            <remarks>This method is not CLS-Compliant and may not be available to languages that cannot
            handle unsigned integers.</remarks>
            </summary>
        </member>
        <member name="M:NpgsqlTypes.BitString.ToInt16Enumerable">
            <summary>
            Interprets the bitstring as a series of 16-bit integers and returns those integers.
            Fails if the Bitstring's length is not evenly divisible by 16.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.BitString.ToUInt32Enumerable">
            <summary>
            Interprets the bitstring as a series of unsigned 32-bit integers and returns those integers.
            Fails if the Bitstring's length is not evenly divisible by 32.
            <remarks>This method is not CLS-Compliant and may not be available to languages that cannot
            handle unsigned integers.</remarks>
            </summary>
        </member>
        <member name="M:NpgsqlTypes.BitString.ToInt32Enumerable">
            <summary>
            Interprets the bitstring as a series of signed 32-bit integers and returns those integers.
            Fails if the Bitstring's length is not evenly divisible by 32.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.BitString.ToUInt64Enumerable">
            <summary>
            Interprets the bitstring as a series of unsigned 64-bit integers and returns those integers.
            Fails if the Bitstring's length is not evenly divisible by 64.
            <remarks>This method is not CLS-Compliant and may not be available to languages that cannot
            handle unsigned integers.</remarks>
            </summary>
        </member>
        <member name="M:NpgsqlTypes.BitString.ToInt64Enumerable">
            <summary>
            Interprets the bitstring as a series of signed 64-bit integers and returns those integers.
            Fails if the Bitstring's length is not evenly divisible by 64.
            </summary>
        </member>
        <member name="P:NpgsqlTypes.BitString.Length">
            <summary>
            The length of the string.
            </summary>
        </member>
        <member name="P:NpgsqlTypes.BitString.Item(System.Int32)">
            <summary>
            Retrieves the value of the bit at the given index.
            </summary>
        </member>
        <member name="T:NpgsqlTypes.NpgsqlInterval">
            <summary>
            Represents the PostgreSQL interval datatype.
            <remarks>PostgreSQL differs from .NET in how it's interval type doesn't assume 24 hours in a day
            (to deal with 23- and 25-hour days caused by daylight savings adjustments) and has a concept
            of months that doesn't exist in .NET's <see cref="T:System.TimeSpan"/> class. (Neither datatype
            has any concessions for leap-seconds).
            <para>For most uses just casting to and from TimeSpan will work correctly — in particular,
            the results of subtracting one <see cref="T:System.DateTime"/> or the PostgreSQL date, time and
            timestamp types from another should be the same whether you do so in .NET or PostgreSQL —
            but if the handling of days and months in PostgreSQL is important to your application then you
            should use this class instead of <see cref="T:System.TimeSpan"/>.</para>
            <para>If you don't know whether these differences are important to your application, they
            probably arent! Just use <see cref="T:System.TimeSpan"/> and do not use this class directly ☺</para>
            <para>To avoid forcing unnecessary provider-specific concerns on users who need not be concerned
            with them a call to <see cref="M:System.Data.IDataRecord.GetValue(System.Int32)"/> on a field containing an
            <see cref="T:NpgsqlTypes.NpgsqlInterval"/> value will return a <see cref="T:System.TimeSpan"/> rather than an
            <see cref="T:NpgsqlTypes.NpgsqlInterval"/>. If you need the extra functionality of <see cref="T:NpgsqlTypes.NpgsqlInterval"/>
            then use <see cref="M:Npgsql.NpgsqlDataReader.GetInterval(System.Int32)"/>.</para>
            </remarks>
            <seealso cref="P:NpgsqlTypes.NpgsqlInterval.Ticks"/>
            <seealso cref="M:NpgsqlTypes.NpgsqlInterval.JustifyDays"/>
            <seealso cref="M:NpgsqlTypes.NpgsqlInterval.JustifyMonths"/>
            <seealso cref="M:NpgsqlTypes.NpgsqlInterval.Canonicalize"/>
            </summary>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlInterval.TicksPerMicrosecond">
            <summary>
            Represents the number of ticks (100ns periods) in one microsecond. This field is constant.
            </summary>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlInterval.TicksPerMillsecond">
            <summary>
            Represents the number of ticks (100ns periods) in one millisecond. This field is constant.
            </summary>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlInterval.TicksPerSecond">
            <summary>
            Represents the number of ticks (100ns periods) in one second. This field is constant.
            </summary>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlInterval.TicksPerMinute">
            <summary>
            Represents the number of ticks (100ns periods) in one minute. This field is constant.
            </summary>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlInterval.TicksPerHour">
            <summary>
            Represents the number of ticks (100ns periods) in one hour. This field is constant.
            </summary>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlInterval.TicksPerDay">
            <summary>
            Represents the number of ticks (100ns periods) in one day. This field is constant.
            </summary>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlInterval.HoursPerDay">
            <summary>
            Represents the number of hours in one day (assuming no daylight savings adjustments). This field is constant.
            </summary>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlInterval.DaysPerMonth">
            <summary>
            Represents the number of days assumed in one month if month justification or unjustifcation is performed.
            This is set to 30 for consistency with PostgreSQL. Note that this is means that month adjustments cause
            a year to be taken as 30 &#xd7; 12 = 360 rather than 356/366 days.
            </summary>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlInterval.TicksPerMonth">
            <summary>
            Represents the number of ticks (100ns periods) in one day, assuming 30 days per month. <seealso cref="F:NpgsqlTypes.NpgsqlInterval.DaysPerMonth"/>
            </summary>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlInterval.MonthsPerYear">
            <summary>
            Represents the number of months in a year. This field is constant.
            </summary>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlInterval.MaxValue">
            <summary>
            Represents the maximum <see cref="T:NpgsqlTypes.NpgsqlInterval"/>. This field is read-only.
            </summary>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlInterval.MinValue">
            <summary>
            Represents the minimum <see cref="T:NpgsqlTypes.NpgsqlInterval"/>. This field is read-only.
            </summary>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlInterval.Zero">
            <summary>
            Represents the zero <see cref="T:NpgsqlTypes.NpgsqlInterval"/>. This field is read-only.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlInterval.#ctor(System.Int64)">
            <summary>
            Initializes a new <see cref="T:NpgsqlTypes.NpgsqlInterval"/> to the specified number of ticks.
            </summary>
            <param name="ticks">A time period expressed in 100ns units.</param>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlInterval.#ctor(System.TimeSpan)">
            <summary>
            Initializes a new <see cref="T:NpgsqlTypes.NpgsqlInterval"/> to hold the same time as a <see cref="T:System.TimeSpan"/>
            </summary>
            <param name="timespan">A time period expressed in a <see cref="T:System.TimeSpan"/></param>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlInterval.#ctor(System.Int32,System.Int32,System.Int64)">
            <summary>
            Initializes a new <see cref="T:NpgsqlTypes.NpgsqlInterval"/> to the specified number of months, days
            &amp; ticks.
            </summary>
            <param name="months">Number of months.</param>
            <param name="days">Number of days.</param>
            <param name="ticks">Number of 100ns units.</param>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlInterval.#ctor(System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            Initializes a new <see cref="T:NpgsqlTypes.NpgsqlInterval"/> to the specified number of
            days, hours, minutes &amp; seconds.
            </summary>
            <param name="days">Number of days.</param>
            <param name="hours">Number of hours.</param>
            <param name="minutes">Number of minutes.</param>
            <param name="seconds">Number of seconds.</param>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlInterval.#ctor(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            Initializes a new <see cref="T:NpgsqlTypes.NpgsqlInterval"/> to the specified number of
            days, hours, minutes, seconds &amp; milliseconds.
            </summary>
            <param name="days">Number of days.</param>
            <param name="hours">Number of hours.</param>
            <param name="minutes">Number of minutes.</param>
            <param name="seconds">Number of seconds.</param>
            <param name="milliseconds">Number of milliseconds.</param>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlInterval.#ctor(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            Initializes a new <see cref="T:NpgsqlTypes.NpgsqlInterval"/> to the specified number of
            months, days, hours, minutes, seconds &amp; milliseconds.
            </summary>
            <param name="months">Number of months.</param>
            <param name="days">Number of days.</param>
            <param name="hours">Number of hours.</param>
            <param name="minutes">Number of minutes.</param>
            <param name="seconds">Number of seconds.</param>
            <param name="milliseconds">Number of milliseconds.</param>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlInterval.#ctor(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            Initializes a new <see cref="T:NpgsqlTypes.NpgsqlInterval"/> to the specified number of
            years, months, days, hours, minutes, seconds &amp; milliseconds.
            <para>Years are calculated exactly equivalent to 12 months.</para>
            </summary>
            <param name="years">Number of years.</param>
            <param name="months">Number of months.</param>
            <param name="days">Number of days.</param>
            <param name="hours">Number of hours.</param>
            <param name="minutes">Number of minutes.</param>
            <param name="seconds">Number of seconds.</param>
            <param name="milliseconds">Number of milliseconds.</param>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlInterval.FromTicks(System.Int64)">
            <summary>
            Creates an <see cref="T:NpgsqlTypes.NpgsqlInterval"/> from a number of ticks.
            </summary>
            <param name="ticks">The number of ticks (100ns units) in the interval.</param>
            <returns>A <see cref="M:NpgsqlTypes.NpgsqlInterval.Canonicalize"/>d <see cref="T:NpgsqlTypes.NpgsqlInterval"/> with the given number of ticks.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlInterval.FromMicroseconds(System.Double)">
            <summary>
            Creates an <see cref="T:NpgsqlTypes.NpgsqlInterval"/> from a number of microseconds.
            </summary>
            <param name="micro">The number of microseconds in the interval.</param>
            <returns>A <see cref="M:NpgsqlTypes.NpgsqlInterval.Canonicalize"/>d <see cref="T:NpgsqlTypes.NpgsqlInterval"/> with the given number of microseconds.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlInterval.FromMilliseconds(System.Double)">
            <summary>
            Creates an <see cref="T:NpgsqlTypes.NpgsqlInterval"/> from a number of milliseconds.
            </summary>
            <param name="milli">The number of milliseconds in the interval.</param>
            <returns>A <see cref="M:NpgsqlTypes.NpgsqlInterval.Canonicalize"/>d <see cref="T:NpgsqlTypes.NpgsqlInterval"/> with the given number of milliseconds.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlInterval.FromSeconds(System.Double)">
            <summary>
            Creates an <see cref="T:NpgsqlTypes.NpgsqlInterval"/> from a number of seconds.
            </summary>
            <param name="seconds">The number of seconds in the interval.</param>
            <returns>A <see cref="M:NpgsqlTypes.NpgsqlInterval.Canonicalize"/>d <see cref="T:NpgsqlTypes.NpgsqlInterval"/> with the given number of seconds.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlInterval.FromMinutes(System.Double)">
            <summary>
            Creates an <see cref="T:NpgsqlTypes.NpgsqlInterval"/> from a number of minutes.
            </summary>
            <param name="minutes">The number of minutes in the interval.</param>
            <returns>A <see cref="M:NpgsqlTypes.NpgsqlInterval.Canonicalize"/>d <see cref="T:NpgsqlTypes.NpgsqlInterval"/> with the given number of minutes.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlInterval.FromHours(System.Double)">
            <summary>
            Creates an <see cref="T:NpgsqlTypes.NpgsqlInterval"/> from a number of hours.
            </summary>
            <param name="hours">The number of hours in the interval.</param>
            <returns>A <see cref="M:NpgsqlTypes.NpgsqlInterval.Canonicalize"/>d <see cref="T:NpgsqlTypes.NpgsqlInterval"/> with the given number of hours.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlInterval.FromDays(System.Double)">
            <summary>
            Creates an <see cref="T:NpgsqlTypes.NpgsqlInterval"/> from a number of days.
            </summary>
            <param name="days">The number of days in the interval.</param>
            <returns>A <see cref="M:NpgsqlTypes.NpgsqlInterval.Canonicalize"/>d <see cref="T:NpgsqlTypes.NpgsqlInterval"/> with the given number of days.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlInterval.FromMonths(System.Double)">
            <summary>
            Creates an <see cref="T:NpgsqlTypes.NpgsqlInterval"/> from a number of months.
            </summary>
            <param name="months">The number of months in the interval.</param>
            <returns>A <see cref="M:NpgsqlTypes.NpgsqlInterval.Canonicalize"/>d <see cref="T:NpgsqlTypes.NpgsqlInterval"/> with the given number of months.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlInterval.Add(NpgsqlTypes.NpgsqlInterval)">
            <summary>
            Adds another interval to this instance and returns the result.
            </summary>
            <param name="interval">An <see cref="T:NpgsqlTypes.NpgsqlInterval"/> to add to this instance.</param>
            <returns>An <see cref="T:NpgsqlTypes.NpgsqlInterval"></see> whose values are the sums of the two instances.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlInterval.Subtract(NpgsqlTypes.NpgsqlInterval)">
            <summary>
            Subtracts another interval from this instance and returns the result.
            </summary>
            <param name="interval">An <see cref="T:NpgsqlTypes.NpgsqlInterval"/> to subtract from this instance.</param>
            <returns>An <see cref="T:NpgsqlTypes.NpgsqlInterval"></see> whose values are the differences of the two instances.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlInterval.Negate">
            <summary>
            Returns an <see cref="T:NpgsqlTypes.NpgsqlInterval"/> whose value is the negated value of this instance.
            </summary>
            <returns>An <see cref="T:NpgsqlTypes.NpgsqlInterval"/> whose value is the negated value of this instance.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlInterval.Duration">
            <summary>
            This absolute value of this instance. In the case of some, but not all, components being negative,
            the rules used for justification are used to determine if the instance is positive or negative.
            </summary>
            <returns>An <see cref="T:NpgsqlTypes.NpgsqlInterval"/> whose value is the absolute value of this instance.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlInterval.JustifyDays">
            <summary>
            Equivalent to PostgreSQL's justify_days function.
            </summary>
            <returns>An <see cref="T:NpgsqlTypes.NpgsqlInterval"/> based on this one, but with any hours outside of the range [-23, 23]
            converted into days.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlInterval.UnjustifyDays">
            <summary>
            Opposite to PostgreSQL's justify_days function.
            </summary>
            <returns>An <see cref="T:NpgsqlTypes.NpgsqlInterval"/> based on this one, but with any days converted to multiples of ±24hours.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlInterval.JustifyMonths">
            <summary>
            Equivalent to PostgreSQL's justify_months function.
            </summary>
            <returns>An <see cref="T:NpgsqlTypes.NpgsqlInterval"/> based on this one, but with any days outside of the range [-30, 30]
            converted into months.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlInterval.UnjustifyMonths">
            <summary>
            Opposite to PostgreSQL's justify_months function.
            </summary>
            <returns>An <see cref="T:NpgsqlTypes.NpgsqlInterval"/> based on this one, but with any months converted to multiples of ±30days.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlInterval.JustifyInterval">
            <summary>
            Equivalent to PostgreSQL's justify_interval function.
            </summary>
            <returns>An <see cref="T:NpgsqlTypes.NpgsqlInterval"/> based on this one,
            but with any months converted to multiples of ±30days
            and then with any days converted to multiples of ±24hours</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlInterval.UnjustifyInterval">
            <summary>
            Opposite to PostgreSQL's justify_interval function.
            </summary>
            <returns>An <see cref="T:NpgsqlTypes.NpgsqlInterval"/> based on this one, but with any months converted to multiples of ±30days and then any days converted to multiples of ±24hours;</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlInterval.Canonicalize">
            <summary>
            Produces a canonical NpgslInterval with 0 months and hours in the range of [-23, 23].
            <remarks>
            <para>
            While the fact that for many purposes, two different <see cref="T:NpgsqlTypes.NpgsqlInterval"/> instances could be considered
            equivalent (e.g. one with 2days, 3hours and one with 1day 27hours) there are different possible canonical forms.
            </para><para>
            E.g. we could move all excess hours into days and all excess days into months and have the most readable form,
            or we could move everything into the ticks and have the form that allows for the easiest arithmetic) the form
            chosen has two important properties that make it the best choice.
            </para><para>First, it is closest two how
            <see cref="T:System.TimeSpan"/> objects are most often represented. Second, it is compatible with results of many
            PostgreSQL functions, particularly with age() and the results of subtracting one date, time or timestamp from
            another.
            </para>
            <para>Note that the results of casting a <see cref="T:System.TimeSpan"/> to <see cref="T:NpgsqlTypes.NpgsqlInterval"/> is
            canonicalised.</para>
            </remarks>
            </summary>
            <returns>An <see cref="T:NpgsqlTypes.NpgsqlInterval"/> based on this one, but with months converted to multiples of ±30days and with any hours outside of the range [-23, 23]
            converted into days.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlInterval.op_Implicit(System.TimeSpan)~NpgsqlTypes.NpgsqlInterval">
            <summary>
            Implicit cast of a <see cref="T:System.TimeSpan"/> to an <see cref="T:NpgsqlTypes.NpgsqlInterval"/>
            </summary>
            <param name="timespan">A <see cref="T:System.TimeSpan"/></param>
            <returns>An eqivalent, canonical, <see cref="T:NpgsqlTypes.NpgsqlInterval"/>.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlInterval.op_Explicit(NpgsqlTypes.NpgsqlInterval)~System.TimeSpan">
            <summary>
            Implicit cast of an <see cref="T:NpgsqlTypes.NpgsqlInterval"/> to a <see cref="T:System.TimeSpan"/>.
            </summary>
            <param name="interval">A <see cref="T:NpgsqlTypes.NpgsqlInterval"/>.</param>
            <returns>An equivalent <see cref="T:System.TimeSpan"/>.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlInterval.Equals(NpgsqlTypes.NpgsqlInterval)">
            <summary>
            Returns true if another <see cref="T:NpgsqlTypes.NpgsqlInterval"/> is exactly the same as this instance.
            </summary>
            <param name="other">An <see cref="T:NpgsqlTypes.NpgsqlInterval"/> for comparison.</param>
            <returns>true if the two <see cref="T:NpgsqlTypes.NpgsqlInterval"/> instances are exactly the same,
            false otherwise.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlInterval.Equals(System.Object)">
            <summary>
            Returns true if another object is an <see cref="T:NpgsqlTypes.NpgsqlInterval"/>, that is exactly the same as
            this instance
            </summary>
            <param name="obj">An <see cref="T:System.Object"/> for comparison.</param>
            <returns>true if the argument is an <see cref="T:NpgsqlTypes.NpgsqlInterval"/> and is exactly the same
            as this one, false otherwise.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlInterval.Compare(NpgsqlTypes.NpgsqlInterval,NpgsqlTypes.NpgsqlInterval)">
            <summary>
            Compares two <see cref="T:NpgsqlTypes.NpgsqlInterval"/> instances.
            </summary>
            <param name="x">The first <see cref="T:NpgsqlTypes.NpgsqlInterval"/>.</param>
            <param name="y">The second <see cref="T:NpgsqlTypes.NpgsqlInterval"/>.</param>
            <returns>0 if the two are equal or equivalent. A value greater than zero if x is greater than y,
            a value less than zero if x is less than y.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlInterval.GetHashCode">
            <summary>
            A hash code suitable for uses with hashing algorithms.
            </summary>
            <returns>An signed integer.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlInterval.CompareTo(NpgsqlTypes.NpgsqlInterval)">
            <summary>
            Compares this instance with another/
            </summary>
            <param name="other">An <see cref="T:NpgsqlTypes.NpgsqlInterval"/> to compare this with.</param>
            <returns>0 if the instances are equal or equivalent. A value less than zero if
            this instance is less than the argument. A value greater than zero if this instance
            is greater than the instance.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlInterval.CompareTo(System.Object)">
            <summary>
            Compares this instance with another/
            </summary>
            <param name="other">An object to compare this with.</param>
            <returns>0 if the argument is an <see cref="T:NpgsqlTypes.NpgsqlInterval"/> and the instances are equal or equivalent.
            A value less than zero if the argument is an <see cref="T:NpgsqlTypes.NpgsqlInterval"/> and
            this instance is less than the argument.
            A value greater than zero if the argument is an <see cref="T:NpgsqlTypes.NpgsqlInterval"/> and this instance
            is greater than the instance.</returns>
            A value greater than zero if the argument is null.
            <exception cref="T:System.ArgumentException">The argument is not an <see cref="T:NpgsqlTypes.NpgsqlInterval"/>.</exception>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlInterval.Parse(System.String)">
            <summary>
            Parses a <see cref="T:System.String"/> and returns a <see cref="T:NpgsqlTypes.NpgsqlInterval"/> instance.
            Designed to use the formats generally returned by PostgreSQL.
            </summary>
            <param name="str">The <see cref="T:System.String"/> to parse.</param>
            <returns>An <see cref="T:NpgsqlTypes.NpgsqlInterval"/> represented by the argument.</returns>
            <exception cref="T:System.ArgumentNullException">The string was null.</exception>
            <exception cref="T:System.OverflowException">A value obtained from parsing the string exceeded the values allowed for the relevant component.</exception>
            <exception cref="T:System.FormatException">The string was not in a format that could be parsed to produce an <see cref="T:NpgsqlTypes.NpgsqlInterval"/>.</exception>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlInterval.TryParse(System.String,NpgsqlTypes.NpgsqlInterval@)">
            <summary>
            Attempt to parse a <see cref="T:System.String"/> to produce an <see cref="T:NpgsqlTypes.NpgsqlInterval"/>.
            </summary>
            <param name="str">The <see cref="T:System.String"/> to parse.</param>
            <param name="result">(out) The <see cref="T:NpgsqlTypes.NpgsqlInterval"/> produced, or <see cref="F:NpgsqlTypes.NpgsqlInterval.Zero"/> if the parsing failed.</param>
            <returns>true if the parsing succeeded, false otherwise.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlInterval.ToString">
            <summary>
            Create a <see cref="T:System.String"/> representation of the <see cref="T:NpgsqlTypes.NpgsqlInterval"/> instance.
            The format returned is of the form:
            [M mon[s]] [d day[s]] [HH:mm:ss[.f[f[f[f[f[f[f[f[f]]]]]]]]]]
            A zero <see cref="T:NpgsqlTypes.NpgsqlInterval"/> is represented as 00:00:00
            <remarks>
            Ticks are 100ns, Postgress resolution is only to 1µs at most. Hence we lose 1 or more decimal
            precision in storing values in the database. Despite this, this method will output that extra
            digit of precision. It's forward-compatible with any future increases in resolution up to 100ns,
            and also makes this ToString() more applicable to any other use-case.
            </remarks>
            </summary>
            <returns>The <see cref="T:System.String"/> representation.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlInterval.op_Addition(NpgsqlTypes.NpgsqlInterval,NpgsqlTypes.NpgsqlInterval)">
            <summary>
            Adds two <see cref="T:NpgsqlTypes.NpgsqlInterval"/> together.
            </summary>
            <param name="x">The first <see cref="T:NpgsqlTypes.NpgsqlInterval"/> to add.</param>
            <param name="y">The second <see cref="T:NpgsqlTypes.NpgsqlInterval"/> to add.</param>
            <returns>An <see cref="T:NpgsqlTypes.NpgsqlInterval"/> whose values are the sum of the arguments.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlInterval.op_Subtraction(NpgsqlTypes.NpgsqlInterval,NpgsqlTypes.NpgsqlInterval)">
            <summary>
            Subtracts one <see cref="T:NpgsqlTypes.NpgsqlInterval"/> from another.
            </summary>
            <param name="x">The <see cref="T:NpgsqlTypes.NpgsqlInterval"/> to subtract the other from.</param>
            <param name="y">The <see cref="T:NpgsqlTypes.NpgsqlInterval"/> to subtract from the other.</param>
            <returns>An <see cref="T:NpgsqlTypes.NpgsqlInterval"/> whose values are the difference of the arguments</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlInterval.op_Equality(NpgsqlTypes.NpgsqlInterval,NpgsqlTypes.NpgsqlInterval)">
            <summary>
            Returns true if two <see cref="T:NpgsqlTypes.NpgsqlInterval"/> are exactly the same.
            </summary>
            <param name="x">The first <see cref="T:NpgsqlTypes.NpgsqlInterval"/> to compare.</param>
            <param name="y">The second <see cref="T:NpgsqlTypes.NpgsqlInterval"/> to compare.</param>
            <returns>true if the two arguments are exactly the same, false otherwise.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlInterval.op_Inequality(NpgsqlTypes.NpgsqlInterval,NpgsqlTypes.NpgsqlInterval)">
            <summary>
            Returns false if two <see cref="T:NpgsqlTypes.NpgsqlInterval"/> are exactly the same.
            </summary>
            <param name="x">The first <see cref="T:NpgsqlTypes.NpgsqlInterval"/> to compare.</param>
            <param name="y">The second <see cref="T:NpgsqlTypes.NpgsqlInterval"/> to compare.</param>
            <returns>false if the two arguments are exactly the same, true otherwise.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlInterval.op_LessThan(NpgsqlTypes.NpgsqlInterval,NpgsqlTypes.NpgsqlInterval)">
            <summary>
            Compares two <see cref="T:NpgsqlTypes.NpgsqlInterval"/> instances to see if the first is less than the second
            </summary>
            <param name="x">The first <see cref="T:NpgsqlTypes.NpgsqlInterval"/> to compare.</param>
            <param name="y">The second <see cref="T:NpgsqlTypes.NpgsqlInterval"/> to compare.</param>
            <returns>true if the first <see cref="T:NpgsqlTypes.NpgsqlInterval"/> is less than second, false otherwise.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlInterval.op_LessThanOrEqual(NpgsqlTypes.NpgsqlInterval,NpgsqlTypes.NpgsqlInterval)">
            <summary>
            Compares two <see cref="T:NpgsqlTypes.NpgsqlInterval"/> instances to see if the first is less than or equivalent to the second
            </summary>
            <param name="x">The first <see cref="T:NpgsqlTypes.NpgsqlInterval"/> to compare.</param>
            <param name="y">The second <see cref="T:NpgsqlTypes.NpgsqlInterval"/> to compare.</param>
            <returns>true if the first <see cref="T:NpgsqlTypes.NpgsqlInterval"/> is less than or equivalent to second, false otherwise.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlInterval.op_GreaterThan(NpgsqlTypes.NpgsqlInterval,NpgsqlTypes.NpgsqlInterval)">
            <summary>
            Compares two <see cref="T:NpgsqlTypes.NpgsqlInterval"/> instances to see if the first is greater than the second
            </summary>
            <param name="x">The first <see cref="T:NpgsqlTypes.NpgsqlInterval"/> to compare.</param>
            <param name="y">The second <see cref="T:NpgsqlTypes.NpgsqlInterval"/> to compare.</param>
            <returns>true if the first <see cref="T:NpgsqlTypes.NpgsqlInterval"/> is greater than second, false otherwise.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlInterval.op_GreaterThanOrEqual(NpgsqlTypes.NpgsqlInterval,NpgsqlTypes.NpgsqlInterval)">
            <summary>
            Compares two <see cref="T:NpgsqlTypes.NpgsqlInterval"/> instances to see if the first is greater than or equivalent the second
            </summary>
            <param name="x">The first <see cref="T:NpgsqlTypes.NpgsqlInterval"/> to compare.</param>
            <param name="y">The second <see cref="T:NpgsqlTypes.NpgsqlInterval"/> to compare.</param>
            <returns>true if the first <see cref="T:NpgsqlTypes.NpgsqlInterval"/> is greater than or equivalent to the second, false otherwise.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlInterval.op_UnaryPlus(NpgsqlTypes.NpgsqlInterval)">
            <summary>
            Returns the instance.
            </summary>
            <param name="x">An <see cref="T:NpgsqlTypes.NpgsqlInterval"/>.</param>
            <returns>The argument.</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlInterval.op_UnaryNegation(NpgsqlTypes.NpgsqlInterval)">
            <summary>
            Negates an <see cref="T:NpgsqlTypes.NpgsqlInterval"/> instance.
            </summary>
            <param name="x">An <see cref="T:NpgsqlTypes.NpgsqlInterval"/>.</param>
            <returns>The negation of the argument.</returns>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlInterval.Ticks">
            <summary>
            The total number of ticks(100ns units) contained. This is the resolution of the
            <see cref="T:NpgsqlTypes.NpgsqlInterval"/>  type. This ignores the number of days and
            months held. If you want them included use <see cref="M:NpgsqlTypes.NpgsqlInterval.UnjustifyInterval"/> first.
            <remarks>The resolution of the PostgreSQL
            interval type is by default 1µs = 1,000 ns. It may be smaller as follows:
            <list type="number">
            <item>
            <term>interval(0)</term>
            <description>resolution of 1s (1 second)</description>
            </item>
            <item>
            <term>interval(1)</term>
            <description>resolution of 100ms = 0.1s (100 milliseconds)</description>
            </item>
            <item>
            <term>interval(2)</term>
            <description>resolution of 10ms = 0.01s (10 milliseconds)</description>
            </item>
            <item>
            <term>interval(3)</term>
            <description>resolution of 1ms = 0.001s (1 millisecond)</description>
            </item>
            <item>
            <term>interval(4)</term>
            <description>resolution of 100µs = 0.0001s (100 microseconds)</description>
            </item>
            <item>
            <term>interval(5)</term>
            <description>resolution of 10µs = 0.00001s (10 microseconds)</description>
            </item>
            <item>
            <term>interval(6) or interval</term>
            <description>resolution of 1µs = 0.000001s (1 microsecond)</description>
            </item>
            </list>
            <para>As such, if the 100-nanosecond resolution is significant to an application, a PostgreSQL interval will
            not suffice for those purposes.</para>
            <para>In more frequent cases though, the resolution of the interval suffices.
            <see cref="T:NpgsqlTypes.NpgsqlInterval"/> will always suffice to handle the resolution of any interval value, and upon
            writing to the database, will be rounded to the resolution used.</para>
            </remarks>
            <returns>The number of ticks in the instance.</returns>
            </summary>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlInterval.Microseconds">
            <summary>
            Gets the number of whole microseconds held in the instance.
            <returns>An  in the range [-999999, 999999].</returns>
            </summary>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlInterval.Milliseconds">
            <summary>
            Gets the number of whole milliseconds held in the instance.
            <returns>An  in the range [-999, 999].</returns>
            </summary>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlInterval.Seconds">
            <summary>
            Gets the number of whole seconds held in the instance.
            <returns>An  in the range [-59, 59].</returns>
            </summary>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlInterval.Minutes">
            <summary>
            Gets the number of whole minutes held in the instance.
            <returns>An  in the range [-59, 59].</returns>
            </summary>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlInterval.Hours">
            <summary>
            Gets the number of whole hours held in the instance.
            <remarks>Note that this can be less than -23 or greater than 23 unless <see cref="M:NpgsqlTypes.NpgsqlInterval.JustifyDays"/>
            has been used to produce this instance.</remarks>
            </summary>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlInterval.Days">
            <summary>
            Gets the number of days held in the instance.
            <remarks>Note that this does not pay attention to a time component with -24 or less hours or
            24 or more hours, unless <see cref="M:NpgsqlTypes.NpgsqlInterval.JustifyDays"/> has been called to produce this instance.</remarks>
            </summary>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlInterval.Months">
            <summary>
            Gets the number of months held in the instance.
            <remarks>Note that this does not pay attention to a day component with -30 or less days or
            30 or more days, unless <see cref="M:NpgsqlTypes.NpgsqlInterval.JustifyMonths"/> has been called to produce this instance.</remarks>
            </summary>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlInterval.Time">
            <summary>
            Returns a <see cref="T:System.TimeSpan"/> representing the time component of the instance.
            <remarks>Note that this may have a value beyond the range ±23:59:59.9999999 unless
            <see cref="M:NpgsqlTypes.NpgsqlInterval.JustifyDays"/> has been called to produce this instance.</remarks>
            </summary>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlInterval.TotalTicks">
            <summary>
            The total number of ticks (100ns units) in the instance, assuming 24 hours in each day and
            30 days in a month.
            </summary>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlInterval.TotalMicroseconds">
            <summary>
            The total number of microseconds in the instance, assuming 24 hours in each day and
            30 days in a month.
            </summary>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlInterval.TotalMilliseconds">
            <summary>
            The total number of milliseconds in the instance, assuming 24 hours in each day and
            30 days in a month.
            </summary>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlInterval.TotalSeconds">
            <summary>
            The total number of seconds in the instance, assuming 24 hours in each day and
            30 days in a month.
            </summary>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlInterval.TotalMinutes">
            <summary>
            The total number of minutes in the instance, assuming 24 hours in each day and
            30 days in a month.
            </summary>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlInterval.TotalHours">
            <summary>
            The total number of hours in the instance, assuming 24 hours in each day and
            30 days in a month.
            </summary>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlInterval.TotalDays">
            <summary>
            The total number of days in the instance, assuming 24 hours in each day and
            30 days in a month.
            </summary>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlInterval.TotalMonths">
            <summary>
            The total number of months in the instance, assuming 24 hours in each day and
            30 days in a month.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTime.Normalize">
            <summary>
            Normalise this time; if it is 24:00:00, convert it to 00:00:00
            </summary>
            <returns>This time, normalised</returns>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlTime.Ticks">
            <summary>
            The total number of ticks(100ns units) contained. This is the resolution of the
            <see cref="T:NpgsqlTypes.NpgsqlTime"/>  type.
            <remarks>The resolution of the PostgreSQL
            interval type is by default 1µs = 1,000 ns. It may be smaller as follows:
            <list type="number">
            <item>
            <term>time(0)</term>
            <description>resolution of 1s (1 second)</description>
            </item>
            <item>
            <term>time(1)</term>
            <description>resolution of 100ms = 0.1s (100 milliseconds)</description>
            </item>
            <item>
            <term>time(2)</term>
            <description>resolution of 10ms = 0.01s (10 milliseconds)</description>
            </item>
            <item>
            <term>time(3)</term>
            <description>resolution of 1ms = 0.001s (1 millisecond)</description>
            </item>
            <item>
            <term>time(4)</term>
            <description>resolution of 100µs = 0.0001s (100 microseconds)</description>
            </item>
            <item>
            <term>time(5)</term>
            <description>resolution of 10µs = 0.00001s (10 microseconds)</description>
            </item>
            <item>
            <term>time(6) or interval</term>
            <description>resolution of 1µs = 0.000001s (1 microsecond)</description>
            </item>
            </list>
            <para>As such, if the 100-nanosecond resolution is significant to an application, a PostgreSQL time will
            not suffice for those purposes.</para>
            <para>In more frequent cases though, the resolution of time suffices.
            <see cref="T:NpgsqlTypes.NpgsqlTime"/> will always suffice to handle the resolution of any time value, and upon
            writing to the database, will be rounded to the resolution used.</para>
            </remarks>
            <returns>The number of ticks in the instance.</returns>
            </summary>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlTime.Microseconds">
            <summary>
            Gets the number of whole microseconds held in the instance.
            <returns>An integer in the range [0, 999999].</returns>
            </summary>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlTime.Milliseconds">
            <summary>
            Gets the number of whole milliseconds held in the instance.
            <returns>An integer in the range [0, 999].</returns>
            </summary>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlTime.Seconds">
            <summary>
            Gets the number of whole seconds held in the instance.
            <returns>An interger in the range [0, 59].</returns>
            </summary>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlTime.Minutes">
            <summary>
            Gets the number of whole minutes held in the instance.
            <returns>An integer in the range [0, 59].</returns>
            </summary>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlTime.Hours">
            <summary>
            Gets the number of whole hours held in the instance.
            <remarks>Note that the time 24:00:00 can be stored for roundtrip compatibility. Any calculations on such a
            value will normalised it to 00:00:00.</remarks>
            </summary>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTimeTZ.Normalize">
            <summary>
            Normalise this time; if it is 24:00:00, convert it to 00:00:00
            </summary>
            <returns>This time, normalised</returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTimeTZ.CompareTo(NpgsqlTypes.NpgsqlTimeTZ)">
            <summary>
            Compares this with another <see cref="T:NpgsqlTypes.NpgsqlTimeTZ"/>. As per postgres' rules,
            first the times are compared as if they were both in the same timezone. If they are equal then
            then timezones are compared (+01:00 being "smaller" than -01:00).
            </summary>
            <param name="other">the <see cref="T:NpgsqlTypes.NpgsqlTimeTZ"/> to compare with.</param>
            <returns>An integer which is 0 if they are equal, &lt; 0 if this is the smaller and &gt; 0 if this is the larger.</returns>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlTimeTZ.Microseconds">
            <summary>
            Gets the number of whole microseconds held in the instance.
            <returns>An integer in the range [0, 999999].</returns>
            </summary>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlTimeTZ.Milliseconds">
            <summary>
            Gets the number of whole milliseconds held in the instance.
            <returns>An integer in the range [0, 999].</returns>
            </summary>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlTimeTZ.Seconds">
            <summary>
            Gets the number of whole seconds held in the instance.
            <returns>An interger in the range [0, 59].</returns>
            </summary>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlTimeTZ.Minutes">
            <summary>
            Gets the number of whole minutes held in the instance.
            <returns>An integer in the range [0, 59].</returns>
            </summary>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlTimeTZ.Hours">
            <summary>
            Gets the number of whole hours held in the instance.
            <remarks>Note that the time 24:00:00 can be stored for roundtrip compatibility. Any calculations on such a
            value will normalised it to 00:00:00.</remarks>
            </summary>
        </member>
        <member name="T:NpgsqlTypes.LargeObjectManager">
            <summary>
            Summary description for LargeObjectManager.
            </summary>
        </member>
        <member name="T:NpgsqlTypes.NativeToBackendTypeConverterOptions">
            <summary>
            Options that control certain aspects of native to backend conversions that depend
            on backend version and status.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.NativeToBackendTypeConverterOptions.System#ICloneable#Clone">
            <summary>
            Clone the current object.
            </summary>
            <returns>A new NativeToBackendTypeConverterOptions object.</returns>
        </member>
        <member name="M:NpgsqlTypes.NativeToBackendTypeConverterOptions.Clone(NpgsqlTypes.NpgsqlBackendTypeMapping)">
            <summary>
            Clone the current object with a different OID/Name mapping.
            </summary>
            <param name="oidToNameMapping">OID/Name mapping object to use in the new instance.</param>
            <returns>A new NativeToBackendTypeConverterOptions object.</returns>
        </member>
        <member name="T:NpgsqlTypes.BasicBackendToNativeTypeConverter">
            <summary>
            Provide event handlers to convert all native supported basic data types from their backend
            text representation to a .NET object.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.BasicBackendToNativeTypeConverter.TextBinaryToString(NpgsqlTypes.NpgsqlBackendTypeInfo,System.Byte[],System.Int32,System.Int32)">
            <summary>
            Convert UTF8 encoded text a string.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.BasicBackendToNativeTypeConverter.ByteaTextToByteArray(NpgsqlTypes.NpgsqlBackendTypeInfo,System.Byte[],System.Int16,System.Int32)">
            <summary>
            Byte array from bytea encoded as ASCII text, escaped or hex format.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.BasicBackendToNativeTypeConverter.ByteaBinaryToByteArray(NpgsqlTypes.NpgsqlBackendTypeInfo,System.Byte[],System.Int32,System.Int32)">
            <summary>
            Byte array from bytea encoded as binary.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.BasicBackendToNativeTypeConverter.BooleanTextToBoolean(NpgsqlTypes.NpgsqlBackendTypeInfo,System.Byte[],System.Int16,System.Int32)">
            <summary>
            Convert a postgresql boolean to a System.Boolean.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.BasicBackendToNativeTypeConverter.BooleanBinaryToBoolean(NpgsqlTypes.NpgsqlBackendTypeInfo,System.Byte[],System.Int32,System.Int32)">
            <summary>
            Convert a postgresql boolean to a System.Boolean.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.BasicBackendToNativeTypeConverter.ToBit(NpgsqlTypes.NpgsqlBackendTypeInfo,System.Byte[],System.Int16,System.Int32)">
            <summary>
            Convert a postgresql bit to a System.Boolean.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.BasicBackendToNativeTypeConverter.ToDateTime(NpgsqlTypes.NpgsqlBackendTypeInfo,System.Byte[],System.Int16,System.Int32)">
            <summary>
            Convert a postgresql datetime to a System.DateTime.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.BasicBackendToNativeTypeConverter.ToDate(NpgsqlTypes.NpgsqlBackendTypeInfo,System.Byte[],System.Int16,System.Int32)">
            <summary>
            Convert a postgresql date to a System.DateTime.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.BasicBackendToNativeTypeConverter.ToTime(NpgsqlTypes.NpgsqlBackendTypeInfo,System.Byte[],System.Int16,System.Int32)">
            <summary>
            Convert a postgresql time to a System.DateTime.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.BasicBackendToNativeTypeConverter.ToMoney(NpgsqlTypes.NpgsqlBackendTypeInfo,System.Byte[],System.Int16,System.Int32)">
            <summary>
            Convert a postgresql money to a System.Decimal.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.BasicBackendToNativeTypeConverter.Float4Float8BinaryToFloatDouble(NpgsqlTypes.NpgsqlBackendTypeInfo,System.Byte[],System.Int32,System.Int32)">
            <summary>
            Convert a postgresql float4 or float8 to a System.Float or System.Double respectively.
            </summary>
        </member>
        <member name="T:NpgsqlTypes.ExtendedBackendToNativeTypeConverter">
            <summary>
            Provide event handlers to convert extended native supported data types from their backend
            text representation to a .NET object.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.ExtendedBackendToNativeTypeConverter.ToPoint(NpgsqlTypes.NpgsqlBackendTypeInfo,System.Byte[],System.Int16,System.Int32)">
            <summary>
            Convert a postgresql point to a System.NpgsqlPoint.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.ExtendedBackendToNativeTypeConverter.ToBox(NpgsqlTypes.NpgsqlBackendTypeInfo,System.Byte[],System.Int16,System.Int32)">
            <summary>
            Convert a postgresql point to a System.RectangleF.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.ExtendedBackendToNativeTypeConverter.ToLSeg(NpgsqlTypes.NpgsqlBackendTypeInfo,System.Byte[],System.Int16,System.Int32)">
            <summary>
            LDeg.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.ExtendedBackendToNativeTypeConverter.ToPath(NpgsqlTypes.NpgsqlBackendTypeInfo,System.Byte[],System.Int16,System.Int32)">
            <summary>
            Path.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.ExtendedBackendToNativeTypeConverter.ToPolygon(NpgsqlTypes.NpgsqlBackendTypeInfo,System.Byte[],System.Int16,System.Int32)">
            <summary>
            Polygon.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.ExtendedBackendToNativeTypeConverter.ToCircle(NpgsqlTypes.NpgsqlBackendTypeInfo,System.Byte[],System.Int16,System.Int32)">
            <summary>
            Circle.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.ExtendedBackendToNativeTypeConverter.ToInet(NpgsqlTypes.NpgsqlBackendTypeInfo,System.Byte[],System.Int16,System.Int32)">
            <summary>
            Inet.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.ExtendedBackendToNativeTypeConverter.ToMacAddress(NpgsqlTypes.NpgsqlBackendTypeInfo,System.Byte[],System.Int16,System.Int32)">
            <summary>
            MAC Address.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.ExtendedBackendToNativeTypeConverter.ToInterval(NpgsqlTypes.NpgsqlBackendTypeInfo,System.Byte[],System.Int16,System.Int32)">
            <summary>
            interval
            </summary>
        </member>
        <member name="T:NpgsqlTypes.BasicNativeToBackendTypeConverter">
            <summary>
            Provide event handlers to convert the basic native supported data types from
            native form to backend representation.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.BasicNativeToBackendTypeConverter.StringToTextText(NpgsqlTypes.NpgsqlNativeTypeInfo,System.Object,System.Boolean,NpgsqlTypes.NativeToBackendTypeConverterOptions,System.Boolean)">
            <summary>
            Convert a string to UTF8 encoded text, escaped and quoted as required.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.BasicNativeToBackendTypeConverter.StringToTextBinary(NpgsqlTypes.NpgsqlNativeTypeInfo,System.Object,NpgsqlTypes.NativeToBackendTypeConverterOptions)">
            <summary>
            Convert a string to UTF8 encoded text.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.BasicNativeToBackendTypeConverter.ByteArrayToByteaText(NpgsqlTypes.NpgsqlNativeTypeInfo,System.Object,System.Boolean,NpgsqlTypes.NativeToBackendTypeConverterOptions,System.Boolean)">
            <summary>
            Binary data, escaped and quoted as required.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.BasicNativeToBackendTypeConverter.ByteArrayToByteaTextEscaped(System.Byte[],NpgsqlTypes.BasicNativeToBackendTypeConverter.StringEncodingInfo)">
            <summary>
            Binary data with possible older style octal escapes, quoted.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.BasicNativeToBackendTypeConverter.ByteArrayToByteaTextHexFormat(System.Byte[],NpgsqlTypes.BasicNativeToBackendTypeConverter.StringEncodingInfo)">
            <summary>
            Binary data in the new hex format (>= 9.0), quoted.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.BasicNativeToBackendTypeConverter.ByteArrayToByteaBinary(NpgsqlTypes.NpgsqlNativeTypeInfo,System.Object,NpgsqlTypes.NativeToBackendTypeConverterOptions)">
            <summary>
            Binary data, raw.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.BasicNativeToBackendTypeConverter.BooleanToBooleanText(NpgsqlTypes.NpgsqlNativeTypeInfo,System.Object,System.Boolean,NpgsqlTypes.NativeToBackendTypeConverterOptions,System.Boolean)">
            <summary>
            Convert to a postgresql boolean text format.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.BasicNativeToBackendTypeConverter.BooleanToBooleanBinary(NpgsqlTypes.NpgsqlNativeTypeInfo,System.Object,NpgsqlTypes.NativeToBackendTypeConverterOptions)">
            <summary>
            Convert to a postgresql boolean binary format.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.BasicNativeToBackendTypeConverter.Int16ToInt2Binary(NpgsqlTypes.NpgsqlNativeTypeInfo,System.Object,NpgsqlTypes.NativeToBackendTypeConverterOptions)">
            <summary>
            Convert to a postgresql binary int2.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.BasicNativeToBackendTypeConverter.Int32ToInt4Binary(NpgsqlTypes.NpgsqlNativeTypeInfo,System.Object,NpgsqlTypes.NativeToBackendTypeConverterOptions)">
            <summary>
            Convert to a postgresql binary int4.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.BasicNativeToBackendTypeConverter.Int64ToInt8Binary(NpgsqlTypes.NpgsqlNativeTypeInfo,System.Object,NpgsqlTypes.NativeToBackendTypeConverterOptions)">
            <summary>
            Convert to a postgresql binary int8.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.BasicNativeToBackendTypeConverter.ToBit(NpgsqlTypes.NpgsqlNativeTypeInfo,System.Object,System.Boolean,NpgsqlTypes.NativeToBackendTypeConverterOptions,System.Boolean)">
            <summary>
            Convert to a postgresql bit.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.BasicNativeToBackendTypeConverter.ToDateTime(NpgsqlTypes.NpgsqlNativeTypeInfo,System.Object,System.Boolean,NpgsqlTypes.NativeToBackendTypeConverterOptions,System.Boolean)">
            <summary>
            Convert to a postgresql timestamp.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.BasicNativeToBackendTypeConverter.ToDate(NpgsqlTypes.NpgsqlNativeTypeInfo,System.Object,System.Boolean,NpgsqlTypes.NativeToBackendTypeConverterOptions,System.Boolean)">
            <summary>
            Convert to a postgresql date.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.BasicNativeToBackendTypeConverter.ToTime(NpgsqlTypes.NpgsqlNativeTypeInfo,System.Object,System.Boolean,NpgsqlTypes.NativeToBackendTypeConverterOptions,System.Boolean)">
            <summary>
            Convert to a postgresql time.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.BasicNativeToBackendTypeConverter.ToMoney(NpgsqlTypes.NpgsqlNativeTypeInfo,System.Object,System.Boolean,NpgsqlTypes.NativeToBackendTypeConverterOptions,System.Boolean)">
            <summary>
            Convert to a postgres money.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.BasicNativeToBackendTypeConverter.SingleDoubleToFloat4Float8Text(NpgsqlTypes.NpgsqlNativeTypeInfo,System.Object,System.Boolean,NpgsqlTypes.NativeToBackendTypeConverterOptions,System.Boolean)">
            <summary>
            Convert to a postgres double with maximum precision.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.BasicNativeToBackendTypeConverter.SingleToFloat4Binary(NpgsqlTypes.NpgsqlNativeTypeInfo,System.Object,NpgsqlTypes.NativeToBackendTypeConverterOptions)">
            <summary>
            Convert a System.Float to a postgres float4.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.BasicNativeToBackendTypeConverter.DoubleToFloat8Binary(NpgsqlTypes.NpgsqlNativeTypeInfo,System.Object,NpgsqlTypes.NativeToBackendTypeConverterOptions)">
            <summary>
            Convert a System.Double to a postgres float8.
            </summary>
        </member>
        <member name="T:NpgsqlTypes.ExtendedNativeToBackendTypeConverter">
            <summary>
            Provide event handlers to convert extended native supported data types from
            native form to backend representation.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.ExtendedNativeToBackendTypeConverter.ToPoint(NpgsqlTypes.NpgsqlNativeTypeInfo,System.Object,System.Boolean,NpgsqlTypes.NativeToBackendTypeConverterOptions,System.Boolean)">
            <summary>
            Point.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.ExtendedNativeToBackendTypeConverter.ToBox(NpgsqlTypes.NpgsqlNativeTypeInfo,System.Object,System.Boolean,NpgsqlTypes.NativeToBackendTypeConverterOptions,System.Boolean)">
            <summary>
            Box.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.ExtendedNativeToBackendTypeConverter.ToLSeg(NpgsqlTypes.NpgsqlNativeTypeInfo,System.Object,System.Boolean,NpgsqlTypes.NativeToBackendTypeConverterOptions,System.Boolean)">
            <summary>
            LSeg.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.ExtendedNativeToBackendTypeConverter.ToPath(NpgsqlTypes.NpgsqlNativeTypeInfo,System.Object,System.Boolean,NpgsqlTypes.NativeToBackendTypeConverterOptions,System.Boolean)">
            <summary>
            Open path.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.ExtendedNativeToBackendTypeConverter.ToPolygon(NpgsqlTypes.NpgsqlNativeTypeInfo,System.Object,System.Boolean,NpgsqlTypes.NativeToBackendTypeConverterOptions,System.Boolean)">
            <summary>
            Polygon.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.ExtendedNativeToBackendTypeConverter.ToMacAddress(NpgsqlTypes.NpgsqlNativeTypeInfo,System.Object,System.Boolean,NpgsqlTypes.NativeToBackendTypeConverterOptions,System.Boolean)">
            <summary>
            Convert to a postgres MAC Address.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.ExtendedNativeToBackendTypeConverter.ToCircle(NpgsqlTypes.NpgsqlNativeTypeInfo,System.Object,System.Boolean,NpgsqlTypes.NativeToBackendTypeConverterOptions,System.Boolean)">
            <summary>
            Circle.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.ExtendedNativeToBackendTypeConverter.ToIPAddress(NpgsqlTypes.NpgsqlNativeTypeInfo,System.Object,System.Boolean,NpgsqlTypes.NativeToBackendTypeConverterOptions,System.Boolean)">
            <summary>
            Convert to a postgres inet.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.ExtendedNativeToBackendTypeConverter.ToInterval(NpgsqlTypes.NpgsqlNativeTypeInfo,System.Object,System.Boolean,NpgsqlTypes.NativeToBackendTypeConverterOptions,System.Boolean)">
            <summary>
            Convert to a postgres interval
            </summary>
        </member>
        <member name="T:NpgsqlTypes.ConvertBackendTextToNativeHandler">
            <summary>
            Delegate called to convert the given backend text data to its native representation.
            </summary>
        </member>
        <member name="T:NpgsqlTypes.ConvertBackendBinaryToNativeHandler">
            <summary>
            Delegate called to convert the given backend binary data to its native representation.
            </summary>
        </member>
        <member name="T:NpgsqlTypes.NpgsqlBackendTypeInfo">
            <summary>
            Represents a backend data type.
            This class can be called upon to convert a backend field representation to a native object.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlBackendTypeInfo.#ctor(System.Int32,System.String,NpgsqlTypes.NpgsqlDbType,System.Data.DbType,System.Type,NpgsqlTypes.ConvertBackendTextToNativeHandler,NpgsqlTypes.ConvertBackendBinaryToNativeHandler)">
            <summary>
            Construct a new NpgsqlTypeInfo with the given attributes and conversion handlers.
            </summary>
            <param name="OID">Type OID provided by the backend server.</param>
            <param name="Name">Type name provided by the backend server.</param>
            <param name="NpgsqlDbType">NpgsqlDbType</param>
            <param name="DbType">DbType</param>
            <param name="Type">System type to convert fields of this type to.</param>
            <param name="ConvertBackendTextToNative">Data conversion handler for text encoding.</param>
            <param name="ConvertBackendBinaryToNative">Data conversion handler for binary data.</param>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlBackendTypeInfo.ConvertBackendBinaryToNative(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Perform a data conversion from a backend representation to
            a native object.
            </summary>
            <param name="BackendData">Data sent from the backend.</param>
            <param name="fieldValueSize">fieldValueSize</param>
            <param name="TypeModifier">Type modifier field sent from the backend.</param>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlBackendTypeInfo.ConvertBackendTextToNative(System.Byte[],System.Int16,System.Int32)">
            <summary>
            Perform a data conversion from a backend representation to
            a native object.
            </summary>
            <param name="BackendData">Data sent from the backend.</param>
            <param name="TypeSize">TypeSize</param>
            <param name="TypeModifier">Type modifier field sent from the backend.</param>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlBackendTypeInfo.OID">
            <summary>
            Type OID provided by the backend server.
            </summary>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlBackendTypeInfo.Name">
            <summary>
            Type name provided by the backend server.
            </summary>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlBackendTypeInfo.NpgsqlDbType">
            <summary>
            NpgsqlDbType.
            </summary>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlBackendTypeInfo.DbType">
            <summary>
            NpgsqlDbType.
            </summary>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlBackendTypeInfo.Type">
            <summary>
            Provider type to convert fields of this type to.
            </summary>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlBackendTypeInfo.FrameworkType">
            <summary>
            System type to convert fields of this type to.
            </summary>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlBackendTypeInfo.SupportsBinaryBackendData">
            <summary>
            Reports whether a backend binary to native decoder is available for this type.
            </summary>
        </member>
        <member name="T:NpgsqlTypes.ConvertNativeToBackendTextHandler">
            <summary>
            Delegate called to convert the given native data to its backand representation.
            </summary>
        </member>
        <member name="T:NpgsqlTypes.NpgsqlNativeTypeInfo">
            <summary>
            Represents a backend data type.
            This class can be called upon to convert a native object to its backend field representation,
            </summary>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlNativeTypeInfo.ArrayOf(NpgsqlTypes.NpgsqlNativeTypeInfo)">
            <summary>
            Returns an NpgsqlNativeTypeInfo for an array where the elements are of the type
            described by the NpgsqlNativeTypeInfo supplied.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlNativeTypeInfo.#ctor(System.String,NpgsqlTypes.NpgsqlDbType,System.Data.DbType,System.Boolean,NpgsqlTypes.ConvertNativeToBackendTextHandler,NpgsqlTypes.ConvertNativeToBackendBinaryHandler)">
            <summary>
            Construct a new NpgsqlTypeInfo with the given attributes and conversion handlers.
            </summary>
            <param name="Name">Type name provided by the backend server.</param>
            <param name="DbType">DbType</param>
            <param name="Quote">Quote</param>
            <param name="NpgsqlDbType">NpgsqlDbType</param>
            <param name="ConvertNativeToBackendText">Data conversion handler for text backend encoding.</param>
            <param name="ConvertNativeToBackendBinary">Data conversion handler for binary backend encoding (for extended queries).</param>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlNativeTypeInfo.ConvertToBackend(System.Object,System.Boolean,NpgsqlTypes.NativeToBackendTypeConverterOptions,System.Boolean)">
            <summary>
            Perform a data conversion from a native object to
            a backend representation.
            DBNull and null values are handled differently depending if a plain query is used
            When
            </summary>
            <param name="NativeData">Native .NET object to be converted.</param>
            <param name="forExtendedQuery">Specifies that the value should be formatted for the extended query syntax.</param>
            <param name="options">Options to guide serialization.  If null, a default options set is used.</param>
            <param name="arrayElement">Specifies that the value should be formatted as an extended query array element.</param>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlNativeTypeInfo.Name">
            <summary>
            Type name provided by the backend server.
            </summary>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlNativeTypeInfo.NpgsqlDbType">
            <summary>
            NpgsqlDbType.
            </summary>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlNativeTypeInfo.DbType">
            <summary>
            DbType.
            </summary>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlNativeTypeInfo.Quote">
            <summary>
            Apply quoting.
            </summary>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlNativeTypeInfo.UseSize">
            <summary>
            Use parameter size information.
            </summary>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlNativeTypeInfo.SupportsBinaryBackendData">
            <summary>
            Reports whether a native to backend binary encoder is available for this type.
            </summary>
        </member>
        <member name="T:NpgsqlTypes.NpgsqlBackendTypeMapping">
            <summary>
            Provide mapping between type OID, type name, and a NpgsqlBackendTypeInfo object that represents it.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlBackendTypeMapping.#ctor">
            <summary>
            Construct an empty mapping.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlBackendTypeMapping.#ctor(NpgsqlTypes.NpgsqlBackendTypeMapping)">
            <summary>
            Copy constuctor.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlBackendTypeMapping.AddType(NpgsqlTypes.NpgsqlBackendTypeInfo)">
            <summary>
            Add the given NpgsqlBackendTypeInfo to this mapping.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlBackendTypeMapping.AddType(System.Int32,System.String,NpgsqlTypes.NpgsqlDbType,System.Data.DbType,System.Type,NpgsqlTypes.ConvertBackendTextToNativeHandler,NpgsqlTypes.ConvertBackendBinaryToNativeHandler)">
            <summary>
            Add a new NpgsqlBackendTypeInfo with the given attributes and conversion handlers to this mapping.
            </summary>
            <param name="OID">Type OID provided by the backend server.</param>
            <param name="Name">Type name provided by the backend server.</param>
            <param name="NpgsqlDbType">NpgsqlDbType</param>
            <param name="DbType">DbType</param>
            <param name="Type">System type to convert fields of this type to.</param>
            <param name="BackendTextConvert">Data conversion handler for text encoding.</param>
            <param name="BackendBinaryConvert">Data conversion handler for binary data.</param>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlBackendTypeMapping.Clone">
            <summary>
            Make a shallow copy of this type mapping.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlBackendTypeMapping.ContainsOID(System.Int32)">
            <summary>
            Determine if a NpgsqlBackendTypeInfo with the given backend type OID exists in this mapping.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlBackendTypeMapping.ContainsName(System.String)">
            <summary>
            Determine if a NpgsqlBackendTypeInfo with the given backend type name exists in this mapping.
            </summary>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlBackendTypeMapping.Count">
            <summary>
            Get the number of type infos held.
            </summary>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlBackendTypeMapping.Item(System.Int32)">
            <summary>
            Retrieve the NpgsqlBackendTypeInfo with the given backend type OID, or null if none found.
            </summary>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlBackendTypeMapping.Item(System.String)">
            <summary>
            Retrieve the NpgsqlBackendTypeInfo with the given backend type name, or null if none found.
            </summary>
        </member>
        <member name="T:NpgsqlTypes.NpgsqlNativeTypeMapping">
            <summary>
            Provide mapping between type Type, NpgsqlDbType and a NpgsqlNativeTypeInfo object that represents it.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlNativeTypeMapping.AddType(NpgsqlTypes.NpgsqlNativeTypeInfo)">
            <summary>
            Add the given NpgsqlNativeTypeInfo to this mapping.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlNativeTypeMapping.AddType(System.String,NpgsqlTypes.NpgsqlDbType,System.Data.DbType,System.Boolean,NpgsqlTypes.ConvertNativeToBackendTextHandler,NpgsqlTypes.ConvertNativeToBackendBinaryHandler)">
            <summary>
            Add a new NpgsqlNativeTypeInfo with the given attributes and conversion handlers to this mapping.
            </summary>
            <param name="Name">Type name provided by the backend server.</param>
            <param name="NpgsqlDbType">NpgsqlDbType</param>
            <param name="DbType">DbType</param>
            <param name="Quote">Quote</param>
            <param name="NativeTextConvert">Data conversion handler for text backend encoding.</param>
            <param name="NativeBinaryConvert">Data conversion handler for binary backend encoding (for extended query).</param>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlNativeTypeMapping.TryGetValue(NpgsqlTypes.NpgsqlDbType,NpgsqlTypes.NpgsqlNativeTypeInfo@)">
            <summary>
            Retrieve the NpgsqlNativeTypeInfo with the given NpgsqlDbType.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlNativeTypeMapping.TryGetValue(System.Data.DbType,NpgsqlTypes.NpgsqlNativeTypeInfo@)">
            <summary>
            Retrieve the NpgsqlNativeTypeInfo with the given DbType.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlNativeTypeMapping.TryGetValue(System.Type,NpgsqlTypes.NpgsqlNativeTypeInfo@)">
            <summary>
            Retrieve the NpgsqlNativeTypeInfo with the given Type.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlNativeTypeMapping.ContainsName(System.String)">
            <summary>
            Determine if a NpgsqlNativeTypeInfo with the given backend type name exists in this mapping.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlNativeTypeMapping.ContainsNpgsqlDbType(NpgsqlTypes.NpgsqlDbType)">
            <summary>
            Determine if a NpgsqlNativeTypeInfo with the given NpgsqlDbType exists in this mapping.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlNativeTypeMapping.ContainsType(System.Type)">
            <summary>
            Determine if a NpgsqlNativeTypeInfo with the given Type name exists in this mapping.
            </summary>
        </member>
        <member name="P:NpgsqlTypes.NpgsqlNativeTypeMapping.Count">
            <summary>
            Get the number of type infos held.
            </summary>
        </member>
        <member name="T:NpgsqlTypes.NpgsqlPoint">
            <summary>
            Represents a PostgreSQL Point type
            </summary>
        </member>
        <member name="T:NpgsqlTypes.NpgsqlLSeg">
            <summary>
            Represents a PostgreSQL Line Segment type.
            </summary>
        </member>
        <member name="T:NpgsqlTypes.NpgsqlPath">
            <summary>
            Represents a PostgreSQL Path type.
            </summary>
        </member>
        <member name="T:NpgsqlTypes.NpgsqlPolygon">
            <summary>
            Represents a PostgreSQL Polygon type.
            </summary>
        </member>
        <member name="T:NpgsqlTypes.NpgsqlCircle">
            <summary>
            Represents a PostgreSQL Circle type.
            </summary>
        </member>
        <member name="T:NpgsqlTypes.NpgsqlInet">
            <summary>
            Represents a PostgreSQL inet type.
            </summary>
        </member>
        <member name="T:NpgsqlTypes.NpgsqlMacAddress">
            <summary>
            Represents a PostgreSQL MacAddress type.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlMacAddress.#ctor(System.String)">
             <summary>
            
             </summary>
             <param name="macAddr">The macAddr parameter must contain a string that can only consist of numbers
             and upper-case letters as hexadecimal digits. (See PhysicalAddress.Parse method on MSDN)</param>
        </member>
        <member name="T:NpgsqlTypes.NpgsqlTypesHelper">
            <summary>
               This class contains helper methods for type conversion between
            the .Net type system and postgresql.
            </summary>
        </member>
        <member name="F:NpgsqlTypes.NpgsqlTypesHelper.BackendTypeMappingCache">
            <summary>
            A cache of basic datatype mappings keyed by server version.  This way we don't
            have to load the basic type mappings for every connection.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTypesHelper.TryGetBackendTypeInfo(System.String,NpgsqlTypes.NpgsqlBackendTypeInfo@)">
            <summary>
            Find a NpgsqlNativeTypeInfo in the default types map that can handle objects
            of the given NpgsqlDbType.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTypesHelper.TryGetNativeTypeInfo(NpgsqlTypes.NpgsqlDbType,NpgsqlTypes.NpgsqlNativeTypeInfo@)">
            <summary>
            Find a NpgsqlNativeTypeInfo in the default types map that can handle objects
            of the given NpgsqlDbType.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTypesHelper.TryGetNativeTypeInfo(System.Data.DbType,NpgsqlTypes.NpgsqlNativeTypeInfo@)">
            <summary>
            Find a NpgsqlNativeTypeInfo in the default types map that can handle objects
            of the given DbType.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTypesHelper.TryGetNativeTypeInfo(System.Type,NpgsqlTypes.NpgsqlNativeTypeInfo@)">
            <summary>
            Find a NpgsqlNativeTypeInfo in the default types map that can handle objects
            of the given System.Type.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTypesHelper.ConvertBackendBytesToSystemType(NpgsqlTypes.NpgsqlBackendTypeInfo,System.Byte[],System.Int32,System.Int32)">
            <summary>
             This method is responsible to convert the byte[] received from the backend
             to the corresponding NpgsqlType.
             The given TypeInfo is called upon to do the conversion.
             If no TypeInfo object is provided, no conversion is performed.
             </summary>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTypesHelper.ConvertBackendStringToSystemType(NpgsqlTypes.NpgsqlBackendTypeInfo,System.Byte[],System.Int16,System.Int32)">
            <summary>
             This method is responsible to convert the string received from the backend
             to the corresponding NpgsqlType.
             The given TypeInfo is called upon to do the conversion.
             If no TypeInfo object is provided, no conversion is performed.
             </summary>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTypesHelper.PrepareDefaultTypesMap">
            <summary>
            Create the one and only native to backend type map.
            This map is used when formatting native data
            types to backend representations.
            </summary>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTypesHelper.CreateAndLoadInitialTypesMapping(Npgsql.NpgsqlConnector)">
            <summary>
             This method creates (or retrieves from cache) a mapping between type and OID
             of all natively supported postgresql data types.
             This is needed as from one version to another, this mapping can be changed and
             so we avoid hardcoding them.
             </summary>
             <returns>NpgsqlTypeMapping containing all known data types.  The mapping must be
             cloned before it is modified because it is cached; changes made by one connection may
             effect another connection.
             </returns>
        </member>
        <member name="M:NpgsqlTypes.NpgsqlTypesHelper.LoadTypesMappings(Npgsql.NpgsqlConnector,NpgsqlTypes.NpgsqlBackendTypeMapping,System.Collections.Generic.IEnumerable{NpgsqlTypes.NpgsqlBackendTypeInfo})">
            <summary>
            Attempt to map types by issuing a query against pg_type.
            This function takes a list of NpgsqlTypeInfo and attempts to resolve the OID field
            of each by querying pg_type.  If the mapping is found, the type info object is
            updated (OID) and added to the provided NpgsqlTypeMapping object.
            </summary>
            <param name="conn">NpgsqlConnector to send query through.</param>
            <param name="TypeMappings">Mapping object to add types too.</param>
            <param name="TypeInfoList">List of types that need to have OID's mapped.</param>
        </member>
        <member name="P:Npgsql.Cache`1.CacheSize">
            <summary>
            Set Cache Size. The default value is 20.
            </summary>
        </member>
        <member name="P:Npgsql.Cache`1.Item(System.String)">
            <summary>
            Lookup cached entity. null will returned if not match.
            For both get{} and set{} apply LRU rule.
            </summary>
            <param name="key">key</param>
            <returns></returns>
        </member>
        <member name="T:Npgsql.BackendEncoding">
            <summary>
            The globally available text encoding used for frontend/backend communication.
            </summary>
        </member>
        <member name="T:Npgsql.NpgsqlState">
            <summary> This class represents the base class for the state pattern design pattern
             implementation.
             </summary>
            
            <summary> This class represents the base class for the state pattern design pattern
             implementation.
             </summary>
            
            <summary> This class represents the base class for the state pattern design pattern
             implementation.
             </summary>
            
        </member>
        <member name="M:Npgsql.NpgsqlState.ChangeState(Npgsql.NpgsqlConnector,Npgsql.NpgsqlState)">
            <summary>
            This method is used by the states to change the state of the context.
             </summary>
        </member>
        <member name="M:Npgsql.NpgsqlState.ProcessAndDiscardBackendResponses(Npgsql.NpgsqlConnector)">
            <summary>
            Call ProcessBackendResponsesEnum(), and scan and discard all results.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlState.ProcessBackendResponsesEnum(Npgsql.NpgsqlConnector)">
            <summary>
             This method is responsible to handle all protocol messages sent from the backend.
             It holds all the logic to do it.
             To exchange data, it uses a Mediator object from which it reads/writes information
             to handle backend requests.
             </summary>
            
        </member>
        <member name="M:Npgsql.NpgsqlState.CheckForContextSocketAvailability(Npgsql.NpgsqlConnector,System.Net.Sockets.SelectMode)">
            <summary>
            Checks for context socket availability.
            Socket.Poll supports integer as microseconds parameter.
            This limits the usable command timeout value
            to 2,147 seconds: (2,147 x 1,000,000 less than  max_int).
            In order to bypass this limit, the availability of
            the socket is checked in 2,147 seconds cycles
            </summary>
            <returns><c>true</c>, if for context socket availability was checked, <c>false</c> otherwise.</returns>
            <param name="context">Context.</param>
            <param name="selectMode">Select mode.</param>
        </member>
        <member name="M:Npgsql.HashAlgorithm.#ctor">
            <summary>
            Called from constructor of derived class.
            </summary>
        </member>
        <member name="M:Npgsql.HashAlgorithm.Finalize">
            <summary>
            Finalizer for HashAlgorithm
            </summary>
        </member>
        <member name="M:Npgsql.HashAlgorithm.ComputeHash(System.Byte[])">
            <summary>
            Computes the entire hash of all the bytes in the byte array.
            </summary>
        </member>
        <member name="M:Npgsql.HashAlgorithm.HashCore(System.Byte[],System.Int32,System.Int32)">
            <summary>
            When overridden in a derived class, drives the hashing function.
            </summary>
            <param name="rgb"></param>
            <param name="start"></param>
            <param name="size"></param>
        </member>
        <member name="M:Npgsql.HashAlgorithm.HashFinal">
            <summary>
            When overridden in a derived class, this pads and hashes whatever data might be left in the buffers and then returns the hash created.
            </summary>
        </member>
        <member name="M:Npgsql.HashAlgorithm.Initialize">
            <summary>
            When overridden in a derived class, initializes the object to prepare for hashing.
            </summary>
        </member>
        <member name="M:Npgsql.HashAlgorithm.TransformBlock(System.Byte[],System.Int32,System.Int32,System.Byte[],System.Int32)">
            <summary>
            Used for stream chaining.  Computes hash as data passes through it.
            </summary>
            <param name="inputBuffer">The buffer from which to grab the data to be copied.</param>
            <param name="inputOffset">The offset into the input buffer to start reading at.</param>
            <param name="inputCount">The number of bytes to be copied.</param>
            <param name="outputBuffer">The buffer to write the copied data to.</param>
            <param name="outputOffset">At what point in the outputBuffer to write the data at.</param>
        </member>
        <member name="M:Npgsql.HashAlgorithm.TransformFinalBlock(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Used for stream chaining.  Computes hash as data passes through it.  Finishes off the hash.
            </summary>
            <param name="inputBuffer">The buffer from which to grab the data to be copied.</param>
            <param name="inputOffset">The offset into the input buffer to start reading at.</param>
            <param name="inputCount">The number of bytes to be copied.</param>
        </member>
        <member name="P:Npgsql.HashAlgorithm.CanTransformMultipleBlocks">
            <summary>
            Get whether or not the hash can transform multiple blocks at a time.
            Note: MUST be overriden if descendant can transform multiple block
            on a single call!
            </summary>
        </member>
        <member name="P:Npgsql.HashAlgorithm.Hash">
            <summary>
            Gets the previously computed hash.
            </summary>
        </member>
        <member name="P:Npgsql.HashAlgorithm.HashSize">
            <summary>
            Returns the size in bits of the hash.
            </summary>
        </member>
        <member name="P:Npgsql.HashAlgorithm.InputBlockSize">
            <summary>
            Must be overriden if not 1
            </summary>
        </member>
        <member name="P:Npgsql.HashAlgorithm.OutputBlockSize">
            <summary>
            Must be overriden if not 1
            </summary>
        </member>
        <member name="T:Npgsql.MD5">
            <summary>
            Common base class for all derived MD5 implementations.
            </summary>
        </member>
        <member name="M:Npgsql.MD5.#ctor">
            <summary>
            Called from constructor of derived class.
            </summary>
        </member>
        <member name="M:Npgsql.MD5.Create">
            <summary>
            Creates the default derived class.
            </summary>
        </member>
        <member name="T:Npgsql.MD5CryptoServiceProvider">
            <summary>
            C# implementation of the MD5 cryptographic hash function.
            </summary>
        </member>
        <member name="M:Npgsql.MD5CryptoServiceProvider.#ctor">
            <summary>
            Creates a new MD5CryptoServiceProvider.
            </summary>
        </member>
        <member name="M:Npgsql.MD5CryptoServiceProvider.HashCore(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Drives the hashing function.
            </summary>
            <param name="rgb">Byte array containing the data to hash.</param>
            <param name="start">Where in the input buffer to start.</param>
            <param name="size">Size in bytes of the data in the buffer to hash.</param>
        </member>
        <member name="M:Npgsql.MD5CryptoServiceProvider.HashFinal">
            <summary>
            This finalizes the hash.  Takes the data from the chaining variables and returns it.
            </summary>
        </member>
        <member name="M:Npgsql.MD5CryptoServiceProvider.Initialize">
            <summary>
            Resets the class after use.  Called automatically after hashing is done.
            </summary>
        </member>
        <member name="M:Npgsql.MD5CryptoServiceProvider.ProcessBlock(System.Byte[],System.Int32)">
            <summary>
            This is the meat of the hash function.  It is what processes each block one at a time.
            </summary>
            <param name="inputBuffer">Byte array to process data from.</param>
            <param name="inputOffset">Where in the byte array to start processing.</param>
        </member>
        <member name="M:Npgsql.MD5CryptoServiceProvider.ProcessFinalBlock(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Pads and then processes the final block.
            </summary>
            <param name="inputBuffer">Buffer to grab data from.</param>
            <param name="inputOffset">Position in buffer in bytes to get data from.</param>
            <param name="inputCount">How much data in bytes in the buffer to use.</param>
        </member>
        <member name="T:Npgsql.StringRowReaderV3">
            <summary>
            Implements <see cref="T:Npgsql.RowReader"/> for version 3 of the protocol.
            </summary>
        </member>
        <member name="T:Npgsql.RowReader">
            <summary>
            Reads a row, field by field, allowing a DataRow to be built appropriately.
            </summary>
        </member>
        <member name="T:Npgsql.IStreamOwner">
             <summary>
             Marker interface which identifies a class which may take possession of a stream for the duration of
             it's lifetime (possibly temporarily giving that possession to another class for part of that time.
            
             It inherits from IDisposable, since any such class must make sure it leaves the stream in a valid state.
            
             The most important such class is that compiler-generated from ProcessBackendResponsesEnum. Of course
             we can't make that inherit from this interface, alas.
             </summary>
        </member>
        <member name="T:Npgsql.IServerResponseObject">
            <summary>
            Marker interface which identifies a class which represents part of
            a response from the server.
            </summary>
        </member>
        <member name="T:Npgsql.RowReader.Streamer">
            <summary>
            Reads part of a field, as needed (for <see cref="M:System.Data.IDataRecord.GetChars(System.Int32,System.Int64,System.Char[],System.Int32,System.Int32)"/>
            and <see cref="M:System.Data.IDataRecord.GetBytes(System.Int32,System.Int64,System.Byte[],System.Int32,System.Int32)"/>
            </summary>
        </member>
        <member name="T:Npgsql.RowReader.Streamer`1">
            <summary>
            Adds further functionality to stream that is dependant upon the type of data read.
            </summary>
        </member>
        <member name="T:Npgsql.RowReader.CharStreamer">
            <summary>
            Completes the implementation of Streamer for char data.
            </summary>
        </member>
        <member name="T:Npgsql.RowReader.ByteStreamer">
            <summary>
            Completes the implementation of Streamer for byte data.
            </summary>
        </member>
        <member name="T:Npgsql.StringRowReaderV2">
            <summary>
            Implements <see cref="T:Npgsql.RowReader"/> for version 2 of the protocol.
            </summary>
        </member>
        <member name="T:Npgsql.StringRowReaderV2.NullMap">
            <summary>
            Encapsulates the null mapping bytes sent at the start of a version 2
            datarow message, and the process of identifying the nullity of the data
            at a particular index
            </summary>
        </member>
        <member name="T:Npgsql.NpgsqlBackEndKeyData">
            <summary>
            This class represents a BackEndKeyData message received
            from PostgreSQL
            </summary>
        </member>
        <member name="T:Npgsql.NpgsqlBind">
             <summary>
             This class represents the Bind message sent to PostgreSQL
             server.
             </summary>
            
        </member>
        <member name="T:Npgsql.ClientMessage">
            <summary>
            For classes representing messages sent from the client to the server.
            </summary>
        </member>
        <member name="T:Npgsql.NpgsqlCancelRequest">
             <summary>
             This class represents the CancelRequest message sent to PostgreSQL
             server.
             </summary>
            
        </member>
        <member name="T:Npgsql.NpgsqlCommand">
            <summary>
            Represents a SQL statement or function (stored procedure) to execute
            against a PostgreSQL database. This class cannot be inherited.
            </summary>
            <summary>
            Represents a SQL statement or function (stored procedure) to execute
            against a PostgreSQL database. This class cannot be inherited.
            </summary>
            <summary>
            Represents a SQL statement or function (stored procedure) to execute
            against a PostgreSQL database. This class cannot be inherited.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlCommand.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Npgsql.NpgsqlCommand">NpgsqlCommand</see> class.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlCommand.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Npgsql.NpgsqlCommand">NpgsqlCommand</see> class with the text of the query.
            </summary>
            <param name="cmdText">The text of the query.</param>
        </member>
        <member name="M:Npgsql.NpgsqlCommand.#ctor(System.String,Npgsql.NpgsqlConnection)">
            <summary>
            Initializes a new instance of the <see cref="T:Npgsql.NpgsqlCommand">NpgsqlCommand</see> class with the text of the query and a <see cref="T:Npgsql.NpgsqlConnection">NpgsqlConnection</see>.
            </summary>
            <param name="cmdText">The text of the query.</param>
            <param name="connection">A <see cref="T:Npgsql.NpgsqlConnection">NpgsqlConnection</see> that represents the connection to a PostgreSQL server.</param>
        </member>
        <member name="M:Npgsql.NpgsqlCommand.#ctor(System.String,Npgsql.NpgsqlConnection,Npgsql.NpgsqlTransaction)">
            <summary>
            Initializes a new instance of the <see cref="T:Npgsql.NpgsqlCommand">NpgsqlCommand</see> class with the text of the query, a <see cref="T:Npgsql.NpgsqlConnection">NpgsqlConnection</see>, and the <see cref="T:Npgsql.NpgsqlTransaction">NpgsqlTransaction</see>.
            </summary>
            <param name="cmdText">The text of the query.</param>
            <param name="connection">A <see cref="T:Npgsql.NpgsqlConnection">NpgsqlConnection</see> that represents the connection to a PostgreSQL server.</param>
            <param name="transaction">The <see cref="T:Npgsql.NpgsqlTransaction">NpgsqlTransaction</see> in which the <see cref="T:Npgsql.NpgsqlCommand">NpgsqlCommand</see> executes.</param>
        </member>
        <member name="M:Npgsql.NpgsqlCommand.#ctor(System.String,Npgsql.NpgsqlConnector,System.Int32)">
            <summary>
            Used to execute internal commands.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlCommand.Cancel">
            <summary>
            Attempts to cancel the execution of a <see cref="T:Npgsql.NpgsqlCommand">NpgsqlCommand</see>.
            </summary>
            <remarks>This Method isn't implemented yet.</remarks>
        </member>
        <member name="M:Npgsql.NpgsqlCommand.System#ICloneable#Clone">
            <summary>
            Create a new command based on this one.
            </summary>
            <returns>A new NpgsqlCommand object.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlCommand.Clone">
            <summary>
            Create a new command based on this one.
            </summary>
            <returns>A new NpgsqlCommand object.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlCommand.CreateDbParameter">
            <summary>
            Creates a new instance of an <see cref="T:System.Data.Common.DbParameter">DbParameter</see> object.
            </summary>
            <returns>An <see cref="T:System.Data.Common.DbParameter">DbParameter</see> object.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlCommand.CreateParameter">
            <summary>
            Creates a new instance of a <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> object.
            </summary>
            <returns>A <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> object.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlCommand.ExecuteBlind">
            <summary>
            Slightly optimised version of ExecuteNonQuery() for internal use in cases where the number
            of affected rows is of no interest.
            This function must not be called with a query that returns result rows, after calling Prepare(), or.
            with a query that requires parameter substitution of any kind.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlCommand.ExecuteNonQuery">
            <summary>
            Executes a SQL statement against the connection and returns the number of rows affected.
            </summary>
            <returns>The number of rows affected if known; -1 otherwise.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlCommand.ExecuteDbDataReader(System.Data.CommandBehavior)">
            <summary>
            Sends the <see cref="P:Npgsql.NpgsqlCommand.CommandText">CommandText</see> to
            the <see cref="T:Npgsql.NpgsqlConnection">Connection</see> and builds a
            <see cref="T:Npgsql.NpgsqlDataReader">NpgsqlDataReader</see>
            using one of the <see cref="T:System.Data.CommandBehavior">CommandBehavior</see> values.
            </summary>
            <param name="behavior">One of the <see cref="T:System.Data.CommandBehavior">CommandBehavior</see> values.</param>
            <returns>A <see cref="T:Npgsql.NpgsqlDataReader">NpgsqlDataReader</see> object.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlCommand.ExecuteReader">
            <summary>
            Sends the <see cref="P:Npgsql.NpgsqlCommand.CommandText">CommandText</see> to
            the <see cref="T:Npgsql.NpgsqlConnection">Connection</see> and builds a
            <see cref="T:Npgsql.NpgsqlDataReader">NpgsqlDataReader</see>.
            </summary>
            <returns>A <see cref="T:Npgsql.NpgsqlDataReader">NpgsqlDataReader</see> object.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlCommand.ExecuteReader(System.Data.CommandBehavior)">
            <summary>
            Sends the <see cref="P:Npgsql.NpgsqlCommand.CommandText">CommandText</see> to
            the <see cref="T:Npgsql.NpgsqlConnection">Connection</see> and builds a
            <see cref="T:Npgsql.NpgsqlDataReader">NpgsqlDataReader</see>
            using one of the <see cref="T:System.Data.CommandBehavior">CommandBehavior</see> values.
            </summary>
            <param name="cb">One of the <see cref="T:System.Data.CommandBehavior">CommandBehavior</see> values.</param>
            <returns>A <see cref="T:Npgsql.NpgsqlDataReader">NpgsqlDataReader</see> object.</returns>
            <remarks>Currently the CommandBehavior parameter is ignored.</remarks>
        </member>
        <member name="M:Npgsql.NpgsqlCommand.BindParameters">
            <summary>
             This method binds the parameters from parameters collection to the bind
             message.
             </summary>
        </member>
        <member name="M:Npgsql.NpgsqlCommand.ExecuteScalar">
            <summary>
            Executes the query, and returns the first column of the first row
            in the result set returned by the query. Extra columns or rows are ignored.
            </summary>
            <returns>The first column of the first row in the result set,
            or a null reference if the result set is empty.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlCommand.Prepare">
            <summary>
            Creates a prepared version of the command on a PostgreSQL server.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlCommand.CheckConnectionState">
            <summary>
             This method checks the connection state to see if the connection
             is set or it is open. If one of this conditions is not met, throws
             an InvalidOperationException
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlCommand.GetCommandText">
            <summary>
            This method substitutes the <see cref="P:Npgsql.NpgsqlCommand.Parameters">Parameters</see>, if exist, in the command
            to their actual values.
            The parameter name format is <b>:ParameterName</b>.
            </summary>
            <returns>A version of <see cref="P:Npgsql.NpgsqlCommand.CommandText">CommandText</see> with the <see cref="P:Npgsql.NpgsqlCommand.Parameters">Parameters</see> inserted.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlCommand.GetCommandText(System.Boolean,System.Boolean)">
            <summary>
            Process this.commandText, trimming each distinct command and substituting paramater
            tokens.
            </summary>
            <param name="prepare"></param>
            <param name="forExtendQuery"></param>
            <returns>UTF8 encoded command ready to be sent to the backend.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlCommand.GetDistinctTrimmedCommands(System.String)">
            <summary>
            Find the beginning and end of each distinct SQL command and produce
            a list of descriptors, one for each command.  Commands described are trimmed of
            leading and trailing white space and their terminating semi-colons.
            </summary>
            <param name="src">Raw command text.</param>
            <returns>List of chunk descriptors.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlCommand.AppendCommandReplacingParameterValues(System.IO.Stream,System.String,System.Int32,System.Int32,System.Boolean,System.Boolean)">
            <summary>
            Append a region of a source command text to an output command, performing parameter token
            substitutions.
            </summary>
            <param name="dest">Stream to which to append output.</param>
            <param name="src">Command text.</param>
            <param name="begin">Starting index within src.</param>
            <param name="length">Length of region to be processed.</param>
            <param name="prepare"></param>
            <param name="forExtendedQuery"></param>
        </member>
        <member name="P:Npgsql.NpgsqlCommand.CommandText">
            <summary>
            Gets or sets the SQL statement or function (stored procedure) to execute at the data source.
            </summary>
            <value>The Transact-SQL statement or stored procedure to execute. The default is an empty string.</value>
        </member>
        <member name="P:Npgsql.NpgsqlCommand.CommandTimeout">
            <summary>
            Gets or sets the wait time before terminating the attempt
            to execute a command and generating an error.
            </summary>
            <value>The time (in seconds) to wait for the command to execute.
            The default is 20 seconds.</value>
        </member>
        <member name="P:Npgsql.NpgsqlCommand.CommandType">
            <summary>
            Gets or sets a value indicating how the
            <see cref="P:Npgsql.NpgsqlCommand.CommandText">CommandText</see> property is to be interpreted.
            </summary>
            <value>One of the <see cref="T:System.Data.CommandType">CommandType</see> values. The default is <see cref="T:System.Data.CommandType">CommandType.Text</see>.</value>
        </member>
        <member name="P:Npgsql.NpgsqlCommand.Connection">
            <summary>
            Gets or sets the <see cref="T:Npgsql.NpgsqlConnection">NpgsqlConnection</see>
            used by this instance of the <see cref="T:Npgsql.NpgsqlCommand">NpgsqlCommand</see>.
            </summary>
            <value>The connection to a data source. The default value is a null reference.</value>
        </member>
        <member name="P:Npgsql.NpgsqlCommand.Parameters">
            <summary>
            Gets the <see cref="T:Npgsql.NpgsqlParameterCollection">NpgsqlParameterCollection</see>.
            </summary>
            <value>The parameters of the SQL statement or function (stored procedure). The default is an empty collection.</value>
        </member>
        <member name="P:Npgsql.NpgsqlCommand.Transaction">
            <summary>
            Gets or sets the <see cref="T:Npgsql.NpgsqlTransaction">NpgsqlTransaction</see>
            within which the <see cref="T:Npgsql.NpgsqlCommand">NpgsqlCommand</see> executes.
            </summary>
            <value>The <see cref="T:Npgsql.NpgsqlTransaction">NpgsqlTransaction</see>.
            The default value is a null reference.</value>
        </member>
        <member name="P:Npgsql.NpgsqlCommand.UpdatedRowSource">
            <summary>
            Gets or sets how command results are applied to the <see cref="T:System.Data.DataRow">DataRow</see>
            when used by the <see cref="M:System.Data.Common.DbDataAdapter.Update(System.Data.DataSet)">Update</see>
            method of the <see cref="T:System.Data.Common.DbDataAdapter">DbDataAdapter</see>.
            </summary>
            <value>One of the <see cref="T:System.Data.UpdateRowSource">UpdateRowSource</see> values.</value>
        </member>
        <member name="P:Npgsql.NpgsqlCommand.LastInsertedOID">
            <summary>
            Returns oid of inserted row. This is only updated when using executenonQuery and when command inserts just a single row. If table is created without oids, this will always be 0.
            </summary>
        </member>
        <member name="T:Npgsql.NpgsqlCommandBuilder">
            <summary>
             This class is responsible to create database commands for automatic insert, update and delete operations.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlCommandBuilder.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Npgsql.NpgsqlCommandBuilder"/> class.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlCommandBuilder.#ctor(Npgsql.NpgsqlDataAdapter)">
            <summary>
            Initializes a new instance of the <see cref="T:Npgsql.NpgsqlCommandBuilder"/> class.
            </summary>
            <param name="adapter">The adapter.</param>
        </member>
        <member name="M:Npgsql.NpgsqlCommandBuilder.DeriveParameters(Npgsql.NpgsqlCommand)">
            <summary>
            
             This method is reponsible to derive the command parameter list with values obtained from function definition.
             It clears the Parameters collection of command. Also, if there is any parameter type which is not supported by Npgsql, an InvalidOperationException will be thrown.
             Parameters name will be parameter1, parameter2, ...
            </summary>
             <param name="command">NpgsqlCommand whose function parameters will be obtained.</param>
        </member>
        <member name="M:Npgsql.NpgsqlCommandBuilder.GetInsertCommand">
            <summary>
            Gets the automatically generated <see cref="T:Npgsql.NpgsqlCommand"/> object required
            to perform insertions at the data source.
            </summary>
            <returns>
            The automatically generated <see cref="T:Npgsql.NpgsqlCommand"/> object required to perform insertions.
            </returns>
        </member>
        <member name="M:Npgsql.NpgsqlCommandBuilder.GetInsertCommand(System.Boolean)">
            <summary>
            Gets the automatically generated <see cref="T:Npgsql.NpgsqlCommand"/> object required to perform insertions 
            at the data source, optionally using columns for parameter names.
            </summary>
            <param name="useColumnsForParameterNames">
            If <c>true</c>, generate parameter names matching column names, if possible. 
            If <c>false</c>, generate @p1, @p2, and so on.
            </param>
            <returns>
            The automatically generated <see cref="T:Npgsql.NpgsqlCommand"/> object required to perform insertions.
            </returns>
        </member>
        <member name="M:Npgsql.NpgsqlCommandBuilder.GetUpdateCommand">
            <summary>
            Gets the automatically generated System.Data.Common.DbCommand object required
            to perform updates at the data source.
            </summary>
            <returns>
            The automatically generated System.Data.Common.DbCommand object required to perform updates.
            </returns>
        </member>
        <member name="M:Npgsql.NpgsqlCommandBuilder.GetUpdateCommand(System.Boolean)">
            <summary>
            Gets the automatically generated <see cref="T:Npgsql.NpgsqlCommand"/> object required to perform updates
            at the data source, optionally using columns for parameter names.
            </summary>
            <param name="useColumnsForParameterNames">
            If <c>true</c>, generate parameter names matching column names, if possible. 
            If <c>false</c>, generate @p1, @p2, and so on.
            </param>
            <returns>
            The automatically generated <see cref="T:Npgsql.NpgsqlCommand"/> object required to perform updates.
            </returns>
        </member>
        <member name="M:Npgsql.NpgsqlCommandBuilder.GetDeleteCommand">
            <summary>
            Gets the automatically generated System.Data.Common.DbCommand object required
            to perform deletions at the data source.
            </summary>
            <returns>
            The automatically generated System.Data.Common.DbCommand object required to perform deletions.
            </returns>
        </member>
        <member name="M:Npgsql.NpgsqlCommandBuilder.GetDeleteCommand(System.Boolean)">
            <summary>
            Gets the automatically generated <see cref="T:Npgsql.NpgsqlCommand"/> object required to perform deletions 
            at the data source, optionally using columns for parameter names.
            </summary>
            <param name="useColumnsForParameterNames">
            If <c>true</c>, generate parameter names matching column names, if possible.
            If <c>false</c>, generate @p1, @p2, and so on.
            </param>
            <returns>
            The automatically generated <see cref="T:Npgsql.NpgsqlCommand"/> object required to perform deletions.
            </returns>
        </member>
        <member name="M:Npgsql.NpgsqlCommandBuilder.ApplyParameterInfo(System.Data.Common.DbParameter,System.Data.DataRow,System.Data.StatementType,System.Boolean)">
            <summary>
            Applies the parameter information.
            </summary>
            <param name="p">The parameter.</param>
            <param name="row">The row.</param>
            <param name="statementType">Type of the statement.</param>
            <param name="whereClause">if set to <c>true</c> [where clause].</param>
        </member>
        <member name="M:Npgsql.NpgsqlCommandBuilder.GetParameterName(System.Int32)">
            <summary>
            Returns the name of the specified parameter in the format of @p#.
            </summary>
            <param name="parameterOrdinal">The number to be included as part of the parameter's name..</param>
            <returns>
            The name of the parameter with the specified number appended as part of the parameter name.
            </returns>
        </member>
        <member name="M:Npgsql.NpgsqlCommandBuilder.GetParameterName(System.String)">
            <summary>
            Returns the full parameter name, given the partial parameter name.
            </summary>
            <param name="parameterName">The partial name of the parameter.</param>
            <returns>
            The full parameter name corresponding to the partial parameter name requested.
            </returns>
        </member>
        <member name="M:Npgsql.NpgsqlCommandBuilder.GetParameterPlaceholder(System.Int32)">
            <summary>
            Returns the placeholder for the parameter in the associated SQL statement.
            </summary>
            <param name="parameterOrdinal">The number to be included as part of the parameter's name.</param>
            <returns>
            The name of the parameter with the specified number appended.
            </returns>
        </member>
        <member name="M:Npgsql.NpgsqlCommandBuilder.SetRowUpdatingHandler(System.Data.Common.DbDataAdapter)">
            <summary>
            Registers the <see cref="T:NpgsqlCommandBuilder" /> to handle the <see cref="E:NpgsqlDataAdapter.RowUpdating"/> event for a <see cref="T:NpgsqlDataAdapter" />.
            </summary>
            <param name="adapter">The <see cref="T:System.Data.Common.DbDataAdapter" /> to be used for the update.</param>
        </member>
        <member name="M:Npgsql.NpgsqlCommandBuilder.RowUpdatingHandler(System.Object,NpgsqlRowUpdatingEventArgs)">
            <summary>
            Adds an event handler for the <see cref="E:Npgsql.NpgsqlDataAdapter.RowUpdating"/> event.
            </summary>
            <param name="sender">The sender</param>
            <param name="e">A <see cref="T:NpgsqlRowUpdatingEventArgs"/> instance containing information about the event.</param>
        </member>
        <member name="M:Npgsql.NpgsqlCommandBuilder.QuoteIdentifier(System.String)">
            <summary>
            Given an unquoted identifier in the correct catalog case, returns the correct quoted form of that identifier, including properly escaping any embedded quotes in the identifier.
            </summary>
            <param name="unquotedIdentifier">The original unquoted identifier.</param>
            <returns>
            The quoted version of the identifier. Embedded quotes within the identifier are properly escaped.
            </returns>
            <PermissionSet>
              <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*"/>
              </PermissionSet>
            <exception cref="T:System.ArgumentNullException">Unquoted identifier parameter cannot be null</exception>
        </member>
        <member name="M:Npgsql.NpgsqlCommandBuilder.UnquoteIdentifier(System.String)">
            <summary>
            Given a quoted identifier, returns the correct unquoted form of that identifier, including properly un-escaping any embedded quotes in the identifier.
            </summary>
            <param name="quotedIdentifier">The identifier that will have its embedded quotes removed.</param>
            <returns>
            The unquoted identifier, with embedded quotes properly un-escaped.
            </returns>
            <PermissionSet>
              <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*"/>
              </PermissionSet>
            <exception cref="T:System.ArgumentNullException">Quoted identifier parameter cannot be null</exception>
        </member>
        <member name="P:Npgsql.NpgsqlCommandBuilder.QuotePrefix">
            <summary>
            Gets or sets the beginning character or characters to use when specifying database objects (for example, tables or columns) whose names contain characters such as spaces or reserved tokens.
            </summary>
            <returns>
            The beginning character or characters to use. The default is an empty string.
              </returns>
              <PermissionSet>
              <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
              </PermissionSet>
        </member>
        <member name="P:Npgsql.NpgsqlCommandBuilder.QuoteSuffix">
            <summary>
            Gets or sets the ending character or characters to use when specifying database objects (for example, tables or columns) whose names contain characters such as spaces or reserved tokens.
            </summary>
            <returns>
            The ending character or characters to use. The default is an empty string.
              </returns>
              <PermissionSet>
              <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
              </PermissionSet>
        </member>
        <member name="T:Npgsql.NoticeEventHandler">
            <summary>
            Represents the method that handles the <see cref="E:Npgsql.NpgsqlConnection.Notification">Notice</see> events.
            </summary>
            <param name="sender">The source of the event.</param>
            <param name="e">A <see cref="T:Npgsql.NpgsqlNoticeEventArgs">NpgsqlNoticeEventArgs</see> that contains the event data.</param>
        </member>
        <member name="T:Npgsql.NotificationEventHandler">
            <summary>
            Represents the method that handles the <see cref="E:Npgsql.NpgsqlConnection.Notification">Notification</see> events.
            </summary>
            <param name="sender">The source of the event.</param>
            <param name="e">A <see cref="T:Npgsql.NpgsqlNotificationEventArgs">NpgsqlNotificationEventArgs</see> that contains the event data.</param>
        </member>
        <member name="T:Npgsql.NpgsqlConnection">
            <summary>
            This class represents a connection to a
            PostgreSQL server.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlConnection.#ctor">
            <summary>
            Initializes a new instance of the
            <see cref="T:Npgsql.NpgsqlConnection">NpgsqlConnection</see> class.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlConnection.#ctor(System.String)">
            <summary>
            Initializes a new instance of the
            <see cref="T:Npgsql.NpgsqlConnection">NpgsqlConnection</see> class
            and sets the <see cref="P:Npgsql.NpgsqlConnection.ConnectionString">ConnectionString</see>.
            </summary>
            <param name="ConnectionString">The connection used to open the PostgreSQL database.</param>
        </member>
        <member name="M:Npgsql.NpgsqlConnection.#ctor(Npgsql.NpgsqlConnectionStringBuilder)">
            <summary>
            Initializes a new instance of the
            <see cref="T:Npgsql.NpgsqlConnection">NpgsqlConnection</see> class
            and sets the <see cref="P:Npgsql.NpgsqlConnection.ConnectionString">ConnectionString</see>.
            </summary>
            <param name="ConnectionString">The connection used to open the PostgreSQL database.</param>
        </member>
        <member name="M:Npgsql.NpgsqlConnection.BeginDbTransaction(System.Data.IsolationLevel)">
            <summary>
            Begins a database transaction with the specified isolation level.
            </summary>
            <param name="isolationLevel">The <see cref="T:System.Data.IsolationLevel">isolation level</see> under which the transaction should run.</param>
            <returns>An <see cref="T:System.Data.Common.DbTransaction">DbTransaction</see>
            object representing the new transaction.</returns>
            <remarks>
            Currently the IsolationLevel ReadCommitted and Serializable are supported by the PostgreSQL backend.
            There's no support for nested transactions.
            </remarks>
        </member>
        <member name="M:Npgsql.NpgsqlConnection.BeginTransaction">
            <summary>
            Begins a database transaction.
            </summary>
            <returns>A <see cref="T:Npgsql.NpgsqlTransaction">NpgsqlTransaction</see>
            object representing the new transaction.</returns>
            <remarks>
            Currently there's no support for nested transactions.
            </remarks>
        </member>
        <member name="M:Npgsql.NpgsqlConnection.BeginTransaction(System.Data.IsolationLevel)">
            <summary>
            Begins a database transaction with the specified isolation level.
            </summary>
            <param name="level">The <see cref="T:System.Data.IsolationLevel">isolation level</see> under which the transaction should run.</param>
            <returns>A <see cref="T:Npgsql.NpgsqlTransaction">NpgsqlTransaction</see>
            object representing the new transaction.</returns>
            <remarks>
            Currently the IsolationLevel ReadCommitted and Serializable are supported by the PostgreSQL backend.
            There's no support for nested transactions.
            </remarks>
        </member>
        <member name="M:Npgsql.NpgsqlConnection.Open">
            <summary>
            Opens a database connection with the property settings specified by the
            <see cref="P:Npgsql.NpgsqlConnection.ConnectionString">ConnectionString</see>.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlConnection.ChangeDatabase(System.String)">
            <summary>
            This method changes the current database by disconnecting from the actual
            database and connecting to the specified.
            </summary>
            <param name="dbName">The name of the database to use in place of the current database.</param>
        </member>
        <member name="M:Npgsql.NpgsqlConnection.Close">
            <summary>
            Releases the connection to the database.  If the connection is pooled, it will be
               made available for re-use.  If it is non-pooled, the actual connection will be shutdown.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlConnection.PromotableLocalTransactionEnded">
            <summary>
            When a connection is closed within an enclosing TransactionScope and the transaction
            hasn't been promoted, we defer the actual closing until the scope ends.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlConnection.CreateDbCommand">
            <summary>
            Creates and returns a <see cref="T:System.Data.Common.DbCommand">DbCommand</see>
            object associated with the <see cref="T:System.Data.Common.DbConnection">IDbConnection</see>.
            </summary>
            <returns>A <see cref="T:System.Data.Common.DbCommand">DbCommand</see> object.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlConnection.CreateCommand">
            <summary>
            Creates and returns a <see cref="T:Npgsql.NpgsqlCommand">NpgsqlCommand</see>
            object associated with the <see cref="T:Npgsql.NpgsqlConnection">NpgsqlConnection</see>.
            </summary>
            <returns>A <see cref="T:Npgsql.NpgsqlCommand">NpgsqlCommand</see> object.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlConnection.Dispose(System.Boolean)">
            <summary>
            Releases all resources used by the
            <see cref="T:Npgsql.NpgsqlConnection">NpgsqlConnection</see>.
            </summary>
            <param name="disposing"><b>true</b> when called from Dispose();
            <b>false</b> when being called from the finalizer.</param>
        </member>
        <member name="M:Npgsql.NpgsqlConnection.System#ICloneable#Clone">
            <summary>
            Create a new connection based on this one.
            </summary>
            <returns>A new NpgsqlConnection object.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlConnection.Clone">
            <summary>
            Create a new connection based on this one.
            </summary>
            <returns>A new NpgsqlConnection object.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlConnection.CopyConnectionStringBuilder">
            <summary>
            Returns a copy of the NpgsqlConnectionStringBuilder that contains the parsed connection string values.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlConnection.DefaultCertificateSelectionCallback(System.Security.Cryptography.X509Certificates.X509CertificateCollection,System.Security.Cryptography.X509Certificates.X509Certificate,System.String,System.Security.Cryptography.X509Certificates.X509CertificateCollection)">
            <summary>
            Default SSL CertificateSelectionCallback implementation.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlConnection.DefaultCertificateValidationCallback(System.Security.Cryptography.X509Certificates.X509Certificate,System.Int32[])">
            <summary>
            Default SSL CertificateValidationCallback implementation.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlConnection.DefaultPrivateKeySelectionCallback(System.Security.Cryptography.X509Certificates.X509Certificate,System.String)">
            <summary>
            Default SSL PrivateKeySelectionCallback implementation.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlConnection.DefaultProvideClientCertificatesCallback(System.Security.Cryptography.X509Certificates.X509CertificateCollection)">
            <summary>
            Default SSL ProvideClientCertificatesCallback implementation.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlConnection.DefaultValidateRemoteCertificateCallback(System.Security.Cryptography.X509Certificates.X509Certificate,System.Security.Cryptography.X509Certificates.X509Chain,System.Net.Security.SslPolicyErrors)">
            <summary>
            Default SSL ValidateRemoteCertificateCallback implementation.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlConnection.LogConnectionString">
            <summary>
            Write each key/value pair in the connection string to the log.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlConnection.LoadConnectionStringBuilder(System.String)">
            <summary>
            Sets the `settings` ConnectionStringBuilder based on the given `connectionString`
            </summary>
            <param name="connectionString">The connection string to load the builder from</param>
        </member>
        <member name="M:Npgsql.NpgsqlConnection.LoadConnectionStringBuilder(Npgsql.NpgsqlConnectionStringBuilder)">
            <summary>
            Sets the `settings` ConnectionStringBuilder based on the given `connectionString`
            </summary>
            <param name="connectionString">The connection string to load the builder from</param>
        </member>
        <member name="M:Npgsql.NpgsqlConnection.RefreshConnectionString">
            <summary>
            Refresh the cached _connectionString whenever the builder settings change
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlConnection.GetSchema">
            <summary>
            Returns the supported collections
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlConnection.GetSchema(System.String)">
            <summary>
            Returns the schema collection specified by the collection name.
            </summary>
            <param name="collectionName">The collection name.</param>
            <returns>The collection specified.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlConnection.GetSchema(System.String,System.String[])">
            <summary>
            Returns the schema collection specified by the collection name filtered by the restrictions.
            </summary>
            <param name="collectionName">The collection name.</param>
            <param name="restrictions">
            The restriction values to filter the results.  A description of the restrictions is contained
            in the Restrictions collection.
            </param>
            <returns>The collection specified.</returns>
        </member>
        <member name="E:Npgsql.NpgsqlConnection.Notice">
            <summary>
            Occurs on NoticeResponses from the PostgreSQL backend.
            </summary>
        </member>
        <member name="E:Npgsql.NpgsqlConnection.Notification">
            <summary>
            Occurs on NotificationResponses from the PostgreSQL backend.
            </summary>
        </member>
        <member name="E:Npgsql.NpgsqlConnection.ProvideClientCertificatesCallback">
            <summary>
            Called to provide client certificates for SSL handshake.
            </summary>
        </member>
        <member name="E:Npgsql.NpgsqlConnection.CertificateSelectionCallback">
            <summary>
            Mono.Security.Protocol.Tls.CertificateSelectionCallback delegate.
            </summary>
        </member>
        <member name="E:Npgsql.NpgsqlConnection.CertificateValidationCallback">
            <summary>
            Mono.Security.Protocol.Tls.CertificateValidationCallback delegate.
            </summary>
        </member>
        <member name="E:Npgsql.NpgsqlConnection.PrivateKeySelectionCallback">
            <summary>
            Mono.Security.Protocol.Tls.PrivateKeySelectionCallback delegate.
            </summary>
        </member>
        <member name="E:Npgsql.NpgsqlConnection.ValidateRemoteCertificateCallback">
            <summary>
            Called to validate server's certificate during SSL handshake
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnection.ConnectionString">
            <summary>
            Gets or sets the string used to connect to a PostgreSQL database.
            Valid values are:
            <ul>
            <li>
            Server:             Address/Name of Postgresql Server;
            </li>
            <li>
            Port:               Port to connect to;
            </li>
            <li>
            Protocol:           Protocol version to use, instead of automatic; Integer 2 or 3;
            </li>
            <li>
            Database:           Database name. Defaults to user name if not specified;
            </li>
            <li>
            User Id:            User name;
            </li>
            <li>
            Password:           Password for clear text authentication;
            </li>
            <li>
            SSL:                True or False. Controls whether to attempt a secure connection. Default = False;
            </li>
            <li>
            Pooling:            True or False. Controls whether connection pooling is used. Default = True;
            </li>
            <li>
            MinPoolSize:        Min size of connection pool;
            </li>
            <li>
            MaxPoolSize:        Max size of connection pool;
            </li>
            <li>
            Timeout:            Time to wait for connection open in seconds. Default is 15.
            </li>
            <li>
            CommandTimeout:     Time to wait for command to finish execution before throw an exception. In seconds. Default is 20.
            </li>
            <li>
            Sslmode:            Mode for ssl connection control. Can be Prefer, Require, Allow or Disable. Default is Disable. Check user manual for explanation of values.
            </li>
            <li>
            ConnectionLifeTime: Time to wait before closing unused connections in the pool in seconds. Default is 15.
            </li>
            <li>
            SyncNotification:   Specifies if Npgsql should use synchronous notifications.
            </li>
            <li>
            SearchPath: Changes search path to specified and public schemas.
            </li>
            </ul>
            </summary>
            <value>The connection string that includes the server name,
            the database name, and other parameters needed to establish
            the initial connection. The default value is an empty string.
            </value>
        </member>
        <member name="P:Npgsql.NpgsqlConnection.Host">
            <summary>
            Backend server host name.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnection.Port">
            <summary>
            Backend server port.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnection.SSL">
            <summary>
            If true, the connection will attempt to use SSL.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnection.ConnectionTimeout">
            <summary>
            Gets the time to wait while trying to establish a connection
            before terminating the attempt and generating an error.
            </summary>
            <value>The time (in seconds) to wait for a connection to open. The default value is 15 seconds.</value>
        </member>
        <member name="P:Npgsql.NpgsqlConnection.CommandTimeout">
            <summary>
            Gets the time to wait while trying to execute a command
            before terminating the attempt and generating an error.
            </summary>
            <value>The time (in seconds) to wait for a command to complete. The default value is 20 seconds.</value>
        </member>
        <member name="P:Npgsql.NpgsqlConnection.ConnectionLifeTime">
            <summary>
            Gets the time to wait before closing unused connections in the pool if the count
            of all connections exeeds MinPoolSize.
            </summary>
            <remarks>
            If connection pool contains unused connections for ConnectionLifeTime seconds,
            the half of them will be closed. If there will be unused connections in a second
            later then again the half of them will be closed and so on.
            This strategy provide smooth change of connection count in the pool.
            </remarks>
            <value>The time (in seconds) to wait. The default value is 15 seconds.</value>
        </member>
        <member name="P:Npgsql.NpgsqlConnection.Database">
            <summary>
             Gets the name of the current database or the database to be used after a connection is opened.
             </summary>
             <value>The name of the current database or the name of the database to be
             used after a connection is opened. The default value is the empty string.</value>
        </member>
        <member name="P:Npgsql.NpgsqlConnection.PreloadReader">
            <summary>
            Whether datareaders are loaded in their entirety (for compatibility with earlier code).
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnection.DataSource">
            <summary>
            Gets the database server name.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnection.SyncNotification">
            <summary>
            Gets flag indicating if we are using Synchronous notification or not.
            The default value is false.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnection.FullState">
            <summary>
            Gets the current state of the connection.
            </summary>
            <value>A bitwise combination of the <see cref="T:System.Data.ConnectionState">ConnectionState</see> values. The default is <b>Closed</b>.</value>
        </member>
        <member name="P:Npgsql.NpgsqlConnection.State">
            <summary>
            Gets whether the current state of the connection is Open or Closed
            </summary>
            <value>ConnectionState.Open or ConnectionState.Closed</value>
        </member>
        <member name="P:Npgsql.NpgsqlConnection.PostgreSqlVersion">
            <summary>
            Version of the PostgreSQL backend.
            This can only be called when there is an active connection.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnection.BackendProtocolVersion">
            <summary>
            Protocol version in use.
            This can only be called when there is an active connection.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnection.ProcessID">
            <summary>
            Process id of backend server.
            This can only be called when there is an active connection.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnection.UseConformantStrings">
            <summary>
            Report whether the backend is expecting standard conformant strings.
            In version 8.1, Postgres began reporting this value (false), but did not actually support standard conformant strings.
            In version 8.2, Postgres began supporting standard conformant strings, but defaulted this flag to false.
            As of version 9.1, this flag defaults to true.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnection.Supports_E_StringPrefix">
            <summary>
            Report whether the backend understands the string literal E prefix (>= 8.1).
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnection.SupportsHexByteFormat">
            <summary>
            Report whether the backend understands the hex byte format (>= 9.0).
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnection.Connector">
            <summary>
            The connector object connected to the backend.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnection.ConnectionStringValues">
            <summary>
            Gets the NpgsqlConnectionStringBuilder containing the parsed connection string values.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnection.UserName">
            <summary>
            User name.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnection.Password">
            <summary>
            Password.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnection.Pooling">
            <summary>
            Determine if connection pooling will be used for this connection.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlConnectionStringBuilder.Clone">
            <summary>
            Return an exact copy of this NpgsqlConnectionString.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlConnectionStringBuilder.CheckIntegratedSecuritySupport">
            <summary>
            No integrated security if we're on mono and .NET 4.5 because of ClaimsIdentity,
            see https://github.com/npgsql/Npgsql/issues/133
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlConnectionStringBuilder.SetValue(System.String,Npgsql.Keywords,System.Object)">
            <summary>
            This function will set value for known key, both private member and base[key].
            </summary>
            <param name="keyword"></param>
            <param name="key"></param>
            <param name="value"></param>
            <returns>value, coerced as needed to the stored type.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlConnectionStringBuilder.SetValue(Npgsql.Keywords,System.Object)">
            <summary>
            The function will modify private member only, not base[key].
            </summary>
            <param name="keyword"></param>
            <param name="value"></param>
            <returns>value, coerced as needed to the stored type.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlConnectionStringBuilder.GetValue(Npgsql.Keywords)">
            <summary>
            The function will access private member only, not base[key].
            </summary>
            <param name="keyword"></param>
            <returns>value.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlConnectionStringBuilder.Clear">
            <summary>
            Clear the member and assign them to the default value.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.Host">
            <summary>
            Gets or sets the backend server host name.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.Port">
            <summary>
            Gets or sets the backend server port.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.Protocol">
            <summary>
            Gets or sets the specified backend communication protocol version.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.Database">
            <summary>
             Gets or sets the name of the database to be used after a connection is opened.
             </summary>
             <value>The name of the database to be
             used after a connection is opened.</value>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.UserName">
            <summary>
            Gets or sets the login user name.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.WindowsIdentityUserName">
            <summary>
            This is a pretty horrible hack to fix https://github.com/npgsql/Npgsql/issues/133
            In a nutshell, starting with .NET 4.5 WindowsIdentity inherits from ClaimsIdentity
            which doesn't exist in mono, and calling UserName getter above bombs.
            The workaround is that the function that actually deals with WindowsIdentity never
            gets called on mono, so never gets JITted and the problem goes away.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.PasswordAsByteArray">
            <summary>
            Gets or sets the login password as a UTF8 encoded byte array.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.Password">
            <summary>
            Sets the login password as a string.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.SSL">
            <summary>
            Gets or sets a value indicating whether to attempt to use SSL.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.SslMode">
            <summary>
            Gets or sets a value indicating whether to attempt to use SSL.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.Encoding">
            <summary>
            Gets the backend encoding.  Always returns "UTF8".
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.Timeout">
            <summary>
            Gets or sets the time to wait while trying to establish a connection
            before terminating the attempt and generating an error.
            </summary>
            <value>The time (in seconds) to wait for a connection to open. The default value is 15 seconds.</value>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.SearchPath">
            <summary>
            Gets or sets the schema search path.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.Pooling">
            <summary>
            Gets or sets a value indicating whether connection pooling should be used.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.ConnectionLifeTime">
            <summary>
            Gets or sets the time to wait before closing unused connections in the pool if the count
            of all connections exeeds MinPoolSize.
            </summary>
            <remarks>
            If connection pool contains unused connections for ConnectionLifeTime seconds,
            the half of them will be closed. If there will be unused connections in a second
            later then again the half of them will be closed and so on.
            This strategy provide smooth change of connection count in the pool.
            </remarks>
            <value>The time (in seconds) to wait. The default value is 15 seconds.</value>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.MinPoolSize">
            <summary>
            Gets or sets the minimum connection pool size.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.MaxPoolSize">
            <summary>
            Gets or sets the maximum connection pool size.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.SyncNotification">
            <summary>
            Gets or sets a value indicating whether to listen for notifications and report them between command activity.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.CommandTimeout">
            <summary>
            Gets the time to wait while trying to execute a command
            before terminating the attempt and generating an error.
            </summary>
            <value>The time (in seconds) to wait for a command to complete. The default value is 20 seconds.</value>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.PreloadReader">
            <summary>
            Gets or sets a value indicating whether datareaders are loaded in their entirety (for compatibility with earlier code).
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.Compatible">
            <summary>
            Compatibilty version. When possible, behaviour caused by breaking changes will be preserved
            if this version is less than that where the breaking change was introduced.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.ApplicationName">
            <summary>
            Gets or sets the ootional application name parameter to be sent to the backend during connection initiation.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.AlwaysPrepare">
            <summary>
            Gets or sets a value indicating whether to silently Prepare() all commands before execution.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnectionStringBuilder.Item(System.String)">
            <summary>
            Case insensative accessor for indivual connection string values.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlConnectionStringBuilder.ValueDescription.#ctor(System.Type,System.Boolean,Npgsql.NpgsqlConnectionStringBuilder.ValueNativeToString)">
            <summary>
            Set both ImplicitDefault and ExplicitDefault to the <paramref name="t"/>'s default value.
            </summary>
            <param name="t"></param>
            <param name="storeInBase"></param>
            <param name="nativeToString"></param>
        </member>
        <member name="M:Npgsql.NpgsqlConnectionStringBuilder.ValueDescription.#ctor(System.IComparable,System.Boolean,Npgsql.NpgsqlConnectionStringBuilder.ValueNativeToString)">
            <summary>
            Set ImplicitDefault to the default value of <paramref name="explicitDefault"/>'s type,
            and ExplicitDefault to <paramref name="explicitDefault"/>.
            </summary>
            <param name="explicitDefault"></param>
            <param name="storeInBase"></param>
            <param name="nativeToString"></param>
        </member>
        <member name="T:Npgsql.ProvideClientCertificatesCallback">
            <summary>
            Represents the method that allows the application to provide a certificate collection to be used for SSL clien authentication
            </summary>
            <param name="certificates">A <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection">X509CertificateCollection</see> to be filled with one or more client certificates.</param>
        </member>
        <member name="T:Npgsql.ValidateRemoteCertificateCallback">
            <summary>
            Represents the method that is called to validate the certificate provided by the server during an SSL handshake
            </summary>
            <param name="cert">The server's certificate</param>
            <param name="chain">The certificate chain containing the certificate's CA and any intermediate authorities</param>
            <param name="errors">Any errors that were detected</param>
        </member>
        <member name="T:Npgsql.NpgsqlConnector">
            <summary>
            !!! Helper class, for compilation only.
            Connector implements the logic for the Connection Objects to
            access the physical connection to the database, and isolate
            the application developer from connection pooling internals.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlConnector.#ctor(Npgsql.NpgsqlConnectionStringBuilder,System.Boolean,System.Boolean)">
            <summary>
            Constructor.
            </summary>
            <param name="ConnectionString">Connection string.</param>
            <param name="Pooled">Pooled</param>
            <param name="Shared">Controls whether the connector can be shared.</param>
        </member>
        <member name="M:Npgsql.NpgsqlConnector.IsValid">
            <summary>
            This method checks if the connector is still ok.
            We try to send a simple query text, select 1 as ConnectionTest;
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlConnector.ReleaseResources">
            <summary>
            This method is responsible for releasing all resources associated with this Connector.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlConnector.ReleasePlansPortals">
            <summary>
            This method is responsible to release all portals used by this Connector.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlConnector.DefaultCertificateSelectionCallback(System.Security.Cryptography.X509Certificates.X509CertificateCollection,System.Security.Cryptography.X509Certificates.X509Certificate,System.String,System.Security.Cryptography.X509Certificates.X509CertificateCollection)">
            <summary>
            Default SSL CertificateSelectionCallback implementation.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlConnector.DefaultCertificateValidationCallback(System.Security.Cryptography.X509Certificates.X509Certificate,System.Int32[])">
            <summary>
            Default SSL CertificateValidationCallback implementation.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlConnector.DefaultPrivateKeySelectionCallback(System.Security.Cryptography.X509Certificates.X509Certificate,System.String)">
            <summary>
            Default SSL PrivateKeySelectionCallback implementation.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlConnector.DefaultProvideClientCertificatesCallback(System.Security.Cryptography.X509Certificates.X509CertificateCollection)">
            <summary>
            Default SSL ProvideClientCertificatesCallback implementation.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlConnector.DefaultValidateRemoteCertificateCallback(System.Object,System.Security.Cryptography.X509Certificates.X509Certificate,System.Security.Cryptography.X509Certificates.X509Chain,System.Net.Security.SslPolicyErrors)">
            <summary>
            Default SSL ValidateRemoteCertificateCallback implementation.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlConnector.ProcessServerVersion">
            <summary>
            This method is required to set all the version dependent features flags.
            SupportsPrepare means the server can use prepared query plans (7.3+)
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlConnector.Open">
            <summary>
            Opens the physical connection to the server.
            </summary>
            <remarks>Usually called by the RequestConnector
            Method of the connection pool manager.</remarks>
        </member>
        <member name="M:Npgsql.NpgsqlConnector.Close">
            <summary>
            Closes the physical connection to the server.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlConnector.NextPortalName">
            <summary>
             Returns next portal index.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlConnector.NextPlanName">
            <summary>
             Returns next plan index.
            </summary>
        </member>
        <member name="E:Npgsql.NpgsqlConnector.Notice">
            <summary>
            Occurs on NoticeResponses from the PostgreSQL backend.
            </summary>
        </member>
        <member name="E:Npgsql.NpgsqlConnector.Notification">
            <summary>
            Occurs on NotificationResponses from the PostgreSQL backend.
            </summary>
        </member>
        <member name="E:Npgsql.NpgsqlConnector.ProvideClientCertificatesCallback">
            <summary>
            Called to provide client certificates for SSL handshake.
            </summary>
        </member>
        <member name="E:Npgsql.NpgsqlConnector.CertificateSelectionCallback">
            <summary>
            Mono.Security.Protocol.Tls.CertificateSelectionCallback delegate.
            </summary>
        </member>
        <member name="E:Npgsql.NpgsqlConnector.CertificateValidationCallback">
            <summary>
            Mono.Security.Protocol.Tls.CertificateValidationCallback delegate.
            </summary>
        </member>
        <member name="E:Npgsql.NpgsqlConnector.PrivateKeySelectionCallback">
            <summary>
            Mono.Security.Protocol.Tls.PrivateKeySelectionCallback delegate.
            </summary>
        </member>
        <member name="E:Npgsql.NpgsqlConnector.ValidateRemoteCertificateCallback">
            <summary>
            Called to validate server's certificate during SSL handshake
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnector.State">
            <summary>
            Gets the current state of the connection.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnector.ConnectionString">
            <summary>
            Return Connection String.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnector.ServerVersion">
            <summary>
            Version of backend server this connector is connected to.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnector.BackendProtocolVersion">
            <summary>
            Backend protocol version in use by this connector.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnector.Socket">
            <summary>
            The physical connection socket to the backend.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnector.BaseStream">
            <summary>
            The physical connection stream to the backend.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnector.Stream">
            <summary>
            The top level stream to the backend.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnector.IsInitialized">
            <summary>
            Reports if this connector is fully connected.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnector.Mediator">
            <summary>
            The connection mediator.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnector.Transaction">
            <summary>
            Report if the connection is in a transaction.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnector.SupportsPrepare">
            <summary>
            Report whether the current connection can support prepare functionality.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlConnector.NativeToBackendTypeConverterOptions">
            <summary>
            Options that control certain aspects of native to backend conversions that depend
            on backend version and status.
            </summary>
        </member>
        <member name="T:Npgsql.NpgsqlConnectorPool">
            <summary>
            This class manages all connector objects, pooled AND non-pooled.
            </summary>
        </member>
        <member name="F:Npgsql.NpgsqlConnectorPool.ConnectorPoolMgr">
            <value>Unique static instance of the connector pool
            mamager.</value>
        </member>
        <member name="F:Npgsql.NpgsqlConnectorPool.PooledConnectors">
            <value>Map of index to unused pooled connectors, avaliable to the
            next RequestConnector() call.</value>
            <remarks>This hashmap will be indexed by connection string.
            This key will hold a list of queues of pooled connectors available to be used.</remarks>
        </member>
        <member name="F:Npgsql.NpgsqlConnectorPool.Timer">
            <value>Timer for tracking unused connections in pools.</value>
        </member>
        <member name="M:Npgsql.NpgsqlConnectorPool.RequestConnector(Npgsql.NpgsqlConnection)">
            <summary>
            Searches the shared and pooled connector lists for a
            matching connector object or creates a new one.
            </summary>
            <param name="Connection">The NpgsqlConnection that is requesting
            the connector. Its ConnectionString will be used to search the
            pool for available connectors.</param>
            <returns>A connector object.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlConnectorPool.RequestPooledConnectorInternal(Npgsql.NpgsqlConnection)">
            <summary>
            Find a pooled connector.  Handle shared/non-shared here.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlConnectorPool.ReleaseConnector(Npgsql.NpgsqlConnection,Npgsql.NpgsqlConnector)">
            <summary>
            Releases a connector, possibly back to the pool for future use.
            </summary>
            <remarks>
            Pooled connectors will be put back into the pool if there is room.
            Shared connectors should just have their use count decremented
            since they always stay in the shared pool.
            </remarks>
            <param name="Connection">Connection to which the connector is leased.</param>
            <param name="Connector">The connector to release.</param>
        </member>
        <member name="M:Npgsql.NpgsqlConnectorPool.ReleaseConnectorInternal(Npgsql.NpgsqlConnection,Npgsql.NpgsqlConnector)">
            <summary>
            Release a pooled connector.  Handle shared/non-shared here.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlConnectorPool.GetPooledConnector(Npgsql.NpgsqlConnection)">
            <summary>
            Find an available pooled connector in the non-shared pool, or create
            a new one if none found.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlConnectorPool.UngetConnector(Npgsql.NpgsqlConnection,Npgsql.NpgsqlConnector)">
            <summary>
            Put a pooled connector into the pool queue.
            </summary>
            <param name="Connection">Connection <paramref name="Connector"/> is leased to.</param>
            <param name="Connector">Connector to pool</param>
        </member>
        <member name="T:Npgsql.NpgsqlConnectorPool.ConnectorQueue">
            <summary>
            A queue with an extra Int32 for keeping track of busy connections.
            </summary>
        </member>
        <member name="F:Npgsql.NpgsqlConnectorPool.ConnectorQueue.Available">
            <summary>
            Connections available to the end user
            </summary>
        </member>
        <member name="F:Npgsql.NpgsqlConnectorPool.ConnectorQueue.Busy">
            <summary>
            Connections currently in use
            </summary>
        </member>
        <member name="T:Npgsql.NpgsqlCopyFormat">
            <summary>
            Represents information about COPY operation data transfer format as returned by server.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlCopyFormat.#ctor(System.Byte,System.Int16[])">
            <summary>
            Only created when a CopyInResponse or CopyOutResponse is received by NpgsqlState.ProcessBackendResponses()
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlCopyFormat.FieldIsBinary(System.Int32)">
            <summary>
            Returns true if this operation is currently active and field at given location is in binary format.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlCopyFormat.IsBinary">
            <summary>
            Returns true if this operation is currently active and in binary format.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlCopyFormat.FieldCount">
            <summary>
            Returns number of fields if this operation is currently active, otherwise -1
            </summary>
        </member>
        <member name="T:Npgsql.NpgsqlCopyIn">
            <summary>
            Represents a PostgreSQL COPY FROM STDIN operation with a corresponding SQL statement
            to execute against a PostgreSQL database
            and an associated stream used to read data from (if provided by user)
            or for writing it (when generated by driver).
            Eg. new NpgsqlCopyIn("COPY mytable FROM STDIN", connection, streamToRead).Start();
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlCopyIn.#ctor(System.String,Npgsql.NpgsqlConnection)">
            <summary>
            Creates NpgsqlCommand to run given query upon Start(). Data for the requested COPY IN operation can then be written to CopyData stream followed by a call to End() or Cancel().
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlCopyIn.#ctor(Npgsql.NpgsqlCommand,Npgsql.NpgsqlConnection)">
            <summary>
            Given command is run upon Start(). Data for the requested COPY IN operation can then be written to CopyData stream followed by a call to End() or Cancel().
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlCopyIn.#ctor(Npgsql.NpgsqlCommand,Npgsql.NpgsqlConnection,System.IO.Stream)">
            <summary>
            Given command is executed upon Start() and all data from fromStream is passed to it as copy data.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlCopyIn.FieldIsBinary(System.Int32)">
            <summary>
            Returns true if this operation is currently active and field at given location is in binary format.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlCopyIn.Start">
            <summary>
            Command specified upon creation is executed as a non-query.
            If CopyStream is set upon creation, it will be flushed to server as copy data, and operation will be finished immediately.
            Otherwise the CopyStream member can be used for writing copy data to server and operation finished with a call to End() or Cancel().
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlCopyIn.End">
            <summary>
            Called after writing all data to CopyStream to successfully complete this copy operation.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlCopyIn.Cancel(System.String)">
            <summary>
            Withdraws an already started copy operation. The operation will fail with given error message.
            Will do nothing if current operation is not active.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlCopyIn.IsActive">
            <summary>
            Returns true if the connection is currently reserved for this operation.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlCopyIn.CopyStream">
            <summary>
            The stream provided by user or generated upon Start().
            User may provide a stream to constructor; it is used to pass to server all data read from it.
            Otherwise, call to Start() sets this to a writable NpgsqlCopyInStream that passes all data written to it to server.
            In latter case this is only available while the copy operation is active and null otherwise.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlCopyIn.IsBinary">
            <summary>
            Returns true if this operation is currently active and in binary format.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlCopyIn.FieldCount">
            <summary>
            Returns number of fields expected on each input row if this operation is currently active, otherwise -1
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlCopyIn.NpgsqlCommand">
            <summary>
            The Command used to execute this copy operation.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlCopyIn.CopyBufferSize">
            <summary>
            Set before a COPY IN query to define size of internal buffer for reading from given CopyStream.
            </summary>
        </member>
        <member name="T:Npgsql.NpgsqlCopyInState">
            <summary>
            Represents an ongoing COPY FROM STDIN operation.
            Provides methods to push data to server and end or cancel the operation.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlCopyInState.StartCopy(Npgsql.NpgsqlConnector,Npgsql.NpgsqlCopyFormat)">
            <summary>
            Called from NpgsqlState.ProcessBackendResponses upon CopyInResponse.
            If CopyStream is already set, it is used to read data to push to server, after which the copy is completed.
            Otherwise CopyStream is set to a writable NpgsqlCopyInStream that calls SendCopyData each time it is written to.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlCopyInState.SendCopyData(Npgsql.NpgsqlConnector,System.Byte[],System.Int32,System.Int32)">
            <summary>
            Sends given packet to server as a CopyData message.
            Does not check for notifications! Use another thread for that.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlCopyInState.SendCopyDone(Npgsql.NpgsqlConnector)">
            <summary>
            Sends CopyDone message to server. Handles responses, ie. may throw an exception.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlCopyInState.SendCopyFail(Npgsql.NpgsqlConnector,System.String)">
            <summary>
            Sends CopyFail message to server. Handles responses, ie. should always throw an exception:
            in CopyIn state the server responds to CopyFail with an error response;
            outside of a CopyIn state the server responds to CopyFail with an error response;
            without network connection or whatever, there's going to eventually be a failure, timeout or user intervention.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlCopyInState.CopyFormat">
            <summary>
            Copy format information returned from server.
            </summary>
        </member>
        <member name="T:Npgsql.NpgsqlCopyInStream">
            <summary>
            Stream for writing data to a table on a PostgreSQL version 7.4 or newer database during an active COPY FROM STDIN operation.
            <b>Passes data exactly as is and when given</b>, so see to it that you use server encoding, correct format and reasonably sized writes!
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlCopyInStream.#ctor(Npgsql.NpgsqlConnector)">
            <summary>
            Created only by NpgsqlCopyInState.StartCopy()
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlCopyInStream.Close">
            <summary>
            Successfully completes copying data to server. Returns after operation is finished.
            Does nothing if this stream is not the active copy operation writer.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlCopyInStream.Cancel(System.String)">
            <summary>
            Withdraws an already started copy operation. The operation will fail with given error message.
            Does nothing if this stream is not the active copy operation writer.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlCopyInStream.Write(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Writes given bytes to server.
            Fails if this stream is not the active copy operation writer.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlCopyInStream.Flush">
            <summary>
            Flushes stream contents to server.
            Fails if this stream is not the active copy operation writer.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlCopyInStream.Read(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Not readable
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlCopyInStream.Seek(System.Int64,System.IO.SeekOrigin)">
            <summary>
            Not seekable
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlCopyInStream.SetLength(System.Int64)">
            <summary>
            Not supported
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlCopyInStream.IsActive">
            <summary>
            True while this stream can be used to write copy data to server
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlCopyInStream.CanRead">
            <summary>
            False
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlCopyInStream.CanWrite">
            <summary>
            True
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlCopyInStream.CanSeek">
            <summary>
            False
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlCopyInStream.Length">
            <summary>
            Number of bytes written so far
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlCopyInStream.Position">
            <summary>
            Number of bytes written so far; not settable
            </summary>
        </member>
        <member name="T:Npgsql.NpgsqlCopyOut">
            <summary>
            Represents a PostgreSQL COPY TO STDOUT operation with a corresponding SQL statement
            to execute against a PostgreSQL database
            and an associated stream used to write results to (if provided by user)
            or for reading the results (when generated by driver).
            Eg. new NpgsqlCopyOut("COPY (SELECT * FROM mytable) TO STDOUT", connection, streamToWrite).Start();
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlCopyOut.#ctor(System.String,Npgsql.NpgsqlConnection)">
            <summary>
            Creates NpgsqlCommand to run given query upon Start(), after which CopyStream provides data from database as requested in the query.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlCopyOut.#ctor(Npgsql.NpgsqlCommand,Npgsql.NpgsqlConnection)">
            <summary>
            Given command is run upon Start(), after which CopyStream provides data from database as requested in the query.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlCopyOut.#ctor(Npgsql.NpgsqlCommand,Npgsql.NpgsqlConnection,System.IO.Stream)">
            <summary>
            Given command is executed upon Start() and all requested copy data is written to toStream immediately.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlCopyOut.FieldIsBinary(System.Int32)">
            <summary>
            Returns true if this operation is currently active and field at given location is in binary format.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlCopyOut.Start">
            <summary>
            Command specified upon creation is executed as a non-query.
            If CopyStream is set upon creation, all copy data from server will be written to it, and operation will be finished immediately.
            Otherwise the CopyStream member can be used for reading copy data from server until no more data is available.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlCopyOut.End">
            <summary>
            Flush generated CopyStream at once. Effectively reads and discard all the rest of copy data from server.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlCopyOut.IsActive">
            <summary>
            Returns true if the connection is currently reserved for this operation.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlCopyOut.CopyStream">
            <summary>
            The stream provided by user or generated upon Start()
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlCopyOut.NpgsqlCommand">
            <summary>
            The Command used to execute this copy operation.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlCopyOut.IsBinary">
            <summary>
            Returns true if this operation is currently active and in binary format.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlCopyOut.FieldCount">
            <summary>
            Returns number of fields if this operation is currently active, otherwise -1
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlCopyOut.Read">
            <summary>
            Faster alternative to using the generated CopyStream.
            </summary>
        </member>
        <member name="T:Npgsql.NpgsqlCopyOutState">
            <summary>
            Represents an ongoing COPY TO STDOUT operation.
            Provides methods to read data from server or end the operation.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlCopyOutState.StartCopy(Npgsql.NpgsqlConnector,Npgsql.NpgsqlCopyFormat)">
            <summary>
            Called from NpgsqlState.ProcessBackendResponses upon CopyOutResponse.
            If CopyStream is already set, it is used to write data received from server, after which the copy ends.
            Otherwise CopyStream is set to a readable NpgsqlCopyOutStream that receives data from server.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlCopyOutState.GetCopyData(Npgsql.NpgsqlConnector)">
            <summary>
            Called from NpgsqlOutStream.Read to read copy data from server.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlCopyOutState.CopyFormat">
            <summary>
            Copy format information returned from server.
            </summary>
        </member>
        <member name="T:Npgsql.NpgsqlCopyOutStream">
            <summary>
            Stream for reading data from a table or select on a PostgreSQL version 7.4 or newer database during an active COPY TO STDOUT operation.
            <b>Passes data exactly as provided by the server.</b>
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlCopyOutStream.#ctor(Npgsql.NpgsqlConnector)">
            <summary>
            Created only by NpgsqlCopyOutState.StartCopy()
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlCopyOutStream.Close">
            <summary>
            Discards copy data as long as server pushes it. Returns after operation is finished.
            Does nothing if this stream is not the active copy operation reader.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlCopyOutStream.Write(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Not writable.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlCopyOutStream.Flush">
            <summary>
            Not flushable.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlCopyOutStream.Read(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Copies data read from server to given byte buffer.
            Since server returns data row by row, length will differ each time, but it is only zero once the operation ends.
            Can be mixed with calls to the more efficient NpgsqlCopyOutStream.Read() : byte[] though that would not make much sense.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlCopyOutStream.Seek(System.Int64,System.IO.SeekOrigin)">
            <summary>
            Not seekable
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlCopyOutStream.SetLength(System.Int64)">
            <summary>
            Not supported
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlCopyOutStream.Read">
            <summary>
            Returns a whole row of data from server without extra work.
            If standard Stream.Read(...) has been called before, it's internal buffers remains are returned.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlCopyOutStream.IsActive">
            <summary>
            True while this stream can be used to read copy data from server
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlCopyOutStream.CanRead">
            <summary>
            True
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlCopyOutStream.CanWrite">
            <summary>
            False
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlCopyOutStream.CanSeek">
            <summary>
            False
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlCopyOutStream.Length">
            <summary>
            Number of bytes read so far
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlCopyOutStream.Position">
            <summary>
            Number of bytes read so far; can not be set.
            </summary>
        </member>
        <member name="T:Npgsql.NpgsqlCopySerializer">
            <summary>
            Writes given objects into a stream for PostgreSQL COPY in default copy format (not CSV or BINARY).
            </summary>
        </member>
        <member name="T:Npgsql.NpgsqlRowUpdatedEventHandler">
            <summary>
            Represents the method that handles the <see cref="E:Npgsql.NpgsqlDataAdapter.RowUpdated">RowUpdated</see> events.
            </summary>
            <param name="sender">The source of the event.</param>
            <param name="e">A <see cref="T:NpgsqlRowUpdatedEventArgs">NpgsqlRowUpdatedEventArgs</see> that contains the event data.</param>
        </member>
        <member name="T:Npgsql.NpgsqlRowUpdatingEventHandler">
            <summary>
            Represents the method that handles the <see cref="E:Npgsql.NpgsqlDataAdapter.RowUpdating">RowUpdating</see> events.
            </summary>
            <param name="sender">The source of the event.</param>
            <param name="e">A <see cref="T:NpgsqlRowUpdatingEventArgs">NpgsqlRowUpdatingEventArgs</see> that contains the event data.</param>
        </member>
        <member name="T:Npgsql.NpgsqlDataAdapter">
            <summary>
            This class represents an adapter from many commands: select, update, insert and delete to fill <see cref="T:System.Data.DataSet">Datasets.</see>
            </summary>
        </member>
        <member name="T:Npgsql.NpgsqlDataReader">
            <summary>
            Provides a means of reading a forward-only stream of rows from a PostgreSQL backend.  This class cannot be inherited.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlDataReader.GetDataTypeName(System.Int32)">
            <summary>
            Return the data type name of the column at index <param name="Index"></param>.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlDataReader.GetFieldType(System.Int32)">
            <summary>
            Return the data type of the column at index <param name="Index"></param>.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlDataReader.GetProviderSpecificFieldType(System.Int32)">
            <summary>
            Return the Npgsql specific data type of the column at requested ordinal.
            </summary>
            <param name="ordinal">column position</param>
            <returns>Appropriate Npgsql type for column.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlDataReader.GetName(System.Int32)">
            <summary>
            Return the column name of the column at index <param name="Index"></param>.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlDataReader.GetDataTypeOID(System.Int32)">
            <summary>
            Return the data type OID of the column at index <param name="Index"></param>.
            </summary>
            FIXME: Why this method returns String?
        </member>
        <member name="M:Npgsql.NpgsqlDataReader.GetOrdinal(System.String)">
            <summary>
            Return the column name of the column named <param name="Name"></param>.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlDataReader.GetFieldDbType(System.Int32)">
            <summary>
            Return the data DbType of the column at index <param name="Index"></param>.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlDataReader.GetFieldNpgsqlDbType(System.Int32)">
            <summary>
            Return the data NpgsqlDbType of the column at index <param name="Index"></param>.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlDataReader.GetInterval(System.Int32)">
            <summary>
            Get the value of a column as a <see cref="T:NpgsqlTypes.NpgsqlInterval"/>.
            <remarks>If the differences between <see cref="T:NpgsqlTypes.NpgsqlInterval"/> and <see cref="T:System.TimeSpan"/>
            in handling of days and months is not important to your application, use <see cref="M:Npgsql.NpgsqlDataReader.GetTimeSpan(System.Int32)"/>
            instead.</remarks>
            </summary>
            <param name="i">Index of the field to find.</param>
            <returns><see cref="T:NpgsqlTypes.NpgsqlInterval"/> value of the field.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlDataReader.GetGuid(System.Int32)">
            <summary>
            Gets the value of a column converted to a Guid.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlDataReader.GetInt16(System.Int32)">
            <summary>
            Gets the value of a column as Int16.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlDataReader.GetInt32(System.Int32)">
            <summary>
            Gets the value of a column as Int32.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlDataReader.GetInt64(System.Int32)">
            <summary>
            Gets the value of a column as Int64.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlDataReader.GetFloat(System.Int32)">
            <summary>
            Gets the value of a column as Single.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlDataReader.GetDouble(System.Int32)">
            <summary>
            Gets the value of a column as Double.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlDataReader.GetString(System.Int32)">
            <summary>
            Gets the value of a column as String.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlDataReader.GetDecimal(System.Int32)">
            <summary>
            Gets the value of a column as Decimal.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlDataReader.GetTimeSpan(System.Int32)">
            <summary>
            Gets the value of a column as TimeSpan.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlDataReader.GetValues(System.Object[])">
            <summary>
            Copy values from each column in the current row into <paramref name="values"/>.
            </summary>
            <param name="values">Destination for column values.</param>
            <returns>The number of column values copied.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlDataReader.GetProviderSpecificValues(System.Object[])">
            <summary>
            Copy values from each column in the current row into <paramref name="values"></paramref>.
            </summary>
            <param name="values">An array appropriately sized to store values from all columns.</param>
            <returns>The number of column values copied.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlDataReader.GetBoolean(System.Int32)">
            <summary>
            Gets the value of a column as Boolean.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlDataReader.GetByte(System.Int32)">
            <summary>
            Gets the value of a column as Byte.  Not implemented.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlDataReader.GetChar(System.Int32)">
            <summary>
            Gets the value of a column as Char.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlDataReader.GetDateTime(System.Int32)">
            <summary>
            Gets the value of a column as DateTime.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlDataReader.GetSchemaTable">
            <summary>
            Returns a System.Data.DataTable that describes the column metadata of the DataReader.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlDataReader.GetTableNameFromQuery">
            <summary>
             This methods parses the command text and tries to get the tablename
             from it.
            </summary>
        </member>
        <member name="E:Npgsql.NpgsqlDataReader.ReaderClosed">
            <summary>
            Is raised whenever Close() is called.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlDataReader.FieldCount">
            <summary>
            Gets the number of columns in the current row.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlDataReader.Item(System.Int32)">
            <summary>
            Gets the value of a column in its native format.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlDataReader.Item(System.String)">
            <summary>
            Gets the value of a column in its native format.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlDataReader.Depth">
            <summary>
            Gets a value indicating the depth of nesting for the current row.  Always returns zero.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlDataReader.IsClosed">
            <summary>
            Gets a value indicating whether the data reader is closed.
            </summary>
        </member>
        <member name="F:Npgsql.NpgsqlDataReader.KeyLookup.primaryKey">
            <summary>
            Contains the column names as the keys
            </summary>
        </member>
        <member name="F:Npgsql.NpgsqlDataReader.KeyLookup.uniqueColumns">
            <summary>
            Contains all unique columns
            </summary>
        </member>
        <member name="T:Npgsql.ForwardsOnlyDataReader">
            <summary>
            This is the primary implementation of NpgsqlDataReader. It is the one used in normal cases (where the
            preload-reader option is not set in the connection string to resolve some potential backwards-compatibility
            issues), the only implementation used internally, and in cases where CachingDataReader is used, it is still
            used to do the actual "leg-work" of turning a response stream from the server into a datareader-style
            object - with CachingDataReader then filling it's cache from here.
            </summary>
        </member>
        <member name="M:Npgsql.ForwardsOnlyDataReader.GetNextResponseObject(System.Boolean)">
            <summary>
            Iterate through the objects returned through from the server.
            If it's a CompletedResponse the rowsaffected count is updated appropriately,
            and we iterate again, otherwise we return it (perhaps updating our cache of pending
            rows if appropriate).
            </summary>
            <returns>The next <see cref="T:Npgsql.IServerResponseObject"/> we will deal with.</returns>
        </member>
        <member name="M:Npgsql.ForwardsOnlyDataReader.GetNextRowDescription">
            <summary>
            Advances the data reader to the next result, when multiple result sets were returned by the PostgreSQL backend.
            </summary>
            <returns>True if the reader was advanced, otherwise false.</returns>
        </member>
        <member name="M:Npgsql.ForwardsOnlyDataReader.Dispose(System.Boolean)">
            <summary>
            Releases the resources used by the <see cref="T:Npgsql.NpgsqlCommand">NpgsqlCommand</see>.
            </summary>
        </member>
        <member name="M:Npgsql.ForwardsOnlyDataReader.Close">
            <summary>
            Closes the data reader object.
            </summary>
        </member>
        <member name="M:Npgsql.ForwardsOnlyDataReader.NextResult">
            <summary>
            Advances the data reader to the next result, when multiple result sets were returned by the PostgreSQL backend.
            </summary>
            <returns>True if the reader was advanced, otherwise false.</returns>
        </member>
        <member name="M:Npgsql.ForwardsOnlyDataReader.Read">
            <summary>
            Advances the data reader to the next row.
            </summary>
            <returns>True if the reader was advanced, otherwise false.</returns>
        </member>
        <member name="M:Npgsql.ForwardsOnlyDataReader.GetValue(System.Int32)">
            <summary>
            Return the value of the column at index <param name="Index"></param>.
            </summary>
        </member>
        <member name="M:Npgsql.ForwardsOnlyDataReader.GetBytes(System.Int32,System.Int64,System.Byte[],System.Int32,System.Int32)">
            <summary>
            Gets raw data from a column.
            </summary>
        </member>
        <member name="M:Npgsql.ForwardsOnlyDataReader.GetChars(System.Int32,System.Int64,System.Char[],System.Int32,System.Int32)">
            <summary>
            Gets raw data from a column.
            </summary>
        </member>
        <member name="M:Npgsql.ForwardsOnlyDataReader.IsDBNull(System.Int32)">
            <summary>
            Report whether the value in a column is DBNull.
            </summary>
        </member>
        <member name="P:Npgsql.ForwardsOnlyDataReader.RecordsAffected">
            <summary>
            Gets the number of rows changed, inserted, or deleted by execution of the SQL statement.
            </summary>
        </member>
        <member name="P:Npgsql.ForwardsOnlyDataReader.HasRows">
            <summary>
            Indicates if NpgsqlDatareader has rows to be read.
            </summary>
        </member>
        <member name="T:Npgsql.CachingDataReader">
            <summary>
            <para>Provides an implementation of NpgsqlDataReader in which all data is pre-loaded into memory.
            This operates by first creating a ForwardsOnlyDataReader as usual, and then loading all of it's
            Rows into memory. There is a general principle that when there is a trade-off between a class design that
            is more efficient and/or scalable on the one hand and one that is less efficient but has more functionality
            (in this case the internal-only functionality of caching results) that one can build the less efficent class
            from the most efficient without significant extra loss in efficiency, but not the other way around. The relationship
            between ForwardsOnlyDataReader and CachingDataReader is an example of this).</para>
            <para>Since the interface presented to the user is still forwards-only, queues are used to
            store this information, so that dequeueing as we go we give the garbage collector the best opportunity
            possible to reclaim any memory that is no longer in use.</para>
            <para>ForwardsOnlyDataReader being used to actually
            obtain the information from the server means that the "leg-work" is still only done (and need only be
            maintained) in one place.</para>
            <para>This class exists to allow for certain potential backwards-compatibility issues to be resolved
            with little effort on the part of affected users. It is considerably less efficient than ForwardsOnlyDataReader
            and hence never used internally.</para>
            </summary>
        </member>
        <member name="T:Npgsql.NpgsqlDescribe">
             <summary>
             This is the base class for NpgsqlDescribeStatement and NpgsqlDescribePortal.
             </summary>
            
        </member>
        <member name="T:Npgsql.NpgsqlDescribeStatement">
             <summary>
             This class represents the Statement Describe message sent to PostgreSQL
             server.
             </summary>
            
        </member>
        <member name="T:Npgsql.NpgsqlDescribePortal">
             <summary>
             This class represents the Portal Describe message sent to PostgreSQL
             server.
             </summary>
            
        </member>
        <member name="T:Npgsql.NpgsqlNoticeEventArgs">
            <summary>
            EventArgs class to send Notice parameters, which are just NpgsqlError's in a lighter context.
            </summary>
        </member>
        <member name="F:Npgsql.NpgsqlNoticeEventArgs.Notice">
            <summary>
            Notice information.
            </summary>
        </member>
        <member name="T:Npgsql.NpgsqlError">
            <summary>
            This class represents the ErrorResponse and NoticeResponse
            message sent from PostgreSQL server.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlError.ToString">
            <summary>
            Return a string representation of this error object.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlError.Severity">
            <summary>
            Severity code.  All versions.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlError.Code">
            <summary>
            Error code.  PostgreSQL 7.4 and up.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlError.Message">
            <summary>
            Terse error message.  All versions.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlError.Detail">
            <summary>
            Detailed error message.  PostgreSQL 7.4 and up.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlError.Hint">
            <summary>
            Suggestion to help resolve the error.  PostgreSQL 7.4 and up.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlError.Position">
            <summary>
            Position (one based) within the query string where the error was encounterd.  PostgreSQL 7.4 and up.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlError.InternalPosition">
            <summary>
            Position (one based) within the query string where the error was encounterd.  This position refers to an internal command executed for example inside a PL/pgSQL function. PostgreSQL 7.4 and up.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlError.InternalQuery">
            <summary>
            Internal query string where the error was encounterd.  This position refers to an internal command executed for example inside a PL/pgSQL function. PostgreSQL 7.4 and up.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlError.Where">
            <summary>
            Trace back information.  PostgreSQL 7.4 and up.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlError.File">
            <summary>
            Source file (in backend) reporting the error.  PostgreSQL 7.4 and up.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlError.Line">
            <summary>
            Source file line number (in backend) reporting the error.  PostgreSQL 7.4 and up.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlError.Routine">
            <summary>
            Source routine (in backend) reporting the error.  PostgreSQL 7.4 and up.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlError.SchemaName">
            <summary>
            Schema name which relates to the error. PostgreSQL 9.3 and up.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlError.TableName">
            <summary>
            Table name which relates to the error. PostgreSQL 9.3 and up.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlError.ColumnName">
            <summary>
            Column name which relates to the error. PostgreSQL 9.3 and up.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlError.DataTypeName">
            <summary>
            Data type of column which relates to the error. PostgreSQL 9.3 and up.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlError.ConstraintName">
            <summary>
            Constraint name which relates to the error. PostgreSQL 9.3 and up.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlError.ErrorSql">
            <summary>
            String containing the sql sent which produced this error.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlError.BackendProtocolVersion">
            <summary>
            Backend protocol version in use.
            </summary>
        </member>
        <member name="T:Npgsql.NpgsqlError.ErrorFieldTypeCodes">
            <summary>
            Error and notice message field codes
            </summary>
        </member>
        <member name="F:Npgsql.NpgsqlError.ErrorFieldTypeCodes.Severity">
            <summary>
            Severity: the field contents are ERROR, FATAL, or PANIC (in an error message),
            or WARNING, NOTICE, DEBUG, INFO, or LOG (in a notice message), or a localized
            translation of one of these. Always present.
            </summary>
        </member>
        <member name="F:Npgsql.NpgsqlError.ErrorFieldTypeCodes.Code">
            <summary>
            Code: the SQLSTATE code for the error (see Appendix A). Not localizable. Always present.
            </summary>
        </member>
        <member name="F:Npgsql.NpgsqlError.ErrorFieldTypeCodes.Message">
            <summary>
            Message: the primary human-readable error message. This should be accurate
            but terse (typically one line). Always present.
            </summary>
        </member>
        <member name="F:Npgsql.NpgsqlError.ErrorFieldTypeCodes.Detail">
            <summary>
            Detail: an optional secondary error message carrying more detail about the problem.
            Might run to multiple lines.
            </summary>
        </member>
        <member name="F:Npgsql.NpgsqlError.ErrorFieldTypeCodes.Hint">
            <summary>
            Hint: an optional suggestion what to do about the problem. This is intended to differ
            from Detail in that it offers advice (potentially inappropriate) rather than hard facts.
            Might run to multiple lines.
            </summary>
        </member>
        <member name="F:Npgsql.NpgsqlError.ErrorFieldTypeCodes.Position">
            <summary>
            Position: the field value is a decimal ASCII integer, indicating an error cursor
            position as an index into the original query string. The first character has index 1,
            and positions are measured in characters not bytes.
            </summary>
        </member>
        <member name="F:Npgsql.NpgsqlError.ErrorFieldTypeCodes.InternalPosition">
            <summary>
            Internal position: this is defined the same as the P field, but it is used when the
            cursor position refers to an internally generated command rather than the one submitted
            by the client.
            The q field will always appear when this field appears.
            </summary>
        </member>
        <member name="F:Npgsql.NpgsqlError.ErrorFieldTypeCodes.InternalQuery">
            <summary>
            Internal query: the text of a failed internally-generated command.
            This could be, for example, a SQL query issued by a PL/pgSQL function.
            </summary>
        </member>
        <member name="F:Npgsql.NpgsqlError.ErrorFieldTypeCodes.Where">
            <summary>
            Where: an indication of the context in which the error occurred.
            Presently this includes a call stack traceback of active procedural language functions
            and internally-generated queries. The trace is one entry per line, most recent first.
            </summary>
        </member>
        <member name="F:Npgsql.NpgsqlError.ErrorFieldTypeCodes.SchemaName">
            <summary>
            Schema name: if the error was associated with a specific database object,
            the name of the schema containing that object, if any.
            </summary>
        </member>
        <member name="F:Npgsql.NpgsqlError.ErrorFieldTypeCodes.TableName">
            <summary>
            Table name: if the error was associated with a specific table, the name of the table.
            (Refer to the schema name field for the name of the table's schema.)
            </summary>
        </member>
        <member name="F:Npgsql.NpgsqlError.ErrorFieldTypeCodes.ColumnName">
            <summary>
            Column name: if the error was associated with a specific table column, the name of the column.
            (Refer to the schema and table name fields to identify the table.)
            </summary>
        </member>
        <member name="F:Npgsql.NpgsqlError.ErrorFieldTypeCodes.DataTypeName">
            <summary>
            Data type name: if the error was associated with a specific data type, the name of the data type.
            (Refer to the schema name field for the name of the data type's schema.)
            </summary>
        </member>
        <member name="F:Npgsql.NpgsqlError.ErrorFieldTypeCodes.ConstraintName">
            <summary>
            Constraint name: if the error was associated with a specific constraint, the name of the constraint.
            Refer to fields listed above for the associated table or domain.
            (For this purpose, indexes are treated as constraints, even if they weren't created with constraint syntax.)
            </summary>
        </member>
        <member name="F:Npgsql.NpgsqlError.ErrorFieldTypeCodes.File">
            <summary>
            File: the file name of the source-code location where the error was reported.
            </summary>
        </member>
        <member name="F:Npgsql.NpgsqlError.ErrorFieldTypeCodes.Line">
            <summary>
            Line: the line number of the source-code location where the error was reported.
            </summary>
        </member>
        <member name="F:Npgsql.NpgsqlError.ErrorFieldTypeCodes.Routine">
            <summary>
            Routine: the name of the source-code routine reporting the error.
            </summary>
        </member>
        <member name="T:Npgsql.LogLevel">
            <summary>
            The level of verbosity of the NpgsqlEventLog
            </summary>
        </member>
        <member name="F:Npgsql.LogLevel.None">
            <summary>
            Don't log at all
            </summary>
        </member>
        <member name="F:Npgsql.LogLevel.Normal">
            <summary>
            Only log the most common issues
            </summary>
        </member>
        <member name="F:Npgsql.LogLevel.Debug">
            <summary>
            Log everything
            </summary>
        </member>
        <member name="T:Npgsql.NpgsqlEventLog">
            <summary>
            This class handles all the Npgsql event and debug logging
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlEventLog.LogMsg(System.String,Npgsql.LogLevel)">
            <summary>
            Writes a string to the Npgsql event log if msglevel is bigger then <see cref="P:Npgsql.NpgsqlEventLog.Level">NpgsqlEventLog.Level</see>
            </summary>
            <remarks>
            This method is obsolete and should no longer be used.
            It is likely to be removed in future versions of Npgsql
            </remarks>
            <param name="message">The message to write to the event log</param>
            <param name="msglevel">The minimum <see cref="T:Npgsql.LogLevel">LogLevel</see> for which this message should be logged.</param>
        </member>
        <member name="M:Npgsql.NpgsqlEventLog.LogMsg(System.Resources.ResourceManager,System.String,Npgsql.LogLevel,System.Object[])">
            <summary>
            Writes a string to the Npgsql event log if msglevel is bigger then <see cref="P:Npgsql.NpgsqlEventLog.Level">NpgsqlEventLog.Level</see>
            </summary>
            <param name="resman">The <see cref="T:System.Resources.ResourceManager">ResourceManager</see> to get the localized resources</param>
            <param name="ResourceString">The name of the resource that should be fetched by the <see cref="T:System.Resources.ResourceManager">ResourceManager</see></param>
            <param name="msglevel">The minimum <see cref="T:Npgsql.LogLevel">LogLevel</see> for which this message should be logged.</param>
            <param name="Parameters">The additional parameters that shall be included into the log-message (must be compatible with the string in the resource):</param>
        </member>
        <member name="M:Npgsql.NpgsqlEventLog.LogIndexerGet(Npgsql.LogLevel,System.String,System.Object)">
            <summary>
            Writes the default log-message for the action of calling the Get-part of an Indexer to the log file.
            </summary>
            <param name="msglevel">The minimum <see cref="T:Npgsql.LogLevel">LogLevel</see> for which this message should be logged.</param>
            <param name="ClassName">The name of the class that contains the Indexer</param>
            <param name="IndexerParam">The parameter given to the Indexer</param>
        </member>
        <member name="M:Npgsql.NpgsqlEventLog.LogIndexerSet(Npgsql.LogLevel,System.String,System.Object,System.Object)">
            <summary>
            Writes the default log-message for the action of calling the Set-part of an Indexer to the logfile.
            </summary>
            <param name="msglevel">The minimum <see cref="T:Npgsql.LogLevel">LogLevel</see> for which this message should be logged.</param>
            <param name="ClassName">The name of the class that contains the Indexer</param>
            <param name="IndexerParam">The parameter given to the Indexer</param>
            <param name="value">The value the Indexer is set to</param>
        </member>
        <member name="M:Npgsql.NpgsqlEventLog.LogPropertyGet(Npgsql.LogLevel,System.String,System.String)">
            <summary>
            Writes the default log-message for the action of calling the Get-part of a Property to the logfile.
            </summary>
            <param name="msglevel">The minimum <see cref="T:Npgsql.LogLevel">LogLevel</see> for which this message should be logged.</param>
            <param name="ClassName">The name of the class that contains the Property</param>
            <param name="PropertyName">The name of the Property</param>
        </member>
        <member name="M:Npgsql.NpgsqlEventLog.LogPropertySet(Npgsql.LogLevel,System.String,System.String,System.Object)">
            <summary>
            Writes the default log-message for the action of calling the Set-part of a Property to the logfile.
            </summary>
            <param name="msglevel">The minimum <see cref="T:Npgsql.LogLevel">LogLevel</see> for which this message should be logged.</param>
            <param name="ClassName">The name of the class that contains the Property</param>
            <param name="PropertyName">The name of the Property</param>
            <param name="value">The value the Property is set to</param>
        </member>
        <member name="M:Npgsql.NpgsqlEventLog.LogMethodEnter(Npgsql.LogLevel,System.String,System.String)">
            <summary>
            Writes the default log-message for the action of calling a Method without Arguments to the logfile.
            </summary>
            <param name="msglevel">The minimum <see cref="T:Npgsql.LogLevel">LogLevel</see> for which this message should be logged.</param>
            <param name="ClassName">The name of the class that contains the Method</param>
            <param name="MethodName">The name of the Method</param>
        </member>
        <member name="M:Npgsql.NpgsqlEventLog.LogMethodEnter(Npgsql.LogLevel,System.String,System.String,System.Object)">
            <summary>
            Writes the default log-message for the action of calling a Method with one Argument to the logfile.
            </summary>
            <param name="msglevel">The minimum <see cref="T:Npgsql.LogLevel">LogLevel</see> for which this message should be logged.</param>
            <param name="ClassName">The name of the class that contains the Method</param>
            <param name="MethodName">The name of the Method</param>
            <param name="MethodParameter">The value of the Argument of the Method</param>
        </member>
        <member name="M:Npgsql.NpgsqlEventLog.LogMethodEnter(Npgsql.LogLevel,System.String,System.String,System.Object,System.Object)">
            <summary>
            Writes the default log-message for the action of calling a Method with two Arguments to the logfile.
            </summary>
            <param name="msglevel">The minimum <see cref="T:Npgsql.LogLevel">LogLevel</see> for which this message should be logged.</param>
            <param name="ClassName">The name of the class that contains the Method</param>
            <param name="MethodName">The name of the Method</param>
            <param name="MethodParameter1">The value of the first Argument of the Method</param>
            <param name="MethodParameter2">The value of the second Argument of the Method</param>
        </member>
        <member name="M:Npgsql.NpgsqlEventLog.LogMethodEnter(Npgsql.LogLevel,System.String,System.String,System.Object,System.Object,System.Object)">
            <summary>
            Writes the default log-message for the action of calling a Method with three Arguments to the logfile.
            </summary>
            <param name="msglevel">The minimum <see cref="T:Npgsql.LogLevel">LogLevel</see> for which this message should be logged.</param>
            <param name="ClassName">The name of the class that contains the Method</param>
            <param name="MethodName">The name of the Method</param>
            <param name="MethodParameter1">The value of the first Argument of the Method</param>
            <param name="MethodParameter2">The value of the second Argument of the Method</param>
            <param name="MethodParameter3">The value of the third Argument of the Method</param>
        </member>
        <member name="M:Npgsql.NpgsqlEventLog.LogMethodEnter(Npgsql.LogLevel,System.String,System.String,System.Object[])">
            <summary>
            Writes the default log-message for the action of calling a Method with more than three Arguments to the logfile.
            </summary>
            <param name="msglevel">The minimum <see cref="T:Npgsql.LogLevel">LogLevel</see> for which this message should be logged.</param>
            <param name="ClassName">The name of the class that contains the Method</param>
            <param name="MethodName">The name of the Method</param>
            <param name="MethodParameters">A <see cref="T:System.Object">Object</see>-Array with zero or more Ojects that are Arguments of the Method.</param>
        </member>
        <member name="P:Npgsql.NpgsqlEventLog.Level">
            <summary>
             Sets/Returns the level of information to log to the logfile.
             </summary>
             <value>The current <see cref="T:Npgsql.LogLevel">LogLevel</see></value>
        </member>
        <member name="P:Npgsql.NpgsqlEventLog.LogName">
            <summary>
             Sets/Returns the filename to use for logging.
             </summary>
             <value>The filename of the current Log file.</value>
        </member>
        <member name="P:Npgsql.NpgsqlEventLog.EchoMessages">
            <summary>
             Sets/Returns whether Log messages should be echoed to the console
             </summary>
             <value><b>true</b> if Log messages are echoed to the console, otherwise <b>false</b></value>
        </member>
        <member name="T:Npgsql.NpgsqlException">
            <summary>
            The exception that is thrown when the PostgreSQL backend reports errors.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlException.#ctor(System.Collections.IList)">
            <summary>
            Construct a backend error exception based on a list of one or more
            backend errors.  The basic Exception.Message will be built from the
            first (usually the only) error in the list.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlException.ToString">
            <summary>
            Format a .NET style exception string.
            Include all errors in the list, including any hints.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlException.AppendString(System.IO.StringWriter,System.String,System.String)">
            <summary>
            Append a line to the given Stream, first checking for zero-length.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlException.Item(System.Int32)">
            <summary>
            Provide access to the entire list of errors provided by the PostgreSQL backend.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlException.Severity">
            <summary>
            Severity code.  All versions.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlException.Code">
            <summary>
            Error code.  PostgreSQL 7.4 and up.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlException.BaseMessage">
            <summary>
            Basic error message.  All versions.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlException.Detail">
            <summary>
            Detailed error message.  PostgreSQL 7.4 and up.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlException.Hint">
            <summary>
            Suggestion to help resolve the error.  PostgreSQL 7.4 and up.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlException.Position">
            <summary>
            Position (one based) within the query string where the error was encounterd.  PostgreSQL 7.4 and up.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlException.Where">
            <summary>
            Trace back information.  PostgreSQL 7.4 and up.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlException.File">
            <summary>
            Source file (in backend) reporting the error.  PostgreSQL 7.4 and up.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlException.Line">
            <summary>
            Source file line number (in backend) reporting the error.  PostgreSQL 7.4 and up.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlException.Routine">
            <summary>
            Source routine (in backend) reporting the error.  PostgreSQL 7.4 and up.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlException.SchemaName">
            <summary>
            Schema name which relates to the error. PostgreSQL 9.3 and up.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlException.TableName">
            <summary>
            Table name which relates to the error. PostgreSQL 9.3 and up.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlException.ColumnName">
            <summary>
            Column name which relates to the error. PostgreSQL 9.3 and up.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlException.DataTypeName">
            <summary>
            Data type of column which relates to the error. PostgreSQL 9.3 and up.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlException.ConstraintName">
            <summary>
            Constraint name which relates to the error. PostgreSQL 9.3 and up.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlException.ErrorSql">
            <summary>
            String containing the sql sent which produced this error.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlException.Errors">
            <summary>
            Returns the entire list of errors provided by the PostgreSQL backend.
            </summary>
        </member>
        <member name="T:Npgsql.NpgsqlExecute">
             <summary>
             This class represents the Execute message sent to PostgreSQL
             server.
             </summary>
            
        </member>
        <member name="T:Npgsql.NpgsqlFactory">
            <summary>
            A factory to create instances of various Npgsql objects.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlFactory.CreateCommand">
            <summary>
            Creates an NpgsqlCommand object.
            </summary>
        </member>
        <member name="T:Npgsql.NpgsqlFlush">
             <summary>
             This class represents the Flush message sent to PostgreSQL
             server.
             </summary>
            
        </member>
        <member name="T:Npgsql.SimpleClientMessage">
            <summary>
            For classes representing simple messages,
            consisting only of a message code and length identifier,
            sent from the client to the server.
            </summary>
        </member>
        <member name="T:Npgsql.NpgsqlMediator">
            <summary>
             This class is responsible for serving as bridge between the backend
             protocol handling and the core classes. It is used as the mediator for
             exchanging data generated/sent from/to backend.
             </summary>
            
        </member>
        <member name="T:Npgsql.NpgsqlNotificationEventArgs">
            <summary>
            EventArgs class to send Notification parameters.
            </summary>
        </member>
        <member name="F:Npgsql.NpgsqlNotificationEventArgs.PID">
            <summary>
            Process ID of the PostgreSQL backend that sent this notification.
            </summary>
        </member>
        <member name="F:Npgsql.NpgsqlNotificationEventArgs.Condition">
            <summary>
            Condition that triggered that notification.
            </summary>
        </member>
        <member name="F:Npgsql.NpgsqlNotificationEventArgs.AdditionalInformation">
            <summary>
            Additional Information From Notifiying Process (for future use, currently postgres always sets this to an empty string)
            </summary>
        </member>
        <member name="T:Npgsql.NpgsqlParameter">
            <summary>
             This class represents a parameter to a command that will be sent to server
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlParameter.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> class.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlParameter.#ctor(System.String,System.Object)">
            <summary>
            Initializes a new instance of the <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see>
            class with the parameter m_Name and a value of the new <b>NpgsqlParameter</b>.
            </summary>
            <param m_Name="parameterName">The m_Name of the parameter to map.</param>
            <param m_Name="value">An <see cref="T:System.Object">Object</see> that is the value of the <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see>.</param>
            <remarks>
            <p>When you specify an <see cref="T:System.Object">Object</see>
            in the value parameter, the <see cref="T:System.Data.DbType">DbType</see> is
            inferred from the .NET Framework type of the <b>Object</b>.</p>
            <p>When using this constructor, you must be aware of a possible misuse of the constructor which takes a DbType parameter.
            This happens when calling this constructor passing an int 0 and the compiler thinks you are passing a value of DbType.
            Use <code> Convert.ToInt32(value) </code> for example to have compiler calling the correct constructor.</p>
            </remarks>
        </member>
        <member name="M:Npgsql.NpgsqlParameter.#ctor(System.String,NpgsqlTypes.NpgsqlDbType)">
            <summary>
            Initializes a new instance of the <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see>
            class with the parameter m_Name and the data type.
            </summary>
            <param m_Name="parameterName">The m_Name of the parameter to map.</param>
            <param m_Name="parameterType">One of the <see cref="T:System.Data.DbType">DbType</see> values.</param>
        </member>
        <member name="M:Npgsql.NpgsqlParameter.#ctor(System.String,NpgsqlTypes.NpgsqlDbType,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see>
            class with the parameter m_Name, the <see cref="T:System.Data.DbType">DbType</see>, and the size.
            </summary>
            <param m_Name="parameterName">The m_Name of the parameter to map.</param>
            <param m_Name="parameterType">One of the <see cref="T:System.Data.DbType">DbType</see> values.</param>
            <param m_Name="size">The length of the parameter.</param>
        </member>
        <member name="M:Npgsql.NpgsqlParameter.#ctor(System.String,NpgsqlTypes.NpgsqlDbType,System.Int32,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see>
            class with the parameter m_Name, the <see cref="T:System.Data.DbType">DbType</see>, the size,
            and the source column m_Name.
            </summary>
            <param m_Name="parameterName">The m_Name of the parameter to map.</param>
            <param m_Name="parameterType">One of the <see cref="T:System.Data.DbType">DbType</see> values.</param>
            <param m_Name="size">The length of the parameter.</param>
            <param m_Name="sourceColumn">The m_Name of the source column.</param>
        </member>
        <member name="M:Npgsql.NpgsqlParameter.#ctor(System.String,NpgsqlTypes.NpgsqlDbType,System.Int32,System.String,System.Data.ParameterDirection,System.Boolean,System.Byte,System.Byte,System.Data.DataRowVersion,System.Object)">
            <summary>
            Initializes a new instance of the <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see>
            class with the parameter m_Name, the <see cref="T:System.Data.DbType">DbType</see>, the size,
            the source column m_Name, a <see cref="T:System.Data.ParameterDirection">ParameterDirection</see>,
            the precision of the parameter, the scale of the parameter, a
            <see cref="T:System.Data.DataRowVersion">DataRowVersion</see> to use, and the
            value of the parameter.
            </summary>
            <param m_Name="parameterName">The m_Name of the parameter to map.</param>
            <param m_Name="parameterType">One of the <see cref="T:System.Data.DbType">DbType</see> values.</param>
            <param m_Name="size">The length of the parameter.</param>
            <param m_Name="sourceColumn">The m_Name of the source column.</param>
            <param m_Name="direction">One of the <see cref="T:System.Data.ParameterDirection">ParameterDirection</see> values.</param>
            <param m_Name="isNullable"><b>true</b> if the value of the field can be null, otherwise <b>false</b>.</param>
            <param m_Name="precision">The total number of digits to the left and right of the decimal point to which
            <see cref="P:Npgsql.NpgsqlParameter.Value">Value</see> is resolved.</param>
            <param m_Name="scale">The total number of decimal places to which
            <see cref="P:Npgsql.NpgsqlParameter.Value">Value</see> is resolved.</param>
            <param m_Name="sourceVersion">One of the <see cref="T:System.Data.DataRowVersion">DataRowVersion</see> values.</param>
            <param m_Name="value">An <see cref="T:System.Object">Object</see> that is the value
            of the <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see>.</param>
        </member>
        <member name="M:Npgsql.NpgsqlParameter.Clone">
            <summary>
            Creates a new <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> that
            is a copy of the current instance.
            </summary>
            <returns>A new <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> that is a copy of this instance.</returns>
        </member>
        <member name="P:Npgsql.NpgsqlParameter.Collection">
            <summary>
            The collection to which this parameter belongs, if any.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlParameter.Precision">
            <summary>
            Gets or sets the maximum number of digits used to represent the
            <see cref="P:Npgsql.NpgsqlParameter.Value">Value</see> property.
            </summary>
            <value>The maximum number of digits used to represent the
            <see cref="P:Npgsql.NpgsqlParameter.Value">Value</see> property.
            The default value is 0, which indicates that the data provider
            sets the precision for <b>Value</b>.</value>
        </member>
        <member name="P:Npgsql.NpgsqlParameter.Scale">
            <summary>
            Gets or sets the number of decimal places to which
            <see cref="P:Npgsql.NpgsqlParameter.Value">Value</see> is resolved.
            </summary>
            <value>The number of decimal places to which
            <see cref="P:Npgsql.NpgsqlParameter.Value">Value</see> is resolved. The default is 0.</value>
        </member>
        <member name="P:Npgsql.NpgsqlParameter.Size">
            <summary>
            Gets or sets the maximum size, in bytes, of the data within the column.
            </summary>
            <value>The maximum size, in bytes, of the data within the column.
            The default value is inferred from the parameter value.</value>
        </member>
        <member name="P:Npgsql.NpgsqlParameter.DbType">
            <summary>
            Gets or sets the <see cref="T:System.Data.DbType">DbType</see> of the parameter.
            </summary>
            <value>One of the <see cref="T:System.Data.DbType">DbType</see> values. The default is <b>String</b>.</value>
        </member>
        <member name="P:Npgsql.NpgsqlParameter.NpgsqlDbType">
            <summary>
            Gets or sets the <see cref="T:System.Data.DbType">DbType</see> of the parameter.
            </summary>
            <value>One of the <see cref="T:System.Data.DbType">DbType</see> values. The default is <b>String</b>.</value>
        </member>
        <member name="P:Npgsql.NpgsqlParameter.Direction">
            <summary>
            Gets or sets a value indicating whether the parameter is input-only,
            output-only, bidirectional, or a stored procedure return value parameter.
            </summary>
            <value>One of the <see cref="T:System.Data.ParameterDirection">ParameterDirection</see>
            values. The default is <b>Input</b>.</value>
        </member>
        <member name="P:Npgsql.NpgsqlParameter.IsNullable">
            <summary>
            Gets or sets a value indicating whether the parameter accepts null values.
            </summary>
            <value><b>true</b> if null values are accepted; otherwise, <b>false</b>. The default is <b>false</b>.</value>
        </member>
        <member name="P:Npgsql.NpgsqlParameter.ParameterName">
            <summary>
            Gets or sets the m_Name of the <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see>.
            </summary>
            <value>The m_Name of the <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see>.
            The default is an empty string.</value>
        </member>
        <member name="P:Npgsql.NpgsqlParameter.CleanName">
            <summary>
            The m_Name scrubbed of any optional marker
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlParameter.SourceColumn">
            <summary>
            Gets or sets the m_Name of the source column that is mapped to the
            <see cref="T:System.Data.DataSet">DataSet</see> and used for loading or
            returning the <see cref="P:Npgsql.NpgsqlParameter.Value">Value</see>.
            </summary>
            <value>The m_Name of the source column that is mapped to the
            <see cref="T:System.Data.DataSet">DataSet</see>. The default is an empty string.</value>
        </member>
        <member name="P:Npgsql.NpgsqlParameter.SourceVersion">
            <summary>
            Gets or sets the <see cref="T:System.Data.DataRowVersion">DataRowVersion</see>
            to use when loading <see cref="P:Npgsql.NpgsqlParameter.Value">Value</see>.
            </summary>
            <value>One of the <see cref="T:System.Data.DataRowVersion">DataRowVersion</see> values.
            The default is <b>Current</b>.</value>
        </member>
        <member name="P:Npgsql.NpgsqlParameter.Value">
            <summary>
            Gets or sets the value of the parameter.
            </summary>
            <value>An <see cref="T:System.Object">Object</see> that is the value of the parameter.
            The default value is null.</value>
        </member>
        <member name="P:Npgsql.NpgsqlParameter.NpgsqlValue">
            <summary>
            Gets or sets the value of the parameter.
            </summary>
            <value>An <see cref="T:System.Object">Object</see> that is the value of the parameter.
            The default value is null.</value>
        </member>
        <member name="T:Npgsql.NpgsqlParameterCollection">
            <summary>
            Represents a collection of parameters relevant to a <see cref="T:Npgsql.NpgsqlCommand">NpgsqlCommand</see>
            as well as their respective mappings to columns in a <see cref="T:System.Data.DataSet">DataSet</see>.
            This class cannot be inherited.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlParameterCollection.#ctor">
            <summary>
            Initializes a new instance of the NpgsqlParameterCollection class.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlParameterCollection.InvalidateHashLookups">
            <summary>
            Invalidate the hash lookup tables.  This should be done any time a change
            may throw the lookups out of sync with the list.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlParameterCollection.Add(Npgsql.NpgsqlParameter)">
            <summary>
            Adds the specified <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> object to the <see cref="T:Npgsql.NpgsqlParameterCollection">NpgsqlParameterCollection</see>.
            </summary>
            <param name="value">The <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> to add to the collection.</param>
            <returns>The index of the new <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> object.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlParameterCollection.Add(System.String,System.Object)">
            <summary>
            Obsolete. Use AddWithValue instead.
            </summary>
            <remarks>
            Use caution when using this overload of the
            <b>Add</b> method to specify integer parameter values.
            Because this overload takes a <i>value</i> of type Object,
            you must convert the integral value to an <b>Object</b>
            type when the value is zero, as the following C# example demonstrates.
            <code>parameters.Add(":pname", Convert.ToInt32(0));</code>
            If you do not perform this conversion, the compiler will assume you
            are attempting to call the NpgsqlParameterCollection.Add(string, DbType) overload.
            </remarks>
        </member>
        <member name="M:Npgsql.NpgsqlParameterCollection.AddWithValue(System.String,System.Object)">
            <summary>
            Adds a <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> to the <see cref="T:Npgsql.NpgsqlParameterCollection">NpgsqlParameterCollection</see> given the specified parameter name and value.
            </summary>
            <param name="parameterName">The name of the <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see>.</param>
            <param name="value">The Value of the <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> to add to the collection.</param>
            <returns>The paramater that was added.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlParameterCollection.AddWithValue(System.String,NpgsqlTypes.NpgsqlDbType,System.Object)">
            <summary>
            Adds a <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> to the <see cref="T:Npgsql.NpgsqlParameterCollection">NpgsqlParameterCollection</see> given the specified parameter name and value.
            </summary>
            <param name="parameterName">The name of the <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see>.</param>
            <param name="value">The Value of the <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> to add to the collection.</param>
            <param name="parameterType">One of the NpgsqlDbType values.</param>
            <returns>The paramater that was added.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlParameterCollection.AddWithValue(System.String,NpgsqlTypes.NpgsqlDbType,System.Int32,System.Object)">
            <summary>
            Adds a <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> to the <see cref="T:Npgsql.NpgsqlParameterCollection">NpgsqlParameterCollection</see> given the specified parameter name and value.
            </summary>
            <param name="parameterName">The name of the <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see>.</param>
            <param name="value">The Value of the <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> to add to the collection.</param>
            <param name="parameterType">One of the NpgsqlDbType values.</param>
            <param name="size">The length of the column.</param>
            <returns>The paramater that was added.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlParameterCollection.AddWithValue(System.String,NpgsqlTypes.NpgsqlDbType,System.Int32,System.String,System.Object)">
            <summary>
            Adds a <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> to the <see cref="T:Npgsql.NpgsqlParameterCollection">NpgsqlParameterCollection</see> given the specified parameter name and value.
            </summary>
            <param name="parameterName">The name of the <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see>.</param>
            <param name="value">The Value of the <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> to add to the collection.</param>
            <param name="parameterType">One of the NpgsqlDbType values.</param>
            <param name="size">The length of the column.</param>
            <param name="sourceColumn">The name of the source column.</param>
            <returns>The paramater that was added.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlParameterCollection.Add(System.String,NpgsqlTypes.NpgsqlDbType)">
            <summary>
            Adds a <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> to the <see cref="T:Npgsql.NpgsqlParameterCollection">NpgsqlParameterCollection</see> given the parameter name and the data type.
            </summary>
            <param name="parameterName">The name of the parameter.</param>
            <param name="parameterType">One of the DbType values.</param>
            <returns>The index of the new <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> object.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlParameterCollection.Add(System.String,NpgsqlTypes.NpgsqlDbType,System.Int32)">
            <summary>
            Adds a <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> to the <see cref="T:Npgsql.NpgsqlParameterCollection">NpgsqlParameterCollection</see> with the parameter name, the data type, and the column length.
            </summary>
            <param name="parameterName">The name of the parameter.</param>
            <param name="parameterType">One of the DbType values.</param>
            <param name="size">The length of the column.</param>
            <returns>The index of the new <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> object.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlParameterCollection.Add(System.String,NpgsqlTypes.NpgsqlDbType,System.Int32,System.String)">
            <summary>
            Adds a <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> to the <see cref="T:Npgsql.NpgsqlParameterCollection">NpgsqlParameterCollection</see> with the parameter name, the data type, the column length, and the source column name.
            </summary>
            <param name="parameterName">The name of the parameter.</param>
            <param name="parameterType">One of the DbType values.</param>
            <param name="size">The length of the column.</param>
            <param name="sourceColumn">The name of the source column.</param>
            <returns>The index of the new <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> object.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlParameterCollection.RemoveAt(System.String)">
            <summary>
            Removes the specified <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> from the collection using the parameter name.
            </summary>
            <param name="parameterName">The name of the <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> object to retrieve.</param>
        </member>
        <member name="M:Npgsql.NpgsqlParameterCollection.Contains(System.String)">
            <summary>
            Gets a value indicating whether a <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> with the specified parameter name exists in the collection.
            </summary>
            <param name="parameterName">The name of the <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> object to find.</param>
            <returns><b>true</b> if the collection contains the parameter; otherwise, <b>false</b>.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlParameterCollection.IndexOf(System.String)">
            <summary>
            Gets the location of the <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> in the collection with a specific parameter name.
            </summary>
            <param name="parameterName">The name of the <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> object to find.</param>
            <returns>The zero-based location of the <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> in the collection.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlParameterCollection.RemoveAt(System.Int32)">
            <summary>
            Removes the specified <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> from the collection using a specific index.
            </summary>
            <param name="index">The zero-based index of the parameter.</param>
        </member>
        <member name="M:Npgsql.NpgsqlParameterCollection.Insert(System.Int32,System.Object)">
            <summary>
            Inserts a <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> into the collection at the specified index.
            </summary>
            <param name="index">The zero-based index where the parameter is to be inserted within the collection.</param>
            <param name="oValue">The <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> to add to the collection.</param>
        </member>
        <member name="M:Npgsql.NpgsqlParameterCollection.Remove(System.String)">
            <summary>
            Removes the specified <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> from the collection.
            </summary>
            <param name="parameterName">The name of the <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> to remove from the collection.</param>
        </member>
        <member name="M:Npgsql.NpgsqlParameterCollection.Remove(System.Object)">
            <summary>
            Removes the specified <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> from the collection.
            </summary>
            <param name="oValue">The <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> to remove from the collection.</param>
        </member>
        <member name="M:Npgsql.NpgsqlParameterCollection.Contains(System.Object)">
            <summary>
            Gets a value indicating whether a <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> exists in the collection.
            </summary>
            <param name="value">The value of the <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> object to find.</param>
            <returns>true if the collection contains the <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> object; otherwise, false.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlParameterCollection.TryGetValue(System.String,Npgsql.NpgsqlParameter@)">
            <summary>
            Gets a value indicating whether a <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> with the specified parameter name exists in the collection.
            </summary>
            <param name="parameterName">The name of the <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> object to find.</param>
            <param name="parameter">A reference to the requested parameter is returned in this out param if it is found in the list.  This value is null if the parameter is not found.</param>
            <returns><b>true</b> if the collection contains the parameter and param will contain the parameter; otherwise, <b>false</b>.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlParameterCollection.Clear">
            <summary>
            Removes all items from the collection.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlParameterCollection.IndexOf(System.Object)">
            <summary>
            Gets the location of a <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> in the collection.
            </summary>
            <param name="value">The value of the <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> object to find.</param>
            <returns>The zero-based index of the <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> object in the collection.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlParameterCollection.Add(System.Object)">
            <summary>
            Adds the specified <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> object to the <see cref="T:Npgsql.NpgsqlParameterCollection">NpgsqlParameterCollection</see>.
            </summary>
            <param name="value">The <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> to add to the collection.</param>
            <returns>The zero-based index of the new <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> object.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlParameterCollection.CopyTo(System.Array,System.Int32)">
            <summary>
            Copies <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> objects from the <see cref="T:Npgsql.NpgsqlParameterCollection">NpgsqlParameterCollection</see> to the specified array.
            </summary>
            <param name="array">An <see cref="T:System.Array">Array</see> to which to copy the <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> objects in the collection.</param>
            <param name="index">The starting index of the array.</param>
        </member>
        <member name="M:Npgsql.NpgsqlParameterCollection.GetEnumerator">
            <summary>
            Returns an enumerator that can iterate through the collection.
            </summary>
            <returns>An <see cref="T:System.Collections.IEnumerator">IEnumerator</see> that can be used to iterate through the collection.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlParameterCollection.CheckType(System.Object)">
            <summary>
            In methods taking an object as argument this method is used to verify
            that the argument has the type <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see>
            </summary>
            <param name="Object">The object to verify</param>
        </member>
        <member name="P:Npgsql.NpgsqlParameterCollection.Item(System.String)">
            <summary>
            Gets the <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> with the specified name.
            </summary>
            <param name="parameterName">The name of the <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> to retrieve.</param>
            <value>The <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> with the specified name, or a null reference if the parameter is not found.</value>
        </member>
        <member name="P:Npgsql.NpgsqlParameterCollection.Item(System.Int32)">
            <summary>
            Gets the <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> at the specified index.
            </summary>
            <param name="index">The zero-based index of the <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> to retrieve.</param>
            <value>The <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> at the specified index.</value>
        </member>
        <member name="P:Npgsql.NpgsqlParameterCollection.Count">
            <summary>
            Gets the number of <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> objects in the collection.
            </summary>
            <value>The number of <see cref="T:Npgsql.NpgsqlParameter">NpgsqlParameter</see> objects in the collection.</value>
        </member>
        <member name="T:Npgsql.NpgsqlParameterStatus">
             <summary>
             This class represents the ParameterStatus message sent from PostgreSQL
             server.
             </summary>
            
        </member>
        <member name="T:Npgsql.NpgsqlParse">
             <summary>
             This class represents the Parse message sent to PostgreSQL
             server.
             </summary>
            
        </member>
        <member name="T:Npgsql.NpgsqlPasswordPacket">
            <summary>
            This class represents a PasswordPacket message sent to backend
            PostgreSQL.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlPromotableSinglePhaseNotification.Prepare">
            <summary>
            Used when a connection is closed
            </summary>
        </member>
        <member name="T:Npgsql.NpgsqlQuery">
            <summary>
            Summary description for NpgsqlQuery
            </summary>
        </member>
        <member name="T:Npgsql.NpgsqlRow">
            <summary>
            This is the abstract base class for NpgsqlAsciiRow and NpgsqlBinaryRow.
            </summary>
        </member>
        <member name="T:Npgsql.NpgsqlRowDescription">
             <summary>
             This class represents a RowDescription message sent from
             the PostgreSQL.
             </summary>
            
        </member>
        <member name="T:Npgsql.NpgsqlRowDescription.FieldData">
            <summary>
            This struct represents the internal data of the RowDescription message.
            </summary>
        </member>
        <member name="T:Npgsql.NpgsqlSchema">
            <summary>
            Provides the underlying mechanism for reading schema information.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlSchema.GetMetaDataCollections">
            <summary>
            Returns the MetaDataCollections that lists all possible collections.
            </summary>
            <returns>The MetaDataCollections</returns>
        </member>
        <member name="M:Npgsql.NpgsqlSchema.GetRestrictions">
            <summary>
            Returns the Restrictions that contains the meaning and position of the values in the restrictions array.
            </summary>
            <returns>The Restrictions</returns>
        </member>
        <member name="M:Npgsql.NpgsqlSchema.GetDatabases(Npgsql.NpgsqlConnection,System.String[])">
            <summary>
            Returns the Databases that contains a list of all accessable databases.
            </summary>
            <param name="conn">The database connection on which to run the metadataquery.</param>
            <param name="restrictions">The restrictions to filter the collection.</param>
            <returns>The Databases</returns>
        </member>
        <member name="M:Npgsql.NpgsqlSchema.GetTables(Npgsql.NpgsqlConnection,System.String[])">
            <summary>
            Returns the Tables that contains table and view names and the database and schema they come from.
            </summary>
            <param name="conn">The database connection on which to run the metadataquery.</param>
            <param name="restrictions">The restrictions to filter the collection.</param>
            <returns>The Tables</returns>
        </member>
        <member name="M:Npgsql.NpgsqlSchema.GetColumns(Npgsql.NpgsqlConnection,System.String[])">
            <summary>
            Returns the Columns that contains information about columns in tables.
            </summary>
            <param name="conn">The database connection on which to run the metadataquery.</param>
            <param name="restrictions">The restrictions to filter the collection.</param>
            <returns>The Columns.</returns>
        </member>
        <member name="M:Npgsql.NpgsqlSchema.GetViews(Npgsql.NpgsqlConnection,System.String[])">
            <summary>
            Returns the Views that contains view names and the database and schema they come from.
            </summary>
            <param name="conn">The database connection on which to run the metadataquery.</param>
            <param name="restrictions">The restrictions to filter the collection.</param>
            <returns>The Views</returns>
        </member>
        <member name="M:Npgsql.NpgsqlSchema.GetUsers(Npgsql.NpgsqlConnection,System.String[])">
            <summary>
            Returns the Users containing user names and the sysid of those users.
            </summary>
            <param name="conn">The database connection on which to run the metadataquery.</param>
            <param name="restrictions">The restrictions to filter the collection.</param>
            <returns>The Users.</returns>
        </member>
        <member name="T:Npgsql.NpgsqlStartupPacket">
             <summary>
             This class represents a StartupPacket message of PostgreSQL
             protocol.
             </summary>
            
        </member>
        <member name="T:Npgsql.CompletedResponse">
            <summary>
            Represents a completed response message.
            </summary>
        </member>
        <member name="T:Npgsql.NpgsqlSync">
             <summary>
             This class represents the Sync message sent to PostgreSQL
             server.
             </summary>
            
        </member>
        <member name="T:Npgsql.NpgsqlTransaction">
            <summary>
            Represents a transaction to be made in a PostgreSQL database. This class cannot be inherited.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlTransaction.Commit">
            <summary>
            Commits the database transaction.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlTransaction.Rollback">
            <summary>
            Rolls back a transaction from a pending state.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlTransaction.Rollback(System.String)">
            <summary>
            Rolls back a transaction from a pending savepoint state.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlTransaction.Save(System.String)">
            <summary>
            Creates a transaction save point.
            </summary>
        </member>
        <member name="M:Npgsql.NpgsqlTransaction.Cancel">
            <summary>
            Cancel the transaction without telling the backend about it.  This is
            used to make the transaction go away when closing a connection.
            </summary>
        </member>
        <member name="P:Npgsql.NpgsqlTransaction.Connection">
            <summary>
            Gets the <see cref="T:Npgsql.NpgsqlConnection">NpgsqlConnection</see>
            object associated with the transaction, or a null reference if the
            transaction is no longer valid.
            </summary>
            <value>The <see cref="T:Npgsql.NpgsqlConnection">NpgsqlConnection</see>
            object associated with the transaction.</value>
        </member>
        <member name="P:Npgsql.NpgsqlTransaction.IsolationLevel">
            <summary>
            Specifies the <see cref="T:System.Data.IsolationLevel">IsolationLevel</see> for this transaction.
            </summary>
            <value>The <see cref="T:System.Data.IsolationLevel">IsolationLevel</see> for this transaction.
            The default is <b>ReadCommitted</b>.</value>
        </member>
        <member name="T:Npgsql.PGUtil">
            <summary>
             This class provides many util methods to handle
             reading and writing of PostgreSQL protocol messages.
             </summary>
        </member>
        <member name="M:Npgsql.PGUtil.ConvertProtocolVersion(Npgsql.ProtocolVersion)">
            <summary>
             This method takes a ProtocolVersion and returns an integer
             version number that the Postgres backend will recognize in a
             startup packet.
             </summary>
        </member>
        <member name="M:Npgsql.PGUtil.ExtractServerVersion(System.String)">
            <summary>
            This method takes a version string as returned by SELECT VERSION() and returns
            a valid version string ("7.2.2" for example).
            This is only needed when running protocol version 2.
            This does not do any validity checks.
            </summary>
        </member>
        <member name="M:Npgsql.PGUtil.ReadString(System.IO.Stream)">
            <summary>
             This method gets a C NULL terminated string from the network stream.
             It keeps reading a byte in each time until a NULL byte is returned.
             It returns the resultant string of bytes read.
             This string is sent from backend.
             </summary>
        </member>
        <member name="M:Npgsql.PGUtil.ReadBytes(System.IO.Stream,System.Byte[],System.Int32,System.Int32)">
            <summary>
            Reads requested number of bytes from stream with retries until Stream.Read returns 0 or count is reached.
            </summary>
            <param name="stream">Stream to read</param>
            <param name="buffer">byte buffer to fill</param>
            <param name="offset">starting position to fill the buffer</param>
            <param name="count">number of bytes to read</param>
            <returns>The number of bytes read.  May be less than count if no more bytes are available.</returns>
        </member>
        <member name="M:Npgsql.PGUtil.ReadBytes(System.Byte[],System.Int32,System.Int32,System.Boolean)">
            <summary>
            Reads requested number of bytes from <paramref name="src"/>.  If output matches <paramref name="src"/> exactly, and <paramref name="forceCopy"/> == false, <paramref name="src"/> is returned directly.
            </summary>
            <param name="src">Source array.</param>
            <param name="offset">Starting position to read from <paramref name="src"/></param>
            <param name="count">Number of bytes to read</param>
            <param name="forceCopy">Force a copy, even if the output is an exact copy of <paramref name="src"/>.</param>
            <returns>byte[] containing data requested.</returns>
        </member>
        <member name="M:Npgsql.PGUtil.WriteString(System.IO.Stream,System.String)">
            <summary>
             This method writes a string to the network stream.
             </summary>
        </member>
        <member name="M:Npgsql.PGUtil.WriteString(System.IO.Stream,System.String,System.Object[])">
            <summary>
             This method writes a string to the network stream.
             </summary>
        </member>
        <member name="M:Npgsql.PGUtil.WriteStringNullTerminated(System.IO.Stream,System.String)">
            <summary>
             This method writes a C NULL terminated string to the network stream.
             It appends a NULL terminator to the end of the String.
             </summary>
        </member>
        <member name="M:Npgsql.PGUtil.WriteStringNullTerminated(System.IO.Stream,System.String,System.Object[])">
            <summary>
             This method writes a C NULL terminated string to the network stream.
             It appends a NULL terminator to the end of the String.
             </summary>
        </member>
        <member name="M:Npgsql.PGUtil.WriteBytes(System.IO.Stream,System.Byte)">
            <summary>
            This method writes a byte to the stream. It also enables logging of them.
            </summary>
        </member>
        <member name="M:Npgsql.PGUtil.WriteBytesNullTerminated(System.IO.Stream,System.Byte)">
            <summary>
            This method writes a byte to the stream. It also enables logging of them.
            </summary>
        </member>
        <member name="M:Npgsql.PGUtil.WriteBytes(System.IO.Stream,System.Byte[])">
            <summary>
            This method writes a set of bytes to the stream. It also enables logging of them.
            </summary>
        </member>
        <member name="M:Npgsql.PGUtil.WriteBytesNullTerminated(System.IO.Stream,System.Byte[])">
            <summary>
            This method writes a set of bytes to the stream. It also enables logging of them.
            </summary>
        </member>
        <member name="M:Npgsql.PGUtil.WriteLimString(System.IO.Stream,System.String,System.Int32)">
            <summary>
             This method writes a C NULL terminated string limited in length to the
             backend server.
             It pads the string with null bytes to the size specified.
             </summary>
        </member>
        <member name="M:Npgsql.PGUtil.WriteLimBytes(System.IO.Stream,System.Byte[],System.Int32)">
            <summary>
             This method writes a C NULL terminated byte[] limited in length to the
             backend server.
             It pads the string with null bytes to the size specified.
             </summary>
        </member>
        <member name="M:Npgsql.PGUtil.WriteInt32(System.IO.Stream,System.Int32)">
            <summary>
            Write a 32-bit integer to the given stream in the correct byte order.
            </summary>
        </member>
        <member name="M:Npgsql.PGUtil.ReadInt32(System.IO.Stream)">
            <summary>
            Read a 32-bit integer from the given stream in the correct byte order.
            </summary>
        </member>
        <member name="M:Npgsql.PGUtil.ReadInt32(System.Byte[],System.Int32)">
            <summary>
            Read a 32-bit integer from the given array in the correct byte order.
            </summary>
        </member>
        <member name="M:Npgsql.PGUtil.WriteInt16(System.IO.Stream,System.Int16)">
            <summary>
            Write a 16-bit integer to the given stream in the correct byte order.
            </summary>
        </member>
        <member name="M:Npgsql.PGUtil.ReadInt16(System.IO.Stream)">
            <summary>
            Read a 16-bit integer from the given stream in the correct byte order.
            </summary>
        </member>
        <member name="M:Npgsql.PGUtil.ReadInt16(System.Byte[],System.Int32)">
            <summary>
            Read a 16-bit integer from the given array in the correct byte order.
            </summary>
        </member>
        <member name="M:Npgsql.PGUtil.HostNetworkByteOrderSwap(System.Byte[],System.Boolean)">
            <summary>
            Copy and possibly reverse a byte array, depending on host architecture endienness.
            </summary>
            <param name="src">Source byte array.</param>
            <param name="forceCopy">Force a copy even if no swap is performed.</param>
            <returns><paramref name="src"/>, reversed if on a little-endian architecture, copied if required.</returns>
        </member>
        <member name="M:Npgsql.PGUtil.HostNetworkByteOrderSwap(System.Byte[],System.Int32,System.Int32,System.Boolean)">
            <summary>
            Copy and possibly reverse a byte array, depending on host architecture endienness.
            </summary>
            <param name="src">Source byte array.</param>
            <param name="start">Starting offset in source array.</param>
            <param name="length">Number of bytes to copy.</param>
            <param name="forceCopy">Force a copy even if no swap is performed.</param>
            <returns><paramref name="src"/>, reversed if on a little-endian architecture, copied if required.</returns>
        </member>
        <member name="T:Npgsql.ProtocolVersion">
            <summary>
            Represent the frontend/backend protocol version.
            </summary>
        </member>
        <member name="T:Npgsql.ServerVersion">
             <summary>
             Represent the backend server version.
             As this class offers no functionality beyond that offered by <see cref="T:System.Version"/> it has been
             deprecated in favour of that class.
             </summary>
            
        </member>
        <member name="M:Npgsql.ServerVersion.ToString">
            <summary>
            Returns the string representation of this version in three place dot notation (Major.Minor.Patch).
            </summary>
        </member>
        <member name="P:Npgsql.ServerVersion.Major">
            <summary>
            Server version major number.
            </summary>
        </member>
        <member name="P:Npgsql.ServerVersion.Minor">
            <summary>
            Server version minor number.
            </summary>
        </member>
        <member name="P:Npgsql.ServerVersion.Patch">
            <summary>
            Server version patch level number.
            </summary>
        </member>
        <member name="T:Npgsql.SSPIHandler">
            <summary>
            A class to handle everything associated with SSPI authentication
            </summary>
        </member>
        <member name="T:Npgsql.SSPIHandler.SecBufferDesc">
            <summary>
            Simplified SecBufferDesc struct with only one SecBuffer
            </summary>
        </member>
    </members>
</doc>
