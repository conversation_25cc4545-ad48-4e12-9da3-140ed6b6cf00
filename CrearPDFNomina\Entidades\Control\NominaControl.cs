﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Entidades.Control
{
    public class NominaControl
    {
        private DateTime _dFechaNomina;
        private DateTime _dFechaUtilidades;
        private DateTime _dFechaAguinaldo;
        private int _iGeneraUtilidades;
        private int _iGeneraAguinaldo; 

        public NominaControl()
        {
            _dFechaNomina = Convert.ToDateTime("1900-01-01");
            _dFechaUtilidades = Convert.ToDateTime("1900-01-01");
            _dFechaAguinaldo = Convert.ToDateTime("1900-01-01");
            _iGeneraUtilidades = 0;
            _iGeneraAguinaldo = 0; 
        }

        public DateTime dFechaNomina
        {
            get
            {
                return _dFechaNomina;
            }
            set
            {
                _dFechaNomina = value;
            }
        }

        public DateTime dFechaUtilidades
        {
            get
            {
                return _dFechaUtilidades;
            }
            set
            {
                _dFechaUtilidades = value;
            }
        }

        public DateTime dFechaAguinaldo
        {
            get
            {
                return _dFechaAguinaldo;
            }
            set
            {
                _dFechaAguinaldo = value;
            }
        }

        public int iGeneraUtilidades
        {
            get
            {
                return _iGeneraUtilidades;
            }
            set
            {
                _iGeneraUtilidades = value;
            }
        }

        public int iGeneraAguinaldo
        {
            get
            {
                return _iGeneraAguinaldo;
            }
            set
            {
                _iGeneraAguinaldo = value;
            }
        }
    }
}
