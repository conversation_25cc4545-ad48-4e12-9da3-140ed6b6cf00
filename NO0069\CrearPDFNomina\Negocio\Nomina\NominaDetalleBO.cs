﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Datos.Nomina;
using Entidades.Nomina;
using Datos.Errores;

namespace Negocios.Nomina
{
    public class NominaDetalleBO
    {
        public List<NominaDetalleEmpleado> obtenerNominaDetalleEmpleado(int iNumeroEmpleado, DateTime dFechaNomina, int iEmpresa)
        {
            NominaDetalleDAL NominaDetalle = new NominaDetalleDAL();
            try
            {
                return NominaDetalle.consultarNominaDetalle(iNumeroEmpleado, dFechaNomina, iEmpresa);
            }
            catch (Exception ex)
            {
                GrabarErrorDAL Error = new GrabarErrorDAL();
                Error.grabarError("Negocios.Nomina", "NominaDetallesBO.cs", "obtenerEdoFondoDetalleEmpleados", "Error al obtener el detalle de nomina del empleado", 5, ex.Message.ToString());
                throw new Exception(ex.Message.ToString());
            }
        }
    }
}
