/*
	NOMBRE: <PERSON><PERSON><PERSON>#eda 93114338
	BD: Administracion PostgreSQL
	FECHA: 08/08/2016
	SERVIDOR PRUEBAS: ************
	SERVIDOR PRODUCCION: **********
	DESCRIPCION: Tabla donde se almacenaran los pdf para envio de correo de talones de nomina, estados de cuenta de fondo y gasolina
	MODULO:
	RUTA:
*/
CREATE TABLE mov_talonesempleadosmail
(
	num_empleado INTEGER NOT NULL DEFAULT 0,
	nom_empleado CHARACTER VARYING(100) NOT NULL DEFAULT ''::CHARACTER VARYING,
	fec_nomina DATE NOT NULL DEFAULT '1900-01-01'::DATE,
	des_correo CHARACTER VARYING(100) NOT NULL DEFAULT ''::CHARACTER VARYING,
	des_archivopdf BYTEA NOT NULL DEFAULT ''::BYTEA,
	clv_tipo INTEGER NOT NULL DEFAULT 0
);

GRANT ALL ON TABLE mov_talonesempleadosmail TO sysgenexus;