/*
	NOMBRE: <PERSON><PERSON> At<PERSON>o
	BD: Administracion PostgreSQL
	FECHA: 30/11/2017
	SERVIDOR PRUEBAS: ************
	SERVIDOR PRODUCCION: **********
	DESCRIPCION: Tabla historica que contiene la informacion ordenada para el envio de correos nomina y gasolina.
	MODULO: NO0069
	RUTA: svn://************/sysx/administracion/contabilidad/nominacoppel/NO0069/CrearPDFNomina/Scripts
*/

CREATE TABLE his_talonesempleadosmail
(
  num_empleado integer NOT NULL DEFAULT 0,
  nom_empleado character varying(100) NOT NULL DEFAULT ''::character varying,
  fec_nomina date NOT NULL DEFAULT '1900-01-01'::date,
  des_correo character varying(100) NOT NULL DEFAULT ''::character varying,
  des_archivopdf bytea NOT NULL DEFAULT ''::bytea,
  clv_tipo integer NOT NULL DEFAULT 0,
  clv_empresa integer NOT NULL DEFAULT 0,
  clv_bloque integer NOT NULL DEFAULT 0,
  clv_estatus integer NOT NULL DEFAULT 0,
  fec_alta timestamp without time zone NOT NULL DEFAULT now(),
  fec_envio timestamp without time zone NOT NULL DEFAULT '1900-01-01 00:00:00'::timestamp without time zone
);

GRANT ALL ON TABLE cat_emailempleados TO sysgenexus;

CREATE INDEX ix_his_talonesempleadosmail_consulta
  ON his_talonesempleadosmail
  USING btree
  (num_empleado , fec_nomina , clv_tipo , clv_empresa , clv_bloque );