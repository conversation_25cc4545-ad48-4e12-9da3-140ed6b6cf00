CREATE OR REPLACE FUNCTION fun_verifica_generacion_talones_correo(integer)
  RETURNS integer AS
$BODY$
DECLARE
	/*
		NOMBRE: <PERSON><PERSON><PERSON>#eda 93114338
		BD: Administracion PostgreSQL
		FECHA: 08/08/2016
		SERVIDOR PRUEBAS: ************
		SERVIDOR PRODUCCION: **********
		DESCRIPCION: Se encarga de verificar si ya fueron enviados los estados de cuenta del fondo y de gasolina.
		MODULO:
		RUTA:
	*/

	iTipo ALIAS FOR $1;
	iValor INTEGER;
	dFecha DATE;
BEGIN
	IF EXISTS(SELECT DISTINCT fec_nomina FROM mov_talonesempleadosmail WHERE clv_tipo = iTipo LIMIT 1) THEN 
		iValor := 1;
	ELSE
		iValor := 0;
	END IF;

	RETURN iValor;
END;
$BODY$
  LANGUAGE plpgsql VOLATILE SECURITY DEFINER;
  
GRANT EXECUTE ON FUNCTION fun_verifica_generacion_talones_correo(integer) TO sysgenexus;
