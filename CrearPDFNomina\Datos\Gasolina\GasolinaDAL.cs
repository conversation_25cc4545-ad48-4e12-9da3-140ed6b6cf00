﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.IO;
using Npgsql;
using NpgsqlTypes;
using Entidades.Gasolina;
using Datos.Errores;

namespace Datos.Gasolina
{
    public class GasolinaDAL : Conexion.ConexionDAL
    {
        public List<EstadoGasolina> consultarEdoGasolina(DateTime dFechaGeneralesFondo)
        {
            NpgsqlCommand cmd = new NpgsqlCommand();
            NpgsqlDataReader lector;
            cmd.Connection = nConexion;
            cmd.CommandTimeout = 4000;

            List<EstadoGasolina> listaEmpleadosGas = new List<EstadoGasolina>();

            try
            {
                // sFecha = dFechaNomina.ToString("yyyy-MM-dd"); 
                cmd.CommandText = "SELECT num_empleado, des_correo FROM fun_obtiene_correos_empleados(2);";
                conectarBaseDatos(3); //Administracion
                lector = cmd.ExecuteReader();

                while (lector.Read())
                {
                    EstadoGasolina Gasolina = new EstadoGasolina();
                    Gasolina.iNumEmp = int.Parse(lector["num_empleado"].ToString());
                    Gasolina.sCorreoEmpleado = lector["des_correo"].ToString();
                    listaEmpleadosGas.Add(Gasolina);
                }
            }
            catch (Exception ex)
            {
                GrabarErrorDAL Error = new GrabarErrorDAL();
                Error.grabarError("Datos.Gasolina", "GasolinaDAL.cs", "consultarEdoGasolina", "Error al obtener correos empleados", 12, ex.Message.ToString());
                throw new Exception(ex.Message.ToString());
            }
            finally
            {
                cerrarConexion();
            }

            return listaEmpleadosGas;
        }
    }
}
