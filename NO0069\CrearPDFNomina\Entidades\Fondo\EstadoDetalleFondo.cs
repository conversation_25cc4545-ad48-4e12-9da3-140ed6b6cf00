﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Entidades.Fondo
{
    public class EstadoDetalleFondo
    {
        private int _iClave;
        private string _sDescripcion;
        private decimal _iImporte;

        public EstadoDetalleFondo()
        {
            _iClave = 0;
            _sDescripcion = string.Empty;
            _iImporte = 0;
        }

        public int iClave
        {
            get
            {
                return _iClave;
            }
            set
            {
                _iClave = value;
            }
        }
        public string sDescripcion
        {
            get
            {
                return _sDescripcion;
            }
            set
            {
                _sDescripcion = value;
            }
        }
        public decimal iImporte
        {
            get
            {
                return _iImporte;
            }
            set
            {
                _iImporte = value;
            }
        }
    }
}
