﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.IO;
using Npgsql;
using NpgsqlTypes;
using Entidades.Nomina;
using Datos.Errores;

namespace Datos.Nomina
{
    public class NominaDAL : Conexion.ConexionDAL
    {
        public List<NominaEmpleado> consultarNomina(DateTime dFechaNomina)
        {
            NpgsqlCommand cmd = new NpgsqlCommand();
            NpgsqlDataReader lector;
            cmd.Connection = nConexion;
            cmd.CommandTimeout = 24000;

            List<NominaEmpleado> listaNomina = new List<NominaEmpleado>();

            try
            {
                // sFecha = dFechaNomina.ToString("yyyy-MM-dd"); 
                cmd.CommandText = "SELECT iempleado,capellidopaterno,capellidomaterno,cnombre,icentro,cdesccentro,inumciudad,cdescciudad,cnumafiliacion,ccurp,crfc,dfechafinal,dfechainicio,ctarjetabanco,cdescrutapago,ctarjetadespensa,mtotaldespensa, mtotalapagar,mtotalingresos, mtotalegresos, inumeroempresa, cnombreempresa, ccorreoempleado, cnombreempleadocorregido, crfcempleadocorregido FROM fun_consultanominaempleado_01( 0,@dFechaNomina,2);";
                cmd.Parameters.AddWithValue("@dFechaNomina", NpgsqlDbType.Date, dFechaNomina);

                conectarBaseDatos(3); //Administracion
                lector = cmd.ExecuteReader();

                while (lector.Read())
                {
                    NominaEmpleado Nomina = new NominaEmpleado();
                    Nomina.iNumEmp = int.Parse(lector["iempleado"].ToString());
                    Nomina.sApellidoPaterno = lector["capellidopaterno"].ToString();
                    Nomina.sApellidoMaterno = lector["capellidomaterno"].ToString();
                    Nomina.sNombre = lector["cnombre"].ToString();
                    Nomina.iCentro = int.Parse(lector["icentro"].ToString());
                    Nomina.sDescCentro = lector["cdesccentro"].ToString();
                    Nomina.iNumeroCiudad = int.Parse(lector["inumciudad"].ToString());
                    Nomina.sDescCiudad = lector["cdescciudad"].ToString();
                    Nomina.sNumAfiliacion = lector["cnumafiliacion"].ToString();
                    Nomina.sCURP = lector["ccurp"].ToString();
                    Nomina.sRFC = lector["crfc"].ToString();
                    Nomina.dFechaFinal = Convert.ToDateTime(lector["dfechafinal"]);
                    Nomina.dFechaInicio = Convert.ToDateTime(lector["dfechainicio"]);
                    Nomina.sTarjetaBanco = lector["ctarjetabanco"].ToString();
                    Nomina.sDescRutaPago = lector["cdescrutapago"].ToString();
                    Nomina.sTarjetaDespensa = lector["ctarjetadespensa"].ToString();
                    Nomina.dTotalDespensa = lector["mtotaldespensa"].ToString();
                    Nomina.dTotalAPagar = lector["mtotalapagar"].ToString();
                    Nomina.dTotalIngresos = lector["mtotalingresos"].ToString();
                    Nomina.dTotalEgresos = lector["mtotalegresos"].ToString();
                    Nomina.iNumeroEmpresa = int.Parse(lector["inumeroempresa"].ToString());
                    Nomina.sNombreEmpresa = lector["cnombreempresa"].ToString();
                    Nomina.sCorreoEmpleado = lector["ccorreoempleado"].ToString();
                    Nomina.cNombreEmpleadoCorregido = lector["cnombreempleadocorregido"].ToString();
                    Nomina.cRfcEmpleadoCorregido = lector["crfcempleadocorregido"].ToString();

                    listaNomina.Add(Nomina);
                }
            }
            catch (Exception ex)
            {
                GrabarErrorDAL Error = new GrabarErrorDAL();
                Error.grabarError("Datos.Nomina", "NominaDAL.cs", "consultarNomina", "Error al consultar la Nomina", 6, ex.Message.ToString());
                throw new Exception(ex.Message.ToString());
            }
            finally
            {
                cerrarConexion();
            }

            return listaNomina;
        }

        public int guardarTalonNomina(int iTipo, int iNumeroEmpleado, string sApellidoPaterno, string ApellidoMaterno, string sNombre,
            byte[] sPDF64, DateTime dFechaNomina, string sCorreoEmpleado, int iEmpresa)
        {

            /*If you were executing a command, increase the CommandTimeout value in ConnectionString or in your NpgsqlCommand object.*/
            NpgsqlCommand cmd = new NpgsqlCommand();
            NpgsqlDataReader lector;
            cmd.Connection = nConexion;
            cmd.CommandTimeout = 24000;
            int iRegresa = 0;

            try
            {
                cmd.CommandText = "SELECT fun_generartalonesempleados AS iRegresa FROM fun_generartalonesempleados(@iTipo,@iNumEmp,@cApellidoPaterno,@cApellidoMaterno,@cNombre,@bPDF,@dFechaNomina,@cCorreoEmpleado,@iEmpresa);";
                cmd.Parameters.AddWithValue("@iTipo", NpgsqlDbType.Integer, iTipo);
                cmd.Parameters.AddWithValue("@iNumEmp", NpgsqlDbType.Integer, iNumeroEmpleado);
                cmd.Parameters.AddWithValue("@cApellidoPaterno", NpgsqlDbType.Char, 15, sApellidoPaterno);
                cmd.Parameters.AddWithValue("@cApellidoMaterno", NpgsqlDbType.Char, 15, ApellidoMaterno);
                cmd.Parameters.AddWithValue("@cNombre", NpgsqlDbType.Char, 20, sNombre);
                cmd.Parameters.AddWithValue("@bPDF", NpgsqlDbType.Bytea, sPDF64);
                cmd.Parameters.AddWithValue("@dFechaNomina", NpgsqlDbType.Date, dFechaNomina);
                cmd.Parameters.AddWithValue("@cCorreoEmpleado", NpgsqlDbType.Char, 100, sCorreoEmpleado);
                cmd.Parameters.AddWithValue("@iEmpresa", NpgsqlDbType.Integer, iEmpresa);

                conectarBaseDatos(3); //Administracion
                lector = cmd.ExecuteReader();

                if (lector.Read())
                {
                    iRegresa = Convert.ToInt16(lector["iRegresa"]);
                }
            }
            catch (Exception ex)
            {
                GrabarErrorDAL Error = new GrabarErrorDAL();
                Error.grabarError("Datos.Nomina", "NominaDAL.cs", "guardarTalonNomina", "Error al guardar Talon de Nomina", 7, ex.Message.ToString());
                throw new Exception(ex.Message.ToString());
            }
            finally
            {
                cerrarConexion();
            }

            return iRegresa;
        }

        public bool depurarTalonNomina(int iTipo)
        {
            bool bContinuar = false; 
            NpgsqlCommand cmd = new NpgsqlCommand();

            try
            {
                conectarBaseDatos(3); //Administracion
                cmd.CommandText = "delete from mov_talonesempleadosmail where clv_tipo = @iclv_tipo";
                cmd.Parameters.AddWithValue("@iclv_tipo", NpgsqlDbType.Integer, iTipo);
                cmd.Connection = nConexion;

                cmd.ExecuteNonQuery();
                bContinuar = true;
            }
            catch (Exception ex)
            {
                GrabarErrorDAL Error = new GrabarErrorDAL();
                Error.grabarError("Datos.Nomina", "NominaDAL.cs", "depurarTalonNomina", "Error al eliminar informacion de la tabla mov_talonesempleadosmail", 8, ex.Message.ToString());
                throw new Exception(ex.Message.ToString());
            }
            finally
            {
                cerrarConexion();
            }

            return bContinuar;
        }
    }
}
