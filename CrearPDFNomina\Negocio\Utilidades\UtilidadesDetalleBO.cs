﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Entidades.Nomina;
using Datos.Nomina;
using Datos.Errores;

namespace Negocios.Utilidades
{
    public class UtilidadesDetalleBO
    {
        public List<NominaDetalleEmpleado> obtenerNominaDetalleEmpleado(int iNumeroEmpleado, DateTime dFechaNomina, int iEmpresa)
        {
            NominaDetalleDAL NominaDetalle = new NominaDetalleDAL();
            try
            {
                return NominaDetalle.consultarNominaDetalle(iNumeroEmpleado, dFechaNomina, iEmpresa);
            }
            catch (Exception ex)
            {
                GrabarErrorDAL Error = new GrabarErrorDAL();
                Error.grabarError("Negocios.Utilidades", "UtilidadesDetallesBO.cs", "obtenerEdoFondoDetalleEmpleados", "Error al obtener el detalle de las utilidades del empleado", 41, ex.Message.ToString());
                throw new Exception(ex.Message.ToString());
            }
        }
    }
}
